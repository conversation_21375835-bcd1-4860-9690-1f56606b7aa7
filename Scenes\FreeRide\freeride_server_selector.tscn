[gd_scene load_steps=29 format=3 uid="uid://c3a5ov1p6xfw5"]

[ext_resource type="Script" path="res://Scenes/FreeRide/freeride_server_selector.gd" id="1_8vst2"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="2_r6dei"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="3_fdod6"]
[ext_resource type="Theme" uid="uid://cavx3qitdfs8s" path="res://Scenes/ui/ui_theme.tres" id="3_xrosu"]
[ext_resource type="StyleBox" uid="uid://bblxlot03sp7l" path="res://Scenes/ui/row_panel_bg.tres" id="5_2jg7b"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="5_qfuhk"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="6_26rx1"]
[ext_resource type="Texture2D" uid="uid://cdi8kfb3vhhfs" path="res://Scenes/ui/assets/button-back.png" id="7_bjwoi"]
[ext_resource type="Texture2D" uid="uid://cl0clegom55cf" path="res://Scenes/ui/assets/reload.png" id="7_vwtaj"]
[ext_resource type="Texture2D" uid="uid://cw0ou8f3ia7b4" path="res://Scenes/ui/assets/gray.png" id="8_ssyhp"]
[ext_resource type="StyleBox" uid="uid://3jmm6a0rtg6b" path="res://Scenes/ui/panel_background.tres" id="10_tdci0"]
[ext_resource type="Texture2D" uid="uid://bw3w61kw7wpki" path="res://Scenes/ui/assets/PNG/grey_crossWhite.png" id="11_tmd7g"]
[ext_resource type="PackedScene" uid="uid://cixw0i4rjg3rx" path="res://Scenes/ui/ExitableControl.tscn" id="11_wl6qr"]
[ext_resource type="PackedScene" uid="uid://ye4yb2j4xayq" path="res://Scenes/ui/chat/AndroidLineEdit.tscn" id="13_swh22"]
[ext_resource type="PackedScene" uid="uid://0n4sboaofbt1" path="res://Scenes/ui/ServerFilterRow.tscn" id="15_nrcgi"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_xclg8"]
texture = ExtResource("2_r6dei")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_87yml"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nq3wa"]
bg_color = Color(0.855709, 1, 0.314511, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.611765, 0.611765, 0.611765, 1)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_u2aag"]
bg_color = Color(0.113725, 0.113725, 0.113725, 0.615686)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5sl8o"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_spn1u"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_iwr70"]
bg_color = Color(0, 0, 0, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.235294, 0.235294, 0.235294, 1)
corner_radius_top_left = 12
corner_radius_top_right = 12
corner_radius_bottom_right = 12
corner_radius_bottom_left = 12

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7c1b5"]
bg_color = Color(0.117647, 0.117647, 0.117647, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qf4be"]
bg_color = Color(0.117647, 0.117647, 0.117647, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_kecyl"]
texture = ExtResource("2_r6dei")

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_7qk0g"]
texture = ExtResource("2_r6dei")

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_16l7n"]
bg_color = Color(0.541176, 0.654902, 0.152941, 1)
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_prhe3"]
texture = ExtResource("2_r6dei")

[node name="FreeRideServerSelector" type="Control"]
layout_direction = 2
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_8vst2")
metadata/_edit_lock_ = true

[node name="BGPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -13.0
offset_right = 22.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_xclg8")
metadata/_edit_lock_ = true

[node name="Title" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -596.0
offset_top = 20.0
offset_right = 596.0
offset_bottom = 104.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 40
text = "SELECT_SERVER"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Scroll" type="ScrollContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 63.0
offset_top = 151.0
offset_right = -53.0
offset_bottom = -28.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("3_xrosu")
theme_override_styles/panel = SubResource("StyleBoxEmpty_87yml")
horizontal_scroll_mode = 0
scroll_deadzone = 60

[node name="VBoxContainer" type="VBoxContainer" parent="Scroll"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 10

[node name="RaceMode" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 52.0
offset_top = -102.0
offset_right = -45.0
offset_bottom = -8.0
grow_horizontal = 2
grow_vertical = 0
theme_override_styles/panel = ExtResource("5_2jg7b")

[node name="RankLabel" type="Label" parent="RaceMode"]
layout_mode = 1
anchors_preset = 9
anchor_bottom = 1.0
offset_left = 17.0
offset_top = 1.0
offset_right = 214.0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 30
text = "RACE_MODE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="JoinButton" parent="RaceMode" instance=ExtResource("6_26rx1")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = -222.0
offset_top = -31.0
offset_right = -25.0
offset_bottom = 27.0
grow_horizontal = 0
pivot_offset = Vector2(98.5, 29)

[node name="Panel" type="Panel" parent="RaceMode/JoinButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_nq3wa")

[node name="Join" type="Label" parent="RaceMode/JoinButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 30
text = "JOIN"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RestartButton" parent="." instance=ExtResource("6_26rx1")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -120.0
offset_top = 24.0
offset_right = -38.0
offset_bottom = 97.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(41, 36.5)

[node name="NinePatchRect" type="NinePatchRect" parent="RestartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("8_ssyhp")
patch_margin_left = 29
patch_margin_top = 23
patch_margin_right = 27
patch_margin_bottom = 29

[node name="TextureRect" type="TextureRect" parent="RestartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -3.0
offset_bottom = -3.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.55, 0.55)
pivot_offset = Vector2(41, 36)
mouse_filter = 2
texture = ExtResource("7_vwtaj")
expand_mode = 1
stretch_mode = 5

[node name="PasswordParent" parent="." instance=ExtResource("11_wl6qr")]
visible = false
layout_mode = 1

[node name="BG" type="Panel" parent="PasswordParent"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_u2aag")

[node name="Panel" type="Panel" parent="PasswordParent"]
custom_minimum_size = Vector2(600, 400)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2
focus_mode = 2
theme_override_styles/panel = ExtResource("10_tdci0")

[node name="Title" type="Label" parent="PasswordParent/Panel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -31.5
offset_right = 31.5
offset_bottom = 48.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 30
text = "PASSWORD"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CancelButton" parent="PasswordParent/Panel" instance=ExtResource("6_26rx1")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -165.0
offset_right = -15.0
offset_bottom = 50.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(75, 25)

[node name="TextureRect2" type="TextureRect" parent="PasswordParent/Panel/CancelButton"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -12.5
offset_top = -13.0
offset_right = 12.5
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("11_tmd7g")
expand_mode = 1
stretch_mode = 5

[node name="LineEdit" parent="PasswordParent/Panel" instance=ExtResource("13_swh22")]
z_index = 3
custom_minimum_size = Vector2(455, 66)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -227.5
offset_top = -33.0
offset_right = 227.5
offset_bottom = 33.0
theme_override_colors/selection_color = Color(0.301961, 0.301961, 0.301961, 1)
theme_override_colors/caret_color = Color(1, 1, 1, 1)
theme_override_colors/font_placeholder_color = Color(0.235294, 0.235294, 0.235294, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_colors/font_selected_color = Color(0.921569, 0.921569, 0.921569, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 6
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 25
theme_override_styles/focus = SubResource("StyleBoxEmpty_5sl8o")
theme_override_styles/normal = SubResource("StyleBoxEmpty_spn1u")
placeholder_text = "PASSWORD"
alignment = 1

[node name="Panel" type="Panel" parent="PasswordParent/Panel/LineEdit"]
show_behind_parent = true
z_index = -1
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -5.0
offset_top = -1.0
offset_right = 20.0
offset_bottom = -1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_iwr70")

[node name="JoinButton" parent="PasswordParent/Panel" instance=ExtResource("6_26rx1")]
custom_minimum_size = Vector2(50, 50)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
offset_left = -98.5
offset_top = -98.0
offset_right = 98.5
offset_bottom = -40.0
grow_vertical = 0
pivot_offset = Vector2(98.5, 29)

[node name="Panel" type="Panel" parent="PasswordParent/Panel/JoinButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_nq3wa")

[node name="Join" type="Label" parent="PasswordParent/Panel/JoinButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 30
text = "JOIN"
horizontal_alignment = 1
vertical_alignment = 1

[node name="FilterButton" parent="." instance=ExtResource("6_26rx1")]
custom_minimum_size = Vector2(200, 45)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -355.0
offset_top = 37.0
offset_right = -155.0
offset_bottom = 82.0
grow_horizontal = 0
grow_vertical = 1
pivot_offset = Vector2(100, 22.5)

[node name="Panel" type="Panel" parent="FilterButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_7c1b5")

[node name="label" type="Label" parent="FilterButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 6.0
offset_right = -18.0
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 20
text = "FILTER_ALL"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ExitableControl" parent="." instance=ExtResource("11_wl6qr")]
layout_mode = 1
metadata/_edit_lock_ = true

[node name="FilterScroll" type="ScrollContainer" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -355.0
offset_top = 37.0
offset_right = -155.0
offset_bottom = 237.0
grow_horizontal = 0
theme = ExtResource("3_xrosu")
theme_override_styles/panel = SubResource("StyleBoxFlat_qf4be")
scroll_deadzone = 50

[node name="FilterContainer" type="VBoxContainer" parent="FilterScroll"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="All" parent="FilterScroll/FilterContainer" instance=ExtResource("15_nrcgi")]
layout_mode = 2
text = "FILTER_ALL"

[node name="Empty" parent="FilterScroll/FilterContainer" instance=ExtResource("15_nrcgi")]
layout_mode = 2
filter = 1
text = "FILTER_EMPTY"

[node name="GunAllowed" parent="FilterScroll/FilterContainer" instance=ExtResource("15_nrcgi")]
layout_mode = 2
filter = 2
text = "FILTER_GUN"

[node name="NoGun" parent="FilterScroll/FilterContainer" instance=ExtResource("15_nrcgi")]
layout_mode = 2
filter = 3
text = "FILTER_NO_GUN"

[node name="Loading" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_kecyl")

[node name="Label" type="Label" parent="Loading"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 3
theme_override_constants/outline_size = 9
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 60
text = "LOADING"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="Loading" instance=ExtResource("5_qfuhk")]
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -19.9999
offset_top = 53.0
offset_right = 20.0001
offset_bottom = 93.0

[node name="Error" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_7qk0g")

[node name="Label" type="Label" parent="Error"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 3
theme_override_constants/outline_size = 9
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 60
text = "CONNECTION_ERROR"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RetryButton" type="Button" parent="Error"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -67.0
offset_top = -97.0
offset_right = 67.0
offset_bottom = -41.0
grow_horizontal = 2
grow_vertical = 0
pivot_offset = Vector2(67, 28)
focus_mode = 0
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 30
flat = true

[node name="Panel" type="Panel" parent="Error/RetryButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_16l7n")

[node name="Label" type="Label" parent="Error/RetryButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 20
text = "RETRY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Empty" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_7qk0g")

[node name="Label" type="Label" parent="Empty"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 3
theme_override_constants/outline_size = 9
theme_override_constants/shadow_outline_size = 5
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 60
text = "NO_SERVER"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RetryButton" type="Button" parent="Empty"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -67.0
offset_top = -97.0
offset_right = 67.0
offset_bottom = -41.0
grow_horizontal = 2
grow_vertical = 0
pivot_offset = Vector2(67, 28)
focus_mode = 0
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 30
flat = true

[node name="Panel" type="Panel" parent="Empty/RetryButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_16l7n")

[node name="Label" type="Label" parent="Empty/RetryButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_fonts/font = ExtResource("3_fdod6")
theme_override_font_sizes/font_size = 20
text = "RETRY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Panel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_prhe3")

[node name="Exit" parent="." instance=ExtResource("6_26rx1")]
custom_minimum_size = Vector2(50, 50)
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 56.0
offset_top = 20.0
offset_right = 138.0
offset_bottom = 93.0
grow_horizontal = 1
grow_vertical = 1
pivot_offset = Vector2(53, 49)

[node name="TextureRect" type="TextureRect" parent="Exit"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = 7.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("7_bjwoi")
expand_mode = 1
stretch_mode = 4

[node name="ListHTTPRequest" type="HTTPRequest" parent="."]

[connection signal="pressed" from="RaceMode/JoinButton" to="." method="_on_race_mode_join_button_pressed"]
[connection signal="pressed" from="RestartButton" to="." method="_on_restart_button_pressed"]
[connection signal="touched" from="PasswordParent" to="." method="exit_password_panel"]
[connection signal="pressed" from="PasswordParent/Panel/CancelButton" to="." method="exit_password_panel"]
[connection signal="pressed" from="PasswordParent/Panel/JoinButton" to="." method="_on_password_join_button_pressed"]
[connection signal="pressed" from="FilterButton" to="." method="_on_filter_button_pressed"]
[connection signal="touched" from="ExitableControl" to="." method="_on_exitable_control_touched"]
[connection signal="button_down" from="Error/RetryButton" to="." method="_on_retry_button_button_down"]
[connection signal="button_up" from="Error/RetryButton" to="." method="_on_retry_button_button_up"]
[connection signal="pressed" from="Error/RetryButton" to="." method="_on_retry_button_pressed"]
[connection signal="button_down" from="Empty/RetryButton" to="." method="_on_retry_button_button_down"]
[connection signal="button_up" from="Empty/RetryButton" to="." method="_on_retry_button_button_up"]
[connection signal="pressed" from="Empty/RetryButton" to="." method="_on_retry_button_pressed"]
[connection signal="pressed" from="Exit" to="." method="_on_exit_pressed"]
