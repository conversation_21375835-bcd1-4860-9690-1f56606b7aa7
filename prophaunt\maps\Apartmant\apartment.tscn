[gd_scene load_steps=46 format=3 uid="uid://dxr1wlyku6bt8"]

[ext_resource type="Texture2D" uid="uid://bj4c4guqfb5o4" path="res://HDRI/FS002_Day.png" id="1_nhoo3"]
[ext_resource type="PackedScene" uid="uid://cf7j12u33go1h" path="res://prophaunt/maps/Source/Wall/floor_full.tscn" id="2_3c11n"]
[ext_resource type="MeshLibrary" uid="uid://byvdy6rx2k6ub" path="res://Scenes/WorldItems/GridMap/MeshLibt.tres" id="2_g54ps"]
[ext_resource type="Material" uid="uid://dh2lss3vj7e7p" path="res://prophaunt/Mat/ApartmentFloor.tres" id="2_lojfe"]
[ext_resource type="PackedScene" uid="uid://bllyomds38rd8" path="res://prophaunt/maps/Source/Apartment/Wall/wall_corner_column_bottom.tscn" id="2_ulmt2"]
[ext_resource type="PackedScene" uid="uid://dfvpq04aiamos" path="res://prophaunt/maps/Source/Apartment/Wall/wall_doorway_wide_round.tscn" id="3_pstti"]
[ext_resource type="PackedScene" uid="uid://c4lyoyh22048g" path="res://prophaunt/maps/Source/Apartment/Wall/roof_flat_corner.tscn" id="4_cok8l"]
[ext_resource type="PackedScene" uid="uid://c122nnm6o7te6" path="res://prophaunt/maps/Source/Arena/stairs_stone.tscn" id="5_oqm26"]
[ext_resource type="Material" uid="uid://35s7fqjptxni" path="res://prophaunt/Mat/FloorArenaStone.tres" id="7_rvbd5"]
[ext_resource type="PackedScene" uid="uid://ba1bu2s1nxvbc" path="res://prophaunt/maps/Source/Apartment/Wall/wall_corner_column.tscn" id="7_yelfj"]
[ext_resource type="PackedScene" uid="uid://dv5swdxcsbd4i" path="res://prophaunt/maps/Source/Apartment/Wall/scaffolding_oriental.tscn" id="9_b3qru"]
[ext_resource type="PackedScene" uid="uid://riv8q35f1ppt" path="res://prophaunt/maps/Source/Apartment/Wall/scaffolding_vertical.tscn" id="10_4a8u4"]
[ext_resource type="PackedScene" uid="uid://4kqi3gopvbqf" path="res://prophaunt/maps/Source/Apartment/Wall/scaffolding_horizental.tscn" id="11_yht0w"]
[ext_resource type="PackedScene" uid="uid://bc3ar2p78i2s2" path="res://prophaunt/maps/Source/Apartment/Wall/cloth_shield.tscn" id="12_sch0d"]
[ext_resource type="PackedScene" uid="uid://dbsa8xwwblo28" path="res://prophaunt/maps/Source/Apartment/Wall/plating_detailed_wide.tscn" id="13_a5154"]
[ext_resource type="PackedScene" uid="uid://br6rhl3i8oatn" path="res://prophaunt/maps/Source/Apartment/Wall/wall.tscn" id="14_4gcox"]
[ext_resource type="PackedScene" uid="uid://epw214kv0m4y" path="res://prophaunt/maps/Source/Apartment/Wall/roof_flat_side.tscn" id="16_g37hv"]
[ext_resource type="Material" uid="uid://butuup68r5pbk" path="res://prophaunt/Mat/Hologram.tres" id="16_uagri"]
[ext_resource type="Material" uid="uid://dkmcjc7wc3ws6" path="res://prophaunt/Mat/GrassPath.tres" id="17_86ax1"]
[ext_resource type="PackedScene" uid="uid://qoi5oqiriwe" path="res://prophaunt/maps/Source/Grass/ground_path_corner.tscn" id="18_gvb43"]
[ext_resource type="PackedScene" uid="uid://cmtmq53mqsvao" path="res://prophaunt/maps/Source/Grass/ground_path_side.tscn" id="19_5vmb7"]
[ext_resource type="PackedScene" uid="uid://7am6ib58fe4k" path="res://prophaunt/maps/Source/Grass/ground_path_corner_small.tscn" id="20_gxqbw"]
[ext_resource type="Shader" path="res://Scenes/WorldItems/Platforms/Unusual_Platforms/Water.gdshader" id="21_8hgaw"]
[ext_resource type="Texture2D" uid="uid://cbrtbovb8rytw" path="res://Scenes/Environment/water/seamless_cartoon_styled_water_texture_by_berserkitty-dcatyft-4119011.png" id="22_s6mik"]
[ext_resource type="PackedScene" uid="uid://b3v2c3lcub5vj" path="res://prophaunt/scenes/Props/Apartment/brick_prop.tscn" id="25_upxrr"]
[ext_resource type="PackedScene" uid="uid://c6ywrwnhqdflr" path="res://prophaunt/scenes/Props/Apartment/cement_bucket_prop.tscn" id="26_gkmp1"]
[ext_resource type="PackedScene" uid="uid://cquetkrbas8dd" path="res://prophaunt/scenes/Props/Apartment/forghoon_bricks_prop.tscn" id="27_klt3q"]
[ext_resource type="PackedScene" uid="uid://bfehfbnprqqbc" path="res://prophaunt/scenes/Props/Apartment/forghoon_prop.tscn" id="28_c3bdt"]
[ext_resource type="PackedScene" uid="uid://dlk26gfdn1hkq" path="res://prophaunt/scenes/Props/Apartment/pickaxe_prop.tscn" id="29_vjtqg"]
[ext_resource type="PackedScene" uid="uid://c3ek6xyh6w4hr" path="res://prophaunt/scenes/Props/Apartment/Shovel_prop.tscn" id="30_dnhsm"]
[ext_resource type="PackedScene" uid="uid://cwf7qdbn87n4t" path="res://prophaunt/scenes/Props/Apartment/hammer_prop.tscn" id="31_syv8x"]

[sub_resource type="PanoramaSkyMaterial" id="PanoramaSkyMaterial_2a0ho"]
panorama = ExtResource("1_nhoo3")

[sub_resource type="Sky" id="Sky_r37ka"]
sky_material = SubResource("PanoramaSkyMaterial_2a0ho")

[sub_resource type="Environment" id="Environment_hmcmb"]
background_mode = 2
background_energy_multiplier = 1.2
sky = SubResource("Sky_r37ka")
ambient_light_source = 1
ambient_light_color = Color(0.753907, 0.448284, 0.283835, 1)
ambient_light_energy = 11.65
glow_intensity = 2.55

[sub_resource type="QuadMesh" id="QuadMesh_uui68"]
size = Vector2(6, 4.67)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_u7o4s"]
render_priority = 0
shader = ExtResource("21_8hgaw")
shader_parameter/speed = 5.0
shader_parameter/uvMul = 60.0
shader_parameter/waveSpeed = 0.2
shader_parameter/Txmap = ExtResource("22_s6mik")

[sub_resource type="PlaneMesh" id="PlaneMesh_s3lv7"]
size = Vector2(2500, 2500)

[sub_resource type="BoxShape3D" id="BoxShape3D_hburm"]
size = Vector3(71.6617, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_7v71a"]
size = Vector3(44.3138, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_2rrgf"]
size = Vector3(47.7038, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_l8e7f"]
size = Vector3(56.7231, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_a6ja5"]
size = Vector3(71.7204, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_qcv6r"]
size = Vector3(29.1263, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_c3fxy"]
size = Vector3(8.68624, 52.1251, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_jwpe0"]
size = Vector3(64.024, 52.1251, 1)

[node name="Apartment" type="Node3D"]

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_hmcmb")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.634045, -0.73713, 0.233724, 0.752998, -0.519731, 0.403576, -0.176014, 0.431879, 0.88459, 5.97245, 34.5241, -0.396627)
light_energy = 2.112
shadow_enabled = true

[node name="GridMap" type="GridMap" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 0)
mesh_library = ExtResource("2_g54ps")
cell_size = Vector3(8, 0.1, 8)
data = {
"cells": PackedInt32Array(-5308411, 65527, 655397, -5308412, 65527, 655397, -5308413, 65527, 655397, -5308414, 65527, 655397, -5308415, 65527, 655397, -5242881, 65527, 655397, -5308416, 65527, 655397, -5242882, 65527, 655397, -5242883, 65527, 655397, -5242884, 65527, 655397, -5242885, 65527, 655397, -5242886, 65527, 655397, -5242887, 65527, 655397, -5242888, 65527, 655397, -5242889, 65527, 655397, -5242890, 65527, 655397, -5242891, 65528, 1441829, -5242891, 65529, 1441829, -5242891, 65530, 1441829, -5242891, 65531, 1441829, -5242891, 65532, 1441829, -5242891, 65533, 1441829, -5242891, 65534, 1441829, -5242891, 65535, 1441829, -5242891, 0, 1441829, -5242891, 2, 1441829, -5242891, 1, 1441829, -5242890, 3, 39, -5242890, 4, 1441829, -5242890, 5, 1441829, -5242890, 6, 1441829, -5242890, 7, 1441829, -5242890, 8, 1441829, -5242889, 9, 37, -5242888, 9, 37, -5242887, 9, 37, -5242886, 9, 37, -5242884, 10, 37, -5242883, 10, 37, -5242882, 10, 37, -5242881, 10, 37, -5308416, 10, 37, -5308415, 10, 37, -5308414, 10, 37, -5308413, 10, 37, -5308412, 10, 37, -5308411, 10, 37, -5308410, 10, 37, -5242885, 9, 39, -5308410, 65528, 1048613, -5308410, 65529, 1048613, -5308410, 65530, 1048613, -5308410, 65531, 1048613, -5308410, 65532, 1048613, -5308410, 65533, 655399, -5308409, 65534, 1048613, -5308409, 65535, 1048613, -5308409, 0, 1048613, -5308409, 1, 1048613, -5308409, 2, 1048613, -5308409, 3, 1048613, -5308409, 4, 1048613, -5308409, 5, 1048613, -5308409, 6, 1048613, -5308409, 7, 1048613, -5308409, 8, 1048613, -5308409, 9, 1048613)
}
metadata/_editor_floor_ = Vector3(0, -81, 0)

[node name="Floor1" type="Node3D" parent="."]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, -4)

[node name="Floor" type="Node3D" parent="Floor1"]

[node name="FloorFull" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull5" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull6" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull15" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull32" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull33" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull34" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull47" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull48" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull51" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull52" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull55" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull56" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull7" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull8" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull18" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull19" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull2" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull11" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull12" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull4" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull22" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull23" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull26" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull27" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull28" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull31" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull77" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull78" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull79" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull82" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull83" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull84" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull85" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull86" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull94" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull95" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull96" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull97" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull98" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull99" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull100" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull101" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull102" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull103" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull104" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull105" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull106" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull107" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull171" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull175" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull174" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull176" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull177" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull179" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull180" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull204" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull205" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull206" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull207" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull208" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull209" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull210" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull211" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull268" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull269" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull270" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull271" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull178" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull108" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull110" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull111" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull112" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull113" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull114" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull115" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull116" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull117" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull118" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull119" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull120" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull121" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull122" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull123" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull124" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull125" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull126" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull127" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull128" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull129" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull130" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull131" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull132" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull133" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull134" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull135" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull136" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull137" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull138" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull139" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull140" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull141" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull142" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull143" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull144" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull145" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull146" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull147" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull148" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull150" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull151" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull149" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull109" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull90" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull91" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull92" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull93" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull152" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull170" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull153" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull166" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull167" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull168" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull169" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull154" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull155" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull156" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull157" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull158" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull159" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull160" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull161" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull162" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull163" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull164" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull165" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull181" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull182" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull183" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull184" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull185" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull192" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull193" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull194" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull195" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull196" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull197" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull198" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull199" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, 15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull200" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull201" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, 11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull202" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull203" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull172" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull173" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull186" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull187" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull188" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull189" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull190" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull191" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull212" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull213" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull214" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull215" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull216" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull217" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull218" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull219" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull244" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -9, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull245" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -7, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull246" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -7, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull247" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -9, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull248" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -5, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull249" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -3, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull250" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -3, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull251" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -5, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull252" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -1, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull253" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 1, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull254" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 1, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull255" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -1, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull256" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 3, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull257" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 5, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull258" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 5, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull259" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 3, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull260" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull261" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 9, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull262" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 9, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull263" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull264" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull265" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 13, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull266" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 13, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull267" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull272" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 13, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull273" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull220" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull221" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -13)
material_override = ExtResource("2_lojfe")

[node name="FloorFull222" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull223" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -11)
material_override = ExtResource("2_lojfe")

[node name="FloorFull274" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull275" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull276" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull277" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull278" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -9, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull279" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -7, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull280" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -7, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull281" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -9, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull282" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -5, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull283" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -3, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull284" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -3, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull285" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -5, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull286" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -1, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull287" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 1, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull288" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 1, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull289" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -1, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull290" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 3, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull291" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 5, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull292" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 5, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull293" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 3, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull294" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull295" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 9, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull296" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 9, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull297" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull298" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull299" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 13, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull300" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 13, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull301" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull302" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull303" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -17)
material_override = ExtResource("2_lojfe")

[node name="FloorFull304" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull305" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -15)
material_override = ExtResource("2_lojfe")

[node name="FloorFull224" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull225" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull226" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull227" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull228" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull229" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull230" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull231" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull232" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull233" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull234" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull235" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull236" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull237" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull238" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull239" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull240" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull241" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, 7)
material_override = ExtResource("2_lojfe")

[node name="FloorFull242" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull243" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -17, 0, 9)
material_override = ExtResource("2_lojfe")

[node name="FloorFull87" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull88" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull89" parent="Floor1/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="Wall" type="Node3D" parent="Floor1"]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)

[node name="WallCornerColumnBottom" parent="Floor1/Wall" instance=ExtResource("2_ulmt2")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 0, 10)

[node name="WallCornerColumnBottom6" parent="Floor1/Wall" instance=ExtResource("2_ulmt2")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13.8063, 0, 6)

[node name="WallCornerColumnBottom7" parent="Floor1/Wall" instance=ExtResource("2_ulmt2")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -13.8, 0, -6)

[node name="WallCornerColumnBottom4" parent="Floor1/Wall" instance=ExtResource("2_ulmt2")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -10, 0, -10)

[node name="WallCornerColumnBottom2" parent="Floor1/Wall" instance=ExtResource("2_ulmt2")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, 10)

[node name="WallCornerColumnBottom3" parent="Floor1/Wall" instance=ExtResource("2_ulmt2")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 10, 0, -10)

[node name="WallDoorwayWideRound2" parent="Floor1/Wall" instance=ExtResource("3_pstti")]
transform = Transform3D(-1, 0, 1.6292e-07, 0, 1, 0, -1.6292e-07, 0, -1, -15.7, 0, -4.76837e-07)

[node name="RoofFlatCorner11" parent="Floor1/Wall" instance=ExtResource("4_cok8l")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -13.8, 4.8, 6)

[node name="RoofFlatCorner12" parent="Floor1/Wall" instance=ExtResource("4_cok8l")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -13.8, 4.8, -6)

[node name="WallCornerColumn" parent="Floor1/Wall" instance=ExtResource("7_yelfj")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 4.8, 10)

[node name="WallCornerColumn3" parent="Floor1/Wall" instance=ExtResource("7_yelfj")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -10, 4.8, -10)

[node name="WallCornerColumn4" parent="Floor1/Wall" instance=ExtResource("7_yelfj")]
transform = Transform3D(8.9407e-08, 0, 1, 0, 1, 0, -1, 0, 8.9407e-08, 10, 4.8, -10)

[node name="WallCornerColumn2" parent="Floor1/Wall" instance=ExtResource("7_yelfj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 4.8, 10)

[node name="wall" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 2, 0, 11.9)

[node name="wall16" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -2, 0, 11.9)

[node name="wall17" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -6, 0, 11.9)

[node name="wall2" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 6, 0, 11.9)

[node name="wall3" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 2, 4.8, 11.9)

[node name="wall5" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -2, 4.8, 11.9)

[node name="wall6" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -6, 4.8, 11.9)

[node name="wall4" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 6, 4.8, 11.9)

[node name="wall23" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, -2, 4.8, -11.9)

[node name="wall24" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 2, 4.8, -11.9)

[node name="wall25" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 6, 4.8, -11.9)

[node name="wall26" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, -6, 4.8, -11.9)

[node name="wall27" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, -2, 0, -11.9)

[node name="wall28" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 2, 0, -11.9)

[node name="wall29" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 6, 0, -11.9)

[node name="wall30" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, -6, 0, -11.9)

[node name="wall7" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 4.8, 6)

[node name="wall8" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 4.8, 2)

[node name="wall9" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 4.8, -2)

[node name="wall10" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 4.8, -6)

[node name="wall11" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 0, 6)

[node name="wall12" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 0, 2)

[node name="wall13" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 0, -2)

[node name="wall14" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 0, 1.74845e-07, 0, 1, 0, -1.74845e-07, 0, 1, 11.9, 0, -6)

[node name="wall19" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -11.9, 4.8, -6)

[node name="wall20" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -11.9, 4.8, -2)

[node name="wall21" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -11.9, 4.8, 2)

[node name="wall22" parent="Floor1/Wall" instance=ExtResource("14_4gcox")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -11.9, 4.8, 6)

[node name="RoofFlatSide7" parent="Floor1/Wall" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -13.8, 4.8, -2)

[node name="RoofFlatSide8" parent="Floor1/Wall" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -13.8, 4.8, 2)

[node name="OutsideFence" type="Node3D" parent="Floor1"]

[node name="ScaffoldingVertical7" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -0.84605, -0.25, 27.9)

[node name="ScaffoldingVertical8" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 1.19851, -0.25, 27.9)

[node name="ScaffoldingVertical10" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 4.95, -0.25, 27.95)

[node name="ScaffoldingVertical36" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 8.75, -0.05, 27.95)

[node name="ScaffoldingVertical31" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -2.82667, -0.300001, 27.9)

[node name="PlatingDetailedWide" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -0.81963, 0.14167, 27.9568)

[node name="PlatingDetailedWide3" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -2.77587, 0.0375969, 27.9568)

[node name="PlatingDetailedWide2" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, 1.17466, 0.0294212, 27.9568)

[node name="PlatingDetailedWide4" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 5.00993, 0.118804, 28.0025)

[node name="ScaffoldingVertical30" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 10.8, -0.25, 27.95)

[node name="PlatingDetailedWide30" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 10.8599, 0.118804, 28.0025)

[node name="ScaffoldingVertical39" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 12.85, -0.25, 27.95)

[node name="PlatingDetailedWide31" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 12.9099, 0.118804, 28.0025)

[node name="ScaffoldingVertical40" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 14.85, -0.25, 27.95)

[node name="PlatingDetailedWide32" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 14.9099, 0.118804, 28.0025)

[node name="PlatingDetailedWide53" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 3.07649, 0.117203, 28.0025)

[node name="ScaffoldingVertical11" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -15.0048, -0.05, 23.9)

[node name="PlatingDetailedWide5" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -14.9781, 0.150495, 23.9525)

[node name="ScaffoldingVertical55" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -17.0548, -0.05, 23.9)

[node name="PlatingDetailedWide76" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -17.0281, 0.150495, 23.9525)

[node name="ScaffoldingVertical56" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -17.9652, -0.05, 22.7629)

[node name="ScaffoldingVertical57" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -17.9652, -0.05, 20.7129)

[node name="PlatingDetailedWide77" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499794, -0.0180446, 0.0320404, -0.0106796, 0.00024776, 1.49966, -0.00957844, -0.941827, -0.000219359, -18.0177, 0.150495, 22.7896)

[node name="PlatingDetailedWide78" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499794, -0.0180446, 0.0320404, -0.0106796, 0.00024776, 1.49966, -0.00957844, -0.941827, -0.000219359, -18.0177, 0.150495, 20.8396)

[node name="ScaffoldingVertical13" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -4.84066, -0.3, 27.9)

[node name="ScaffoldingVertical34" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -6.89066, -0.3, 27.9)

[node name="PlatingDetailedWide7" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -4.78073, 0.0861303, 27.9525)

[node name="PlatingDetailedWide62" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -6.87108, -0.0174986, 27.9525)

[node name="ScaffoldingVertical47" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -8.94066, -0.3, 27.9)

[node name="PlatingDetailedWide69" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -8.92108, -0.0174986, 27.9525)

[node name="ScaffoldingVertical48" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -10.9907, -0.3, 27.9)

[node name="PlatingDetailedWide70" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -10.9711, -0.0174986, 27.9525)

[node name="ScaffoldingVertical49" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91803, 0, 0, 0, 0.06503, -12.9907, -0.3, 27.9)

[node name="PlatingDetailedWide71" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957846, -0.941827, -0.000219363, -0.0106796, 0.00024776, 1.49966, -0.499794, 0.0180447, -0.0320404, -12.9711, -0.0174986, 27.9525)

[node name="ScaffoldingVertical50" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -13.9546, -0.3, 26.8165)

[node name="PlatingDetailedWide72" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499794, -0.0180446, 0.0320404, -0.0106796, 0.00024776, 1.49966, -0.00957844, -0.941827, -0.000219359, -14.0071, -0.0174986, 26.836)

[node name="PlatingDetailedWide63" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499794, -0.0180446, 0.0320404, -0.0106796, 0.00024776, 1.49966, -0.00957844, -0.941827, -0.000219359, -14.0235, 0.115414, 24.9405)

[node name="ScaffoldingVertical14" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -8.96973, -0.05, -17.9436)

[node name="ScaffoldingVertical87" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -6.94802, -0.05, -17.9436)

[node name="PlatingDetailedWide8" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -9.0026, 0.121377, -17.9961)

[node name="PlatingDetailedWide57" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -7.00387, 0.165577, -17.9961)

[node name="PlatingDetailedWide58" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -5.06024, 0.0989502, -17.9961)

[node name="ScaffoldingVertical15" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -3.08293, -0.05, -17.9475)

[node name="PlatingDetailedWide59" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -1.19051, 0.0989502, -18)

[node name="ScaffoldingVertical16" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 0.808294, -0.05, -17.9475)

[node name="PlatingDetailedWide10" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 0.767529, 0.163262, -18)

[node name="ScaffoldingVertical17" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 2.84051, -0.05, -17.9475)

[node name="ScaffoldingVertical88" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 4.86974, -0.05, -17.9475)

[node name="PlatingDetailedWide11" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 2.8006, 0.0989502, -18)

[node name="PlatingDetailedWide60" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 4.83436, 0.187517, -18)

[node name="ScaffoldingVertical93" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 8.66974, -0.05, -17.9475)

[node name="PlatingDetailedWide73" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 8.63436, 0.187517, -18)

[node name="PlatingDetailedWide74" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 10.7078, 0.187517, -18)

[node name="PlatingDetailedWide61" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 6.72845, 0.284521, -17.9507)

[node name="ScaffoldingVertical18" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 12.6862, -0.05, -17.8287)

[node name="PlatingDetailedWide12" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, 12.6574, 0.0579052, -17.8734)

[node name="ScaffoldingVertical19" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.753, -0.05, -16.6815)

[node name="PlatingDetailedWide13" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.8055, 0.0523851, -16.7169)

[node name="PlatingDetailedWide40" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.8055, 0.0523851, -14.8169)

[node name="ScaffoldingVertical20" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.8859, -0.05, -6.74112)

[node name="PlatingDetailedWide14" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.9318, 0.16079, -6.7961)

[node name="ScaffoldingVertical44" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.9359, -0.05, -8.79112)

[node name="PlatingDetailedWide37" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.9818, 0.16079, -8.8461)

[node name="ScaffoldingVertical45" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.8359, -0.05, -10.8411)

[node name="PlatingDetailedWide38" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.8818, 0.16079, -10.8961)

[node name="ScaffoldingVertical46" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.8859, -0.05, -12.8911)

[node name="PlatingDetailedWide39" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.9318, 0.16079, -12.9461)

[node name="PlatingDetailedWide46" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.9384, 0.0523851, -4.86781)

[node name="ScaffoldingVertical21" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.8859, -0.05, -2.89112)

[node name="PlatingDetailedWide15" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.9384, 0.11744, -2.92247)

[node name="ScaffoldingVertical22" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.9859, -0.05, -0.841119)

[node name="PlatingDetailedWide16" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.9384, 0.0523851, -0.895956)

[node name="PlatingDetailedWide47" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.8884, 0.178143, 1.12988)

[node name="ScaffoldingVertical23" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 13.7859, -0.05, 3.13199)

[node name="PlatingDetailedWide17" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 13.8384, 0.0523851, 3.07207)

[node name="ScaffoldingVertical24" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 17.9131, -0.05, 5.25621)

[node name="PlatingDetailedWide18" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 17.9656, 0.156876, 5.19629)

[node name="PlatingDetailedWide49" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 17.9656, 0.217624, 7.12815)

[node name="PlatingDetailedWide50" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 17.9656, 0.151957, 8.96747)

[node name="ScaffoldingVertical82" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 17.9131, -0.05, 10.9562)

[node name="PlatingDetailedWide44" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 17.9602, 0.0700029, 10.8963)

[node name="ScaffoldingVertical91" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.86265e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.86265e-09, 17.9631, -0.05, 26.9062)

[node name="PlatingDetailedWide67" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 18.0102, 0.0700029, 26.8463)

[node name="ScaffoldingVertical92" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, -4.7052e-09, 0, 1.91803, 0, 4.7052e-09, 0, 0.06503, 16.8566, -0.05, 27.9027)

[node name="PlatingDetailedWide68" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219354, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 16.9166, 0.0700029, 27.9498)

[node name="PlatingDetailedWide51" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 17.9862, 0.17368, 12.9625)

[node name="PlatingDetailedWide52" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957833, 0.9418, 0.000219364, 17.9812, 0.0785846, 14.9072)

[node name="ScaffoldingVertical25" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, -9.79905e-10, 0, 1.91803, 0, 9.79905e-10, 0, -0.06503, 16.8693, -0.05, 4.1525)

[node name="ScaffoldingVertical85" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, -9.79905e-10, 0, 1.91803, 0, 9.79905e-10, 0, -0.06503, 14.8156, -0.05, 4.1525)

[node name="PlatingDetailedWide19" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.00957833, 0.9418, 0.000219364, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180442, 0.0320404, 16.8093, 0.158161, 4.1)

[node name="PlatingDetailedWide48" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.00957833, 0.9418, 0.000219364, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180442, 0.0320404, 14.8009, 0.158161, 4.1)

[node name="ScaffoldingVertical35" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-3.72529e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, -3.72529e-09, 17.9209, -0.05, 16.8517)

[node name="PlatingDetailedWide26" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957838, 0.9418, 0.000219367, 17.9734, 0.185969, 16.8162)

[node name="ScaffoldingVertical41" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-3.72529e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, -3.72529e-09, 17.9209, -0.05, 18.9017)

[node name="PlatingDetailedWide33" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957838, 0.9418, 0.000219367, 17.9734, 0.0859691, 18.8662)

[node name="ScaffoldingVertical42" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-3.72529e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, -3.72529e-09, 17.9209, -0.05, 20.9517)

[node name="PlatingDetailedWide34" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957838, 0.9418, 0.000219367, 17.9734, 0.185969, 20.9162)

[node name="ScaffoldingVertical43" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-3.72529e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, -3.72529e-09, 17.9209, -0.05, 23.0017)

[node name="PlatingDetailedWide35" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957838, 0.9418, 0.000219367, 17.9734, 0.235969, 22.9662)

[node name="PlatingDetailedWide36" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180442, -0.0320404, -0.0106794, 0.000247753, 1.49966, 0.00957838, 0.9418, 0.000219367, 17.9734, 0.135969, 24.9162)

[node name="PlatingDetailedWide27" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 8.81773, 0.145638, 28.004)

[node name="PlatingDetailedWide45" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, 6.91773, 0.0763687, 28.004)

[node name="ScaffoldingVertical12" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -18.007, -0.05, 16.9895)

[node name="PlatingDetailedWide6" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -18.0595, 0.0759282, 17.0259)

[node name="PlatingDetailedWide64" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -18.0234, 0.0956466, 18.9658)

[node name="ScaffoldingVertical26" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -18.007, -0.05, 14.9556)

[node name="ScaffoldingVertical89" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -18.007, -0.05, 12.9148)

[node name="ScaffoldingVertical90" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -18.007, -0.05, 10.8988)

[node name="PlatingDetailedWide20" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -18.0595, 0.15185, 15.0155)

[node name="PlatingDetailedWide65" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -18.0095, 0.0510981, 12.9539)

[node name="PlatingDetailedWide66" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -18.0595, 0.181897, 10.9873)

[node name="ScaffoldingVertical27" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, -2.22045e-16, 0, 1.91803, 0, 2.22045e-16, 0, 0.06503, -18.9632, -0.05, 9.81104)

[node name="PlatingDetailedWide21" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, -18.9032, 0.128182, 9.86354)

[node name="ScaffoldingVertical28" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.957, -0.05, -1.39267)

[node name="ScaffoldingVertical86" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.957, -0.05, -3.41512)

[node name="PlatingDetailedWide22" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0095, 0.0963042, -1.34969)

[node name="ScaffoldingVertical51" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.957, -0.05, 0.657326)

[node name="PlatingDetailedWide41" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0095, 0.0963042, 0.700312)

[node name="ScaffoldingVertical52" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.957, -0.05, 2.67848)

[node name="PlatingDetailedWide42" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0095, 0.0963042, 2.72147)

[node name="ScaffoldingVertical53" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.957, -0.05, 4.70733)

[node name="PlatingDetailedWide43" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0095, 0.0963042, 4.75031)

[node name="ScaffoldingVertical54" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.957, -0.05, 6.75733)

[node name="PlatingDetailedWide75" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0095, 0.0963042, 6.80031)

[node name="PlatingDetailedWide54" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0095, 0.158122, -3.33235)

[node name="PlatingDetailedWide55" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0261, 0.119227, -5.22409)

[node name="ScaffoldingVertical83" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 17.9099, -0.0500002, 13.023)

[node name="ScaffoldingVertical84" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(1.49012e-08, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 1.49012e-08, 17.9023, -0.0500002, 8.94103)

[node name="ScaffoldingVertical32" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -22.007, -0.05, 8.75733)

[node name="PlatingDetailedWide24" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -22.0541, 0.144506, 8.79495)

[node name="ScaffoldingVertical33" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, -2.22045e-16, 0, 1.91803, 0, 2.22045e-16, 0, 0.06503, -21.0132, -0.05, 9.86104)

[node name="PlatingDetailedWide25" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.00957831, -0.9418, -0.000219363, -0.0106794, 0.000247753, 1.49966, -0.499786, 0.0180441, -0.0320404, -20.9532, 0.128182, 9.91354)

[node name="ScaffoldingVertical29" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.907, -0.05, -7.13194)

[node name="PlatingDetailedWide23" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -21.9595, 0.146014, -7.13005)

[node name="ScaffoldingVertical37" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.907, -0.05, -9.09267)

[node name="PlatingDetailedWide28" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -21.9595, 0.0741321, -9.07774)

[node name="ScaffoldingVertical58" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.907, -0.05, -11.1427)

[node name="PlatingDetailedWide79" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -21.9595, 0.0741321, -11.1277)

[node name="ScaffoldingVertical59" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.907, -0.05, -13.1927)

[node name="PlatingDetailedWide80" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -21.9595, 0.0741321, -13.1777)

[node name="ScaffoldingVertical60" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.907, -0.05, -15.2427)

[node name="PlatingDetailedWide81" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -21.9595, 0.0741321, -15.2277)

[node name="ScaffoldingVertical61" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -21.907, -0.05, -16.9427)

[node name="PlatingDetailedWide82" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180441, 0.0320404, -0.0106794, 0.000247753, 1.49966, -0.00957831, -0.9418, -0.000219363, -21.9595, 0.0741321, -16.9277)

[node name="ScaffoldingVertical38" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -12.8032, -0.05, -17.9365)

[node name="PlatingDetailedWide29" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -12.8632, 0.0820879, -17.989)

[node name="ScaffoldingVertical62" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -14.8532, -0.05, -17.9365)

[node name="PlatingDetailedWide83" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -14.9132, 0.0820879, -17.989)

[node name="ScaffoldingVertical63" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -16.9032, -0.05, -17.9365)

[node name="PlatingDetailedWide84" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -16.9632, 0.0820879, -17.989)

[node name="ScaffoldingVertical64" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -18.9532, -0.05, -17.9365)

[node name="PlatingDetailedWide85" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -19.0132, 0.0820879, -17.989)

[node name="ScaffoldingVertical65" parent="Floor1/OutsideFence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -20.9532, -0.05, -17.9365)

[node name="PlatingDetailedWide86" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -21.0132, 0.0820879, -17.989)

[node name="PlatingDetailedWide56" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -10.9632, 0.195359, -17.989)

[node name="PlatingDetailedWide9" parent="Floor1/OutsideFence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000219362, -0.0106794, 0.000247753, 1.49966, 0.499786, -0.0180441, 0.0320404, -3.14285, 0.180822, -18)

[node name="Light" type="Node3D" parent="Floor1"]

[node name="SpotLight3D" type="SpotLight3D" parent="Floor1/Light"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 4.76071, 0)
light_energy = 20.0
shadow_enabled = true
spot_range = 16.1679
spot_angle = 89.99

[node name="SpotLight3D2" type="SpotLight3D" parent="Floor1/Light"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0.9, 2.51071, 8.5)
light_energy = 20.0
shadow_enabled = true
spot_range = 9.0
spot_angle = 89.99

[node name="SpotLight3D3" type="SpotLight3D" parent="Floor1/Light"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, -3.1, 2.51071, 8.5)
light_energy = 20.0
shadow_enabled = true
spot_range = 9.0
spot_angle = 89.99

[node name="Props" type="Node3D" parent="Floor1"]

[node name="CementBucket3" parent="Floor1/Props" instance=ExtResource("26_gkmp1")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 3.28922, -0.0343831, -3.83307)

[node name="Forghoon6" parent="Floor1/Props" instance=ExtResource("28_c3bdt")]
transform = Transform3D(-0.492555, 0, -0.0859645, 0, 0.5, 0, 0.0859645, 0, -0.492555, -1.6315, 0, 4.75)

[node name="Forghoon5" parent="Floor1/Props" instance=ExtResource("28_c3bdt")]
transform = Transform3D(-0.499337, 0, -0.0257322, 0, 0.5, 0, 0.0257322, 0, -0.499337, -3.382, 0, 4.75)

[node name="Forghoon4" parent="Floor1/Props" instance=ExtResource("28_c3bdt")]
transform = Transform3D(-0.495922, 0, 0.0637314, 0, 0.5, 0, -0.0637314, 0, -0.495922, -4.9, 0, 4.75)

[node name="ToolPickaxeProp6" parent="Floor1/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(2.20522, -1.17421, 0.0907188, 0.0913585, -0.0214369, -2.49824, 1.17416, 2.20698, 0.0240006, -5.34737, 0.0410801, -3.99928)

[node name="ToolPickaxeProp7" parent="Floor1/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(0.729067, -2.39086, 0.0471769, 0.0913585, -0.0214369, -2.49824, 2.38959, 0.730277, 0.0811188, -4.74737, 0.0410801, -3.64928)

[node name="ToolPickaxeProp8" parent="Floor1/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(2.20522, -1.17421, 0.0907188, 0.0913585, -0.0214369, -2.49824, 1.17416, 2.20698, 0.0240006, 13.1526, 0.0410801, 24.6007)

[node name="ToolPickaxeProp9" parent="Floor1/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(0.729067, -2.39086, 0.0471769, 0.0913585, -0.0214369, -2.49824, 2.38959, 0.730277, 0.0811188, 13.7526, 0.0410801, 24.9507)

[node name="ShovelProp23" parent="Floor1/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(1, 0, 0, 0, 0.280466, -0.959864, 0, 0.959864, 0.280466, -4.92376, 1.15391, -5.61077)

[node name="ShovelProp24" parent="Floor1/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(0.965926, 0.00362272, -0.258794, 6.75709e-11, 0.999902, 0.0139971, 0.258819, -0.0135202, 0.965831, -4.52249, 0.0193687, -4.98574)

[node name="ShovelProp25" parent="Floor1/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(0.997377, 0.00101319, -0.0723782, 0, 0.999902, 0.0139971, 0.0723853, -0.0139604, 0.997279, -4.09151, 0.0193687, -5.23574)

[node name="BricksProp16" parent="Floor1/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 4.60738, 1.11759e-08, -2.74241)

[node name="CementBucket4" parent="Floor1/Props" instance=ExtResource("26_gkmp1")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 3.28922, -0.0343831, -8.33307)

[node name="BricksProp17" parent="Floor1/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 4.60738, 1.11759e-08, -7.24241)

[node name="BricksProp18" parent="Floor1/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 12.6074, 1.11759e-08, 26.2576)

[node name="ForghoonBricks2" parent="Floor1/Props" instance=ExtResource("27_klt3q")]
transform = Transform3D(0.353553, 0, 0.353553, 0, 0.5, 0, -0.353553, 0, 0.353553, 7, 0, 5)

[node name="ForghoonBricks3" parent="Floor1/Props" instance=ExtResource("27_klt3q")]
transform = Transform3D(0.353553, 0, 0.353553, 0, 0.5, 0, -0.353553, 0, 0.353553, 2, 0, -7.5)

[node name="ForghoonBricks4" parent="Floor1/Props" instance=ExtResource("27_klt3q")]
transform = Transform3D(-0.433013, 0, -0.25, 0, 0.5, 0, 0.25, 0, -0.433013, 13.5, 0, 26)

[node name="StairsStone" parent="Floor1" instance=ExtResource("5_oqm26")]
transform = Transform3D(1.5005, 0, 0, 0, 1.0455, 0, 0, 0, 0.958, -0.478147, 0, -4.95)
material_override = ExtResource("7_rvbd5")

[node name="Floor2" type="Node3D" parent="."]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 9.8, -4)

[node name="StairsStone2" parent="Floor2" instance=ExtResource("5_oqm26")]
transform = Transform3D(-6.55889e-08, 0, -1, 0, 0.8535, 0, 1.5005, 0, -4.37114e-08, 5, 0, -0.473665)
material_override = ExtResource("7_rvbd5")

[node name="Floor" type="Node3D" parent="Floor2"]

[node name="FloorFull" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull6" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull15" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull47" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull51" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull7" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull18" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull2" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull11" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull4" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull22" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull26" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull77" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull78" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull82" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull83" parent="Floor2/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="RoofFlatCorner" parent="Floor2/Floor" instance=ExtResource("4_cok8l")]
transform = Transform3D(-2.18557e-08, 0, -0.5, 0, 0.5, 0, 0.5, 0, -2.18557e-08, -5, -0.0999999, 5)

[node name="RoofFlatCorner3" parent="Floor2/Floor" instance=ExtResource("4_cok8l")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 5, -0.0999999, 5)

[node name="RoofFlatCorner4" parent="Floor2/Floor" instance=ExtResource("4_cok8l")]
transform = Transform3D(-2.18557e-08, 0, 0.5, 0, 0.5, 0, -0.5, 0, -2.18557e-08, 5, -0.0999999, -5)

[node name="RoofFlatCorner2" parent="Floor2/Floor" instance=ExtResource("4_cok8l")]
transform = Transform3D(-0.5, 0, 4.37114e-08, 0, 0.5, 0, -4.37114e-08, 0, -0.5, -5, -0.0999999, -5)

[node name="RoofFlatSide13" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-0.5, 0, 5.96046e-08, 0, 0.5, 0, -5.96046e-08, 0, -0.5, -5, -0.0999999, 3)

[node name="RoofFlatSide14" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-0.5, 0, 5.96046e-08, 0, 0.5, 0, -5.96046e-08, 0, -0.5, -5, -0.0999999, 1)

[node name="RoofFlatSide15" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-0.5, 0, 5.96046e-08, 0, 0.5, 0, -5.96046e-08, 0, -0.5, -5, -0.0999999, -1)

[node name="RoofFlatSide17" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(8.14603e-08, 0, 0.5, 0, 0.5, 0, -0.5, 0, 8.14603e-08, -3, -0.0999999, -5)

[node name="RoofFlatSide18" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(8.14603e-08, 0, 0.5, 0, 0.5, 0, -0.5, 0, 8.14603e-08, -1, -0.0999999, -5)

[node name="RoofFlatSide19" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(0.5, 0, -1.03316e-07, 0, 0.5, 0, 1.03316e-07, 0, 0.5, 5, -0.0999999, -3)

[node name="RoofFlatSide26" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(0.5, 0, -1.03316e-07, 0, 0.5, 0, 1.03316e-07, 0, 0.5, 5, -0.0999999, -1)

[node name="RoofFlatSide27" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(0.5, 0, -1.03316e-07, 0, 0.5, 0, 1.03316e-07, 0, 0.5, 5, -0.0999999, 1)

[node name="RoofFlatSide20" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(0.5, 0, -1.03316e-07, 0, 0.5, 0, 1.03316e-07, 0, 0.5, 5, -0.0999999, 3)

[node name="RoofFlatSide21" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1.25172e-07, 0, -0.5, 0, 0.5, 0, 0.5, 0, -1.25172e-07, 3, -0.0999999, 5)

[node name="RoofFlatSide22" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1.25172e-07, 0, -0.5, 0, 0.5, 0, 0.5, 0, -1.25172e-07, 1, -0.0999999, 5)

[node name="RoofFlatSide23" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1.25172e-07, 0, -0.5, 0, 0.5, 0, 0.5, 0, -1.25172e-07, -1, -0.0999999, 5)

[node name="RoofFlatSide24" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1.25172e-07, 0, -0.5, 0, 0.5, 0, 0.5, 0, -1.25172e-07, -3, -0.0999999, 5)

[node name="RoofFlatSide25" parent="Floor2/Floor" instance=ExtResource("16_g37hv")]
transform = Transform3D(-0.5, 0, 5.96046e-08, 0, 0.5, 0, -5.96046e-08, 0, -0.5, -5, -0.0999999, -3)

[node name="Fence" type="Node3D" parent="Floor2"]

[node name="ScaffoldingVertical3" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -3.05, 1.15, 6.05)

[node name="ScaffoldingVertical4" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -3.05, -1.2, 6.05)

[node name="ScaffoldingVertical76" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, -0.607502, 4.94214)

[node name="ScaffoldingHorizental18" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 3.25592, 0.72976)

[node name="ScaffoldingHorizental29" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 1.39403, 5.01687)

[node name="ScaffoldingVertical43" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, -0.7098, 0.942145)

[node name="ScaffoldingVertical44" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, -0.349178, 2.99214)

[node name="ScaffoldingHorizental8" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-2.84255e-09, 1.91783, -0.00093938, 0, 0.0277066, 0.0650235, 0.06503, 8.38054e-08, -1.01619e-10, -6.41986, 0.563062, 0.944455)

[node name="ScaffoldingHorizental23" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.25943e-08, 2.94389e-09, -2.84255e-09, 1.91784, -0.000939383, -2.84255e-09, -0.0277067, -0.0650235, -4.91986, 1.21306, -5.92324)

[node name="ScaffoldingHorizental9" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-2.84255e-09, 1.91783, -0.00093938, 0, 0.0277066, 0.0650235, 0.06503, 8.38054e-08, -1.01619e-10, -6.41329, 1.64094, 2.9992)

[node name="ScaffoldingHorizental3" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91783, -0.000939383, 0, 0.0277067, 0.0650235, 0.879897, 2.11453, 6.04135)

[node name="ScaffoldingHorizental4" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91783, -0.000939383, 0, 0.0277067, 0.0650235, 2.95437, 1.11863, 6.13137)

[node name="ScaffoldingHorizental2" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, 0.87928, 0.0577945, 0, -1.70462, 0.0298117, -3.04861, 1.57141, 6.47962)

[node name="ClothShield9" parent="Floor2/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-2.18557e-08, -0.5, 2.18557e-08, 0, -2.18557e-08, -0.5, 0.5, -2.18557e-08, 9.55343e-16, -5.98779, 0.478688, 3.04314)

[node name="ClothShield6" parent="Floor2/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.5115, 0, 0, 0, -2.18557e-08, -0.5, 0, 0.5, -2.18557e-08, -1.01922, 0.720716, 6.05305)

[node name="ClothShield7" parent="Floor2/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.488536, 0.0217661, -9.51426e-10, -5.42572e-17, -2.18557e-08, -0.5, -0.0212872, 0.499526, -2.1835e-08, 4.90952, 0.720716, 6.09945)

[node name="ClothShield10" parent="Floor2/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-2.23584e-08, 0.5, -2.18557e-08, 0, -2.18557e-08, -0.5, -0.5115, -2.18557e-08, 9.55343e-16, 5.83622, 1.45342, -1.32905)

[node name="ClothShield12" parent="Floor2/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-2.23584e-08, 0.5, -2.18557e-08, 0, -2.18557e-08, -0.5, -0.5115, -2.18557e-08, 9.55343e-16, 5.83622, 0.623702, -4.87382)

[node name="ScaffoldingVertical40" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 2.95062, 1.65239, 6.13968)

[node name="ScaffoldingVertical64" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 0.876139, 0.0645308, 6.04966)

[node name="ScaffoldingVertical46" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.95695, 0.0499997, -1.09905)

[node name="PlatingDetailedWide30" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180443, 0.0203222, -0.0106801, 0.000247773, 0.95112, -0.00957833, -0.9418, -0.000139134, -6.00945, 0.5, -1.05634)

[node name="ScaffoldingVertical47" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.95695, 0.0499997, -3.12553)

[node name="PlatingDetailedWide31" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180443, 0.0203222, -0.0106801, 0.000247773, 0.95112, -0.00957833, -0.9418, -0.000139134, -6.00945, 0.5, -3.08282)

[node name="ScaffoldingVertical68" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -4.91184, -0.39913, -5.92893)

[node name="ScaffoldingVertical69" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -1.09771, -0.39913, -5.92893)

[node name="ScaffoldingVertical52" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 0.3, 5.10749)

[node name="ScaffoldingVertical54" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 0.3, 2.87107)

[node name="ScaffoldingVertical55" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 2.65, 2.87107)

[node name="ScaffoldingVertical60" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 0.3, 0.821066)

[node name="ScaffoldingVertical72" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 0.3, -1.27893)

[node name="ScaffoldingVertical61" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 2.65, 0.821066)

[node name="ScaffoldingVertical80" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, -0.620297, -4.82893)

[node name="PlatingDetailedWide36" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000139133, -0.0106801, 0.000247773, 0.95112, 0.499786, -0.0180442, 0.0203222, -3.06991, 0.549999, -5.98763)

[node name="PlatingDetailedWide38" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000139133, -0.0106801, 0.000247773, 0.95112, 0.499786, -0.0180442, 0.0203222, -1.13343, 0.549999, -5.98763)

[node name="ScaffoldingVertical99" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 4.8023, -0.39913, -5.92893)

[node name="PlatingDetailedWide67" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000139133, -0.0106801, 0.000247773, 0.95112, 0.499786, -0.0180442, 0.0203222, 4.76658, 0.549999, -5.98763)

[node name="ScaffoldingVertical75" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, 1.64942, 4.94214)

[node name="ScaffoldingVertical42" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, 1.6402, 0.942145)

[node name="ScaffoldingHorizental22" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.25943e-08, 2.94389e-09, -2.84255e-09, 1.91784, -0.000939383, -2.84255e-09, -0.0277067, -0.0650235, -4.91986, 3.11306, -5.92324)

[node name="ScaffoldingVertical65" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.95695, 2.39807, -1.09905)

[node name="ScaffoldingVertical67" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -4.91184, 1.67847, -5.92893)

[node name="ScaffoldingHorizental24" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.25943e-08, 2.94389e-09, -2.84255e-09, 1.91784, -0.000939383, -2.84255e-09, -0.0277067, -0.0650235, -1.09513, 2.14632, -5.92324)

[node name="ScaffoldingVertical70" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -1.09771, 1.62444, -5.92893)

[node name="ScaffoldingVertical79" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 1.68485, -4.82893)

[node name="ScaffoldingHorizental7" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(2.42933e-09, -1.62466, 0.0345653, 1.47595e-09, -1.01949, -0.0550833, 0.06503, 8.38054e-08, -1.01619e-10, -6.35958, 2.95373, 0.949904)

[node name="ScaffoldingHorizental27" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(2.2637e-09, -1.51053, 0.0400757, 1.71924e-09, -1.18201, -0.051214, 0.06503, 8.38054e-08, -1.01619e-10, -6.50483, 3.59922, 4.9492)

[node name="ScaffoldingHorizental14" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(2.2637e-09, -1.51053, 0.0400757, 1.71924e-09, -1.18201, -0.051214, 0.06503, 8.38054e-08, -1.01619e-10, -6.50483, 3.59922, -1.09629)

[node name="PlatingDetailedWide35" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.307011, -0.0111913, 0.750787, -0.394518, 0.0141567, 0.584261, -0.00957833, -0.9418, -0.000139135, -6.95344, 3.30931, -1.04742)

[node name="ScaffoldingHorizental26" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(2.2637e-09, -1.51053, 0.0400757, 1.71924e-09, -1.18201, -0.051214, 0.06503, 8.38054e-08, -1.01619e-10, -6.50483, 3.59922, -5.02296)

[node name="PlatingDetailedWide37" parent="Floor2/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.307011, -0.0111913, 0.750787, -0.394518, 0.0141567, 0.584261, -0.00957833, -0.9418, -0.000139135, -6.95344, 3.30931, -4.96725)

[node name="ScaffoldingHorizental28" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, -1.91783, 0.000939377, 0, -0.0277066, -0.0650235, -3.04861, 3, 6.04135)

[node name="ScaffoldingOriental" parent="Floor2/Fence" instance=ExtResource("9_b3qru")]
transform = Transform3D(-0.06503, 1.52276e-07, 2.38028e-09, 0, -0.803055, 0.059056, 5.6851e-09, 1.74183, 0.0272272, -2.95, -0.563536, 5.95)

[node name="ScaffoldingVertical5" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -3.05, -3.55, 6.05)

[node name="ScaffoldingHorizental6" parent="Floor2/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91783, -0.000939383, 0, 0.0277067, 0.0650235, 2.95706, -0.8, 6.04135)

[node name="ScaffoldingVertical41" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 2.95, -2.25, 6.05)

[node name="ScaffoldingVertical6" parent="Floor2/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -3.05, -5.9, 6.05)

[node name="Props" type="Node3D" parent="Floor2"]

[node name="BricksProp" parent="Floor2/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.12941, 0, 0.482963, 0, 0.5, 0, -0.482963, 0, 0.12941, -4.02628, -0.0500002, 5.05)

[node name="BricksProp3" parent="Floor2/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.482963, 0, -0.12941, 0, 0.5, 0, 0.12941, 0, 0.482963, -4.87628, -0.0500002, 4.55)

[node name="CementBucket" parent="Floor2/Props" instance=ExtResource("26_gkmp1")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 3.05, 0, 1.05583)

[node name="ForghoonBricks7" parent="Floor2/Props" instance=ExtResource("27_klt3q")]
transform = Transform3D(0.359591, 0, 0.347411, 0, 0.5, 0, -0.347411, 0, 0.359591, -4.2125, 3.29018e-05, 3.78978)

[node name="BricksProp21" parent="Floor2/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(-0.100644, -0.802142, -0.588591, -0.0587549, 0.595354, -0.801312, 0.993186, -0.0460651, -0.107049, 3.78518, 0.963945, 0.270812)

[node name="BricksProp22" parent="Floor2/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(-0.77714, 0.0520861, -0.627168, 0.0487427, 0.998557, 0.0225315, 0.627437, -0.0130598, -0.778558, 3.53518, 0.0139451, -0.0791881)

[node name="BricksProp23" parent="Floor2/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(-0.719499, 0.0507497, -0.692637, 0.0487428, 0.998557, 0.0225315, 0.692781, -0.0175497, -0.720934, 3.53518, -4.88605, -6.57919)

[node name="BricksProp24" parent="Floor2/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(-0.978927, -0.198607, 0.047505, -0.134595, 0.452561, -0.881518, 0.153577, -0.869335, -0.469755, 2.53518, -3.83605, -6.17919)

[node name="BricksProp25" parent="Floor2/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(-0.978927, -0.198607, 0.047505, -0.134595, 0.452561, -0.881518, 0.153577, -0.869335, -0.469755, 12.5352, -3.83605, 27.7708)

[node name="BricksProp26" parent="Floor2/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(-0.963739, -0.053669, 0.261396, -0.0553684, 0.998466, 0.00086464, -0.261042, -0.0136398, -0.965231, 11.5352, -4.88605, 27.2708)

[node name="HammerProp2" parent="Floor2/Props" instance=ExtResource("31_syv8x")]
transform = Transform3D(2.47487, -2.47487, -1.0818e-07, -3.70577e-22, -1.5299e-07, 3.5, -2.47487, -2.47487, -1.0818e-07, 4.05, 0.0999999, 2)

[node name="Floor3" type="Node3D" parent="."]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 17.8, -4)

[node name="Floor" type="Node3D" parent="Floor3"]

[node name="FloorFull" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull5" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull6" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull15" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull32" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull47" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull48" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull49" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull50" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull51" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull52" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull55" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull56" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull7" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull8" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull18" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull2" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull11" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull12" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull4" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull22" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull26" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull27" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull28" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull31" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull77" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull78" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull79" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull82" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull83" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull84" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull87" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull88" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull89" parent="Floor3/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="StairsStone3" parent="Floor3/Floor" instance=ExtResource("5_oqm26")]
transform = Transform3D(-1.5, 0, 1.50996e-07, 0, 0.853, 0, -2.26494e-07, 0, -1, 0.477547, 0, -5)
material_override = ExtResource("7_rvbd5")

[node name="Fence" type="Node3D" parent="Floor3"]

[node name="ScaffoldingVertical66" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 0.900074, -1.27175, 6.0196)

[node name="ScaffoldingVertical" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -3.05, -0.5, 6.05)

[node name="ScaffoldingVertical2" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -3.05, 1.8402, 6.05)

[node name="ScaffoldingVertical95" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -5.07644, 1.7779, 6.05)

[node name="ScaffoldingVertical96" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -5.07644, -0.02456, 6.05)

[node name="ScaffoldingOriental2" parent="Floor3/Fence" instance=ExtResource("9_b3qru")]
transform = Transform3D(-0.06503, 1.52276e-07, 2.38028e-09, 0, -0.803055, 0.059056, 5.6851e-09, 1.74183, 0.0272272, -2.96013, 1.69161, 5.94114)

[node name="ScaffoldingHorizental" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, 0.735351, 0.0600612, 0, -1.77147, 0.0249318, -3.04953, 3.28728, 6.44544)

[node name="ScaffoldingVertical9" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, -0.00979996, 0.967022)

[node name="ScaffoldingVertical81" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, 2.3392, 0.967022)

[node name="ScaffoldingVertical45" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.9508, -0.00979996, 4.94214)

[node name="ScaffoldingHorizental32" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.50165e-08, -2.74065e-09, 2.84255e-09, -1.91784, 0.000939377, -2.84255e-09, 0.0277066, 0.0650235, -0.985544, 1.90879, 6.01844)

[node name="ScaffoldingHorizental10" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, -5.94214, 1.20309, 0.949205)

[node name="ScaffoldingHorizental33" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, -5.94214, 0.696225, -1.11659)

[node name="ScaffoldingHorizental15" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 2.13002, 0.72976)

[node name="ScaffoldingHorizental17" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 0.801102, 0.72976)

[node name="ScaffoldingHorizental16" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 0.82075, 2.77462)

[node name="ScaffoldingHorizental20" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 2.18786, 4.83045)

[node name="ScaffoldingHorizental19" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.83853, 3.10131, 2.77462)

[node name="ScaffoldingHorizental21" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-2.6897e-09, 1.82367, 0.0201465, 9.19578e-10, -0.594212, 0.0618309, 0.06503, 8.38054e-08, -1.0162e-10, 6.31515, 3.53617, 2.76904)

[node name="ScaffoldingHorizental11" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.11661e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, -5.99215, 1.22599, 4.9492)

[node name="ScaffoldingHorizental36" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 8.25943e-08, -2.94389e-09, 2.84255e-09, -1.91784, 0.000939377, -2.84255e-09, -0.0277066, -0.0650235, -5.03844, 2.31948, 6.00562)

[node name="ScaffoldingHorizental13" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(2.2637e-09, -1.51053, 0.0400757, 1.71924e-09, -1.18201, -0.051214, 0.06503, 8.38054e-08, -1.01619e-10, -6.31994, 2.64476, 0.953086)

[node name="ScaffoldingHorizental25" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.25943e-08, 2.94389e-09, -2.84255e-09, 1.91784, -0.000939383, -2.84255e-09, -0.0277067, -0.0650235, -4.91986, 2.26306, -5.86305)

[node name="ScaffoldingHorizental34" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.25943e-08, 2.94389e-09, -2.84255e-09, 1.91784, -0.000939383, -2.84255e-09, -0.0277067, -0.0650235, 2.75514, 2.78052, -5.86305)

[node name="ScaffoldingHorizental37" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, 1.6768e-07, 2.48505e-16, 3.18811e-09, 1.0756, 0.0538426, 4.70705e-09, 1.58806, -0.0364679, -0.714368, 2.63988, -6.27211)

[node name="ScaffoldingHorizental12" parent="Floor3/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91783, -0.000939383, 0, 0.0277067, 0.0650235, 2.90897, 0.849793, 6.04135)

[node name="ClothShield" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.5115, 0, 0, 0, -2.18557e-08, -0.5, 0, 0.5, -2.18557e-08, -3.00043, 0.415605, 6.05305)

[node name="ClothShield3" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.5115, 0, 0, 0, -2.18557e-08, -0.5, 0, 0.5, -2.18557e-08, -5.02907, 0.415605, 6.07516)

[node name="ClothShield8" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-2.3014e-08, -0.5, 2.18557e-08, 0, -2.18557e-08, -0.5, 0.5265, -2.18557e-08, 9.55343e-16, -5.98779, 0.1827, 3.00305)

[node name="ClothShield11" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-2.23584e-08, 0.5, -2.18557e-08, 0, -2.18557e-08, -0.5, -0.5115, -2.18557e-08, 9.55343e-16, 5.83622, 2.11389, -4.87927)

[node name="ClothShield14" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-2.23584e-08, 0.5, -2.18557e-08, 0, -2.18557e-08, -0.5, -0.5115, -2.18557e-08, 9.55343e-16, 5.83858, 2.86077, 0.774562)

[node name="ClothShield13" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-1.89707e-08, 0.5, -2.18557e-08, 0, -2.18557e-08, -0.5, -0.434, -2.18557e-08, 9.55343e-16, 5.87844, 0.227553, -3.07491)

[node name="ScaffoldingVertical30" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 0.900074, 1.1881, 6.0196)

[node name="ClothShield2" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.517, 0, 0, 0, -2.18557e-08, -0.5, 0, 0.5, -2.18557e-08, 0.942538, 0.361036, 6.02265)

[node name="ScaffoldingVertical39" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 4.81797, -0.0500002, 6.02121)

[node name="ClothShield5" parent="Floor3/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.5125, 0, 0, 0, -2.18557e-08, -0.5, 0, 0.5, -2.18557e-08, 4.86897, 0.24092, 6.02426)

[node name="PlatingDetailedWide39" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180443, -0.0203222, -0.0106801, 0.000247774, 0.95112, 0.0095783, 0.9418, 0.000139135, 5.8642, 0.309719, 4.89629)

[node name="PlatingDetailedWide40" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180443, -0.0203222, -0.0106801, 0.000247774, 0.95112, 0.0095783, 0.9418, 0.000139135, 5.91555, 0.2, -1.25316)

[node name="PlatingDetailedWide43" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180443, -0.0203222, -0.0106801, 0.000247774, 0.95112, 0.0095783, 0.9418, 0.000139135, 5.91555, 0.2, 0.751192)

[node name="ScaffoldingVertical50" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-2.84255e-09, 0, -0.06503, 0, 1.91803, 0, 0.06503, 0, -2.84255e-09, -5.95695, -0.0999994, -3.07583)

[node name="PlatingDetailedWide34" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.499786, -0.0180443, 0.0203222, -0.0106801, 0.000247773, 0.95112, -0.00957833, -0.9418, -0.000139134, -6.03551, 0.184297, -3.02078)

[node name="ScaffoldingVertical48" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -4.91184, -0.0500002, -5.86874)

[node name="ScaffoldingVertical51" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -4.91184, 2.3, -5.86874)

[node name="ScaffoldingVertical100" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -2.82249, 2.3, -5.86874)

[node name="ScaffoldingVertical101" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -0.714557, 2.3, -5.86874)

[node name="ScaffoldingVertical102" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -0.714557, -0.0133009, -5.86874)

[node name="ScaffoldingVertical73" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, -0.0499992, -1.22893)

[node name="PlatingDetailedWide41" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(-0.499786, 0.0180443, -0.0203222, -0.0106801, 0.000247774, 0.95112, 0.0095783, 0.9418, 0.000139135, 5.89091, 0.2, -4.88124)

[node name="ScaffoldingVertical74" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, -0.0499992, -4.82893)

[node name="ScaffoldingVertical78" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 2.2, -4.82893)

[node name="PlatingDetailedWide42" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.00957832, 0.9418, 0.000139136, -0.0106801, 0.000247774, 0.95112, 0.499786, -0.0180443, 0.0203222, 4.82112, 0.154618, -5.97256)

[node name="ScaffoldingVertical77" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 4.8383, -0.0499992, -5.91302)

[node name="ScaffoldingVertical92" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 2.75937, -0.0499992, -5.87762)

[node name="ScaffoldingVertical98" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 4.84816, 1.10138, -5.91302)

[node name="ScaffoldingVertical56" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 0.0512924, 4.92107)

[node name="ScaffoldingVertical57" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 1, 2.87107)

[node name="PlatingDetailedWide32" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000139133, -0.0106801, 0.000247773, 0.95112, 0.499786, -0.0180442, 0.0203222, -4.94416, 0.4, -5.92124)

[node name="ScaffoldingVertical49" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -2.86184, -0.0500002, -5.86874)

[node name="PlatingDetailedWide33" parent="Floor3/Fence" instance=ExtResource("13_a5154")]
transform = Transform3D(0.0095783, 0.9418, 0.000139133, -0.0106801, 0.000247773, 0.95112, 0.499786, -0.0180442, 0.0203222, -2.91476, 0.4, -5.92124)

[node name="ScaffoldingVertical62" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, 1, 0.821066)

[node name="ScaffoldingVertical53" parent="Floor3/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, -1.35, 5.10749)

[node name="Props" type="Node3D" parent="Floor3"]

[node name="CementBucket2" parent="Floor3/Props" instance=ExtResource("26_gkmp1")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, -0.93949, -0.0178566, -3.04647)

[node name="ForghoonBricks" parent="Floor3/Props" instance=ExtResource("27_klt3q")]
transform = Transform3D(0.359591, 0, 0.347411, 0, 0.5, 0, -0.347411, 0, 0.359591, -4.3625, 3.33786e-05, 3.68978)

[node name="ToolPickaxeProp3" parent="Floor3/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(0.124416, -1.92767, -1.58701, 0.0148625, 1.58952, -1.92956, 2.49686, 0.0865927, 0.0905651, -5.59737, 0.0410805, -0.699283)

[node name="ToolPickaxeProp5" parent="Floor3/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(-1.06861, -1.85756, -1.28746, -0.0681432, 1.44995, -2.03544, 2.25908, -0.834942, -0.670405, -5.66531, 0.0410805, -0.906902)

[node name="ShovelProp19" parent="Floor3/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(0.903178, 0.00600849, -0.429224, 0, 0.999902, 0.0139971, 0.429266, -0.0126419, 0.90309, 0.0329522, 0.0259581, -3.51634)

[node name="ShovelProp20" parent="Floor3/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(0.993186, -0.0460651, -0.107049, -0.0587549, 0.595354, -0.801312, 0.100644, 0.802142, 0.588591, 0.635179, 0.963945, -3.77919)

[node name="BricksProp14" parent="Floor3/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, -4.89141, 0.0220737, 4.71121)

[node name="HammerProp3" parent="Floor3/Props" instance=ExtResource("31_syv8x")]
transform = Transform3D(3.03109, -1.75, -7.64949e-08, 3.21828e-15, -1.5299e-07, 3.5, -1.75, -3.03109, -1.32493e-07, -3.55, 0.1, 4.35)

[node name="Floor4" type="Node3D" parent="."]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 25.8, -4)

[node name="Floor" type="Node3D" parent="Floor4"]

[node name="FloorFull" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull5" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull6" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull15" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull32" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull33" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull34" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull47" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull51" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull55" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull56" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull7" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull8" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull18" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull2" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull11" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull12" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull4" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull22" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull19" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull23" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull26" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull27" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull28" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull31" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull77" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull78" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull79" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull82" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull83" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull84" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="FloorFull87" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 1)
material_override = ExtResource("2_lojfe")

[node name="FloorFull88" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 3)
material_override = ExtResource("2_lojfe")

[node name="FloorFull89" parent="Floor4/Floor" instance=ExtResource("2_3c11n")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, 5)
material_override = ExtResource("2_lojfe")

[node name="Fence" type="Node3D" parent="Floor4"]

[node name="ScaffoldingVertical93" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 2.75937, -0.61748, -5.87762)

[node name="ScaffoldingVertical97" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, 4.82411, -0.61748, -5.87762)

[node name="ScaffoldingVertical63" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, -0.649999, 0.821066)

[node name="ScaffoldingVertical58" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(4.90736e-09, 0, 0.065, 0, 1.918, 0, -0.065, 0, 4.90736e-09, 5.83867, -1.36311, 4.91873)

[node name="ScaffoldingVertical94" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, -5.07644, -0.0221024, 6.05)

[node name="ScaffoldingHorizental5" parent="Floor4/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, -5.94214, 0.885993, 0.973408)

[node name="ScaffoldingHorizental30" parent="Floor4/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-8.53474e-17, 0.0277066, 0.0650235, 2.84255e-09, -1.91784, 0.000939377, 0.06503, 8.38054e-08, -1.01619e-10, 5.84134, 0.590867, 0.730648)

[node name="ScaffoldingHorizental31" parent="Floor4/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.50165e-08, -2.74065e-09, 2.84255e-09, -1.91784, 0.000939377, -2.84255e-09, 0.0277066, 0.0650235, 0.991343, 0.740867, 6.01844)

[node name="ScaffoldingVertical71" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 0.900074, -0.511896, 6.0196)

[node name="ScaffoldingVertical91" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(0.06503, 0, 0, 0, 1.91804, 0, 0, 0, 0.06503, 4.70654, -0.154043, 5.87902)

[node name="ClothShield15" parent="Floor4/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(0.5125, 0, 0, 0, -2.18557e-08, -0.5, 0, 0.5, -2.18557e-08, 4.75755, 0.136877, 5.88207)

[node name="ClothShield16" parent="Floor4/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-0.5125, -4.37114e-08, 1.91069e-15, 0, -2.18557e-08, -0.5, 4.48042e-08, -0.5, 2.18557e-08, 2.70883, 0.136877, -5.90992)

[node name="ScaffoldingVertical103" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(-0.06503, 0, 5.6851e-09, 0, 1.91803, 0, -5.6851e-09, 0, -0.06503, -0.714557, 0.628467, -5.86874)

[node name="ClothShield17" parent="Floor4/Fence" instance=ExtResource("12_sch0d")]
transform = Transform3D(-0.5125, -4.37114e-08, 1.91069e-15, 0, -2.18557e-08, -0.5, 4.48042e-08, -0.5, 2.18557e-08, 4.77356, 0.136877, -5.87341)

[node name="ScaffoldingVertical59" parent="Floor4/Fence" instance=ExtResource("10_4a8u4")]
transform = Transform3D(8.52765e-09, 0, 0.06503, 0, 1.91803, 0, -0.06503, 0, 8.52765e-09, 5.83816, -0.649999, 2.87107)

[node name="ScaffoldingHorizental35" parent="Floor4/Fence" instance=ExtResource("11_yht0w")]
transform = Transform3D(-0.06503, -8.25943e-08, 2.94389e-09, 9.82865e-10, 1.00718, 0.055343, -3.89797e-09, 1.63231, -0.0341481, 2.75543, -0.204006, -6.37812)

[node name="Props" type="Node3D" parent="Floor4"]

[node name="ForghoonBricks3" parent="Floor4/Props" instance=ExtResource("27_klt3q")]
transform = Transform3D(0.439238, 0, -0.238894, 0, 0.5, 0, 0.238894, 0, 0.439238, 4.00002, -0.0408621, -4.61587)

[node name="ToolPickaxeProp" parent="Floor4/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(1.82481, -1.70797, -0.0539138, 0, 0.0788757, -2.49876, 1.70882, 1.8239, 0.0575732, -4.5, 0.0265045, -1.5)

[node name="ToolPickaxeProp4" parent="Floor4/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(-1.02525, -2.27897, -0.0719379, 0, 0.0788756, -2.49876, 2.2801, -1.02473, -0.0323467, -3.98693, 0.0265045, -0.963956)

[node name="ToolPickaxeProp2" parent="Floor4/Props" instance=ExtResource("29_vjtqg")]
transform = Transform3D(2.48474, -0.150183, -0.231377, -0.0701852, 1.68377, -1.84662, 0.266766, 1.84184, 1.66927, 1.64078, 0.0114574, 5.62032)

[node name="ShovelProp10" parent="Floor4/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(1, 0, 0, 0, 0.280466, -0.959864, 0, 0.959864, 0.280466, 2.07624, 1.15391, -5.96077)

[node name="ShovelProp13" parent="Floor4/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(0.965926, 0.00362272, -0.258794, 6.75709e-11, 0.999902, 0.0139971, 0.258819, -0.0135202, 0.965831, 2.52751, 0.0193691, -5.43574)

[node name="ShovelProp17" parent="Floor4/Props" instance=ExtResource("30_dnhsm")]
transform = Transform3D(0.997377, 0.00101319, -0.0723782, 0, 0.999902, 0.0139971, 0.0723853, -0.0139604, 0.997279, 2.90849, 0.0193691, -5.58574)

[node name="BricksProp11" parent="Floor4/Props" instance=ExtResource("25_upxrr")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 3.82047, 0, 3.95299)

[node name="BricksProp12" parent="Floor4/Props" instance=ExtResource("26_gkmp1")]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 4.71583, 0, 2.74467)

[node name="HammerProp" parent="Floor4/Props" instance=ExtResource("31_syv8x")]
transform = Transform3D(3.38074, -0.905867, -3.95967e-08, -3.70577e-22, -1.5299e-07, 3.5, -0.905867, -3.38074, -1.47777e-07, 3.2, 0.0500002, 4.5)

[node name="HaunterRoom" type="Node3D" parent="."]
transform = Transform3D(1, -9.31323e-10, 5.55112e-17, 9.31323e-10, 1, -1.21951e-15, -1.11022e-16, -2.51361e-15, 1, 0, 0, 14.2709)

[node name="Walls" type="Node3D" parent="HaunterRoom"]

[node name="FloorFull32" parent="HaunterRoom/Walls" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 2.79067e-31, 0, -2.70786e-31, 2, 0, 0, 0, 2, 2, 5, -0.178576)
material_override = ExtResource("2_lojfe")

[node name="FloorFull33" parent="HaunterRoom/Walls" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 2.79067e-31, 0, -2.70786e-31, 2, 0, 0, 0, 2, 6, 5, -0.178576)
material_override = ExtResource("2_lojfe")

[node name="FloorFull80" parent="HaunterRoom/Walls" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 2.79067e-31, 0, -2.70786e-31, 2, 0, 0, 0, 2, -2, 5, -0.178576)
material_override = ExtResource("2_lojfe")

[node name="FloorFull85" parent="HaunterRoom/Walls" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 2.79067e-31, 0, -2.70786e-31, 2, 0, 0, 0, 2, -6, 5, -0.178576)
material_override = ExtResource("2_lojfe")

[node name="WallCornerColumnBottom8" parent="HaunterRoom/Walls" instance=ExtResource("2_ulmt2")]
transform = Transform3D(-4.37114e-08, 1.39534e-31, -1, 0, 1, -5.33065e-23, 1, 0, -4.37114e-08, -10.1, -2.19851e-14, 3.82142)

[node name="WallDoorwayWideRound" parent="HaunterRoom/Walls" instance=ExtResource("3_pstti")]
transform = Transform3D(-1, 1.39534e-31, 1.6292e-07, -1.98683e-22, 1, 0, -1.6292e-07, 0, -1, -12, -2.21967e-14, -2.17858)

[node name="RoofFlatCorner5" parent="HaunterRoom/Walls" instance=ExtResource("4_cok8l")]
transform = Transform3D(-4.37114e-08, 1.39534e-31, -1, 0, 1, -5.33065e-23, 1, 0, -4.37114e-08, -10, 4.8, 3.82142)

[node name="RoofFlatCorner6" parent="HaunterRoom/Walls" instance=ExtResource("4_cok8l")]
transform = Transform3D(-1, 1.39534e-31, 8.74228e-08, -1.06613e-22, 1, 0, -8.74228e-08, 0, -1, -10, 4.8, -4.17858)

[node name="RoofFlatCorner7" parent="HaunterRoom/Walls" instance=ExtResource("4_cok8l")]
transform = Transform3D(-4.37114e-08, 1.39534e-31, 1, 0, 1, -5.33065e-23, -1, 0, -4.37114e-08, 6, 4.8, -4.17858)

[node name="RoofFlatCorner8" parent="HaunterRoom/Walls" instance=ExtResource("4_cok8l")]
transform = Transform3D(1, 1.39534e-31, -1.42109e-14, 1.71949e-29, 1, 0, 1.42109e-14, 0, 1, 6, 4.8, 3.82142)

[node name="RoofFlatSide" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(-4.37114e-08, 1.39534e-31, -1, 0, 1, -5.33065e-23, 1, 0, -4.37114e-08, -6, 4.8, 3.82142)

[node name="RoofFlatSide2" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(-4.37114e-08, 1.39534e-31, -1, 0, 1, -5.33065e-23, 1, 0, -4.37114e-08, -2, 4.8, 3.82142)

[node name="RoofFlatSide9" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(-4.37114e-08, 1.39534e-31, -1, 0, 1, -5.33065e-23, 1, 0, -4.37114e-08, 2, 4.8, 3.82142)

[node name="RoofFlatSide5" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(1.31134e-07, 1.39534e-31, 1, 0, 1, 1.59919e-22, -1, 0, 1.31134e-07, -2, 4.8, -4.17858)

[node name="RoofFlatSide10" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(1.31134e-07, 1.39534e-31, 1, 0, 1, 1.59919e-22, -1, 0, 1.31134e-07, 2, 4.8, -4.17858)

[node name="RoofFlatSide6" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(1.31134e-07, 1.39534e-31, 1, 0, 1, 1.59919e-22, -1, 0, 1.31134e-07, -6, 4.8, -4.17858)

[node name="RoofFlatSide11" parent="HaunterRoom/Walls" instance=ExtResource("16_g37hv")]
transform = Transform3D(-1, 1.39534e-31, 1.19209e-07, -1.45377e-22, 1, 0, -1.19209e-07, 0, -1, -10, 4.8, -0.178576)

[node name="WallCornerColumnBottom5" parent="HaunterRoom/Walls" instance=ExtResource("2_ulmt2")]
transform = Transform3D(1, 9.31323e-10, -1.07137e-14, -9.31323e-10, 1, 1.21951e-15, 1.07692e-14, 2.51361e-15, 1, 5.9, -5.4948e-09, 3.82142)

[node name="wall31" parent="HaunterRoom/Walls" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 9.31323e-10, -1, 1.09738e-15, 1, 9.31323e-10, 1, 2.51361e-15, 1.31134e-07, -2.1, 1.95578e-09, 5.72142)

[node name="wall36" parent="HaunterRoom/Walls" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 9.31323e-10, -1, 1.09738e-15, 1, 9.31323e-10, 1, 2.51361e-15, 1.31134e-07, 1.9, -1.76951e-09, 5.72142)

[node name="wall32" parent="HaunterRoom/Walls" instance=ExtResource("14_4gcox")]
transform = Transform3D(1.31134e-07, 9.31323e-10, -1, 1.09738e-15, 1, 9.31323e-10, 1, 2.51361e-15, 1.31134e-07, -6.1, 5.68107e-09, 5.72142)

[node name="wall34" parent="HaunterRoom/Walls" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 9.31323e-10, 1.74845e-07, -9.31323e-10, 1, 1.05667e-15, -1.74845e-07, 2.51361e-15, 1, 7.8, -7.26432e-09, -4.17858)

[node name="StairsStone4" parent="HaunterRoom/Walls" instance=ExtResource("5_oqm26")]
transform = Transform3D(-2.151, 9.89065e-10, -3.01992e-07, 2.00327e-09, 1.062, 5.30848e-15, 3.24792e-07, 2.66946e-15, -2, 17.086, 0.0182402, -0.218235)
material_override = ExtResource("7_rvbd5")

[node name="wall37" parent="HaunterRoom/Walls" instance=ExtResource("14_4gcox")]
transform = Transform3D(1, 9.31323e-10, 1.74845e-07, -9.31323e-10, 1, 1.05667e-15, -1.74845e-07, 2.51361e-15, 1, 7.7941, -7.25882e-09, -0.176798)

[node name="MeshInstance3D" type="MeshInstance3D" parent="HaunterRoom"]
transform = Transform3D(-4.37114e-08, 1.39534e-31, -1, 0, 1, -5.33065e-23, 1, 0, -4.37114e-08, -12.0159, 2.23852, -2.20103)
material_override = ExtResource("16_uagri")
mesh = SubResource("QuadMesh_uui68")

[node name="HaunterSpawn" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0.7, 14)

[node name="1" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 7.3, 0, -3.7)

[node name="2" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 7.3, 0, -0.0999994)

[node name="3" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 7.3, 0, 3.3)

[node name="4" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 3, 0, -3.7)

[node name="5" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 3, 0, -0.0999994)

[node name="6" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 3, 0, 3.3)

[node name="7" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -1.1, 0, -3.7)

[node name="8" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -1.1, 0, -0.0999994)

[node name="9" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -1.1, 0, 3.3)

[node name="10" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -6.1, 0, -3.7)

[node name="11" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -6.1, 0, -0.0999994)

[node name="12" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -6.1, 0, 3.3)

[node name="Ground" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -4)

[node name="FloorFull181" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull182" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull183" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull184" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull185" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull186" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull187" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull357" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull360" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull362" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull363" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull364" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull365" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull188" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull189" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull190" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull191" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull192" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull193" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull194" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull195" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull196" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull197" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull198" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull201" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull343" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull202" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull203" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 22)
material_override = ExtResource("2_lojfe")

[node name="FloorFull204" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull205" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 14)
material_override = ExtResource("2_lojfe")

[node name="FloorFull206" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull207" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 6)
material_override = ExtResource("2_lojfe")

[node name="FloorFull208" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 6)
material_override = ExtResource("2_lojfe")

[node name="FloorFull209" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull210" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull211" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull212" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull213" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull214" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull215" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull216" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull217" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull218" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull219" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull220" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull221" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull222" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull223" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull224" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull225" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull226" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull227" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull228" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull229" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull230" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull231" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull232" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull233" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull234" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull235" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull236" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull237" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull238" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull199" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull200" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull366" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull239" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull344" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull345" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull240" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 22)
material_override = ExtResource("2_lojfe")

[node name="FloorFull241" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull242" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 14)
material_override = ExtResource("2_lojfe")

[node name="FloorFull243" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull244" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull245" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull246" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull247" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull248" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull249" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull250" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull251" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull252" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull253" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull254" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull255" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull640" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull637" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull638" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull639" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull641" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull642" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull644" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull711" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull712" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull713" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull714" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull715" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull716" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull717" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull718" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull719" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull720" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull721" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull722" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull723" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull724" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull725" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull726" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull727" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull728" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull729" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull730" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull731" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull732" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull733" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull734" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull735" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull736" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull737" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull738" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull739" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull740" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull741" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull742" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull743" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull744" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull745" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull746" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull747" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull748" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull749" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull750" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull751" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull752" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull753" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull754" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull755" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull756" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull757" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull758" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull759" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull760" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull761" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull762" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull763" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull764" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull765" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull766" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull767" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull792" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull793" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull794" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull795" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull796" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull797" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull798" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull799" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull768" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull769" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull770" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull771" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull772" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull773" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull774" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull775" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull776" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull777" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull778" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull779" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull780" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull781" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull782" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull783" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull784" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull785" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull786" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull787" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull788" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull789" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull790" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull791" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull256" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull257" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull328" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull340" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull346" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull349" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull350" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull351" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull352" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull397" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull402" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull403" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull404" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull355" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull707" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull708" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull709" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull710" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull310" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -22)
material_override = ExtResource("2_lojfe")

[node name="FloorFull327" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull379" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -14)
material_override = ExtResource("2_lojfe")

[node name="FloorFull382" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull384" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -6)
material_override = ExtResource("2_lojfe")

[node name="FloorFull385" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull387" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 2)
material_override = ExtResource("2_lojfe")

[node name="FloorFull389" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -2)
material_override = ExtResource("2_lojfe")

[node name="FloorFull390" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull392" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 6)
material_override = ExtResource("2_lojfe")

[node name="FloorFull395" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull396" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 14)
material_override = ExtResource("2_lojfe")

[node name="FloorFull318" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -22)
material_override = ExtResource("2_lojfe")

[node name="FloorFull409" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull600" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull601" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull602" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull603" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull410" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -14)
material_override = ExtResource("2_lojfe")

[node name="FloorFull411" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull414" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -6)
material_override = ExtResource("2_lojfe")

[node name="FloorFull416" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull417" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 2)
material_override = ExtResource("2_lojfe")

[node name="FloorFull419" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -2)
material_override = ExtResource("2_lojfe")

[node name="FloorFull420" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 10)
material_override = ExtResource("2_lojfe")

[node name="FloorFull421" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 6)
material_override = ExtResource("2_lojfe")

[node name="FloorFull422" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 18)
material_override = ExtResource("2_lojfe")

[node name="FloorFull427" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 14)
material_override = ExtResource("2_lojfe")

[node name="FloorFull258" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull259" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull260" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull261" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull262" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull263" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull264" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull265" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull266" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull267" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull268" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull269" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull270" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull605" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull606" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull607" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull608" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull609" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull610" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull611" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull612" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull613" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull614" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull615" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull616" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull271" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull272" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull273" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull274" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull275" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull276" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull277" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull278" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull279" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull280" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull281" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull282" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull283" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull284" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull285" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull286" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull289" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull290" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull627" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull696" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull701" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull702" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull703" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull704" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull705" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull706" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull804" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull805" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull800" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull801" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull802" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull803" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull806" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull807" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull808" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull809" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull810" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull811" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull812" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull813" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull814" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull815" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull816" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull817" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull818" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull819" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull820" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull821" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull822" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull823" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull824" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull825" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull826" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull827" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull828" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull829" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull830" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull831" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull832" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull833" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull834" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull835" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull836" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull837" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull303" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull304" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull305" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull306" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull307" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull309" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull311" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull312" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull313" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull314" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull315" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull316" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull317" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull319" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull320" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull321" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull322" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull339" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull341" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull342" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull347" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull348" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull353" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull354" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull617" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull618" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull619" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull620" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull356" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull358" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull359" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull361" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull367" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull368" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull369" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull370" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull371" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull372" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull377" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull378" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull380" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull381" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull383" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull386" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull388" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull391" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull393" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull394" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull398" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull399" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull400" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull401" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull405" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull406" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull407" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull408" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull621" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull622" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull623" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull624" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull625" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull626" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull628" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull695" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull697" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull698" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull699" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull700" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull412" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull413" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull415" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull418" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull423" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull424" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull483" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull484" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull425" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull426" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull433" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull434" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull441" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull451" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull458" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull460" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull461" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull462" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull463" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull467" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull468" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull469" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull452" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull459" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull473" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull629" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull630" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull631" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull632" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull633" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull634" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull635" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull636" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull464" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull465" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull466" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull470" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull471" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull472" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull474" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull475" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull476" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull477" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull478" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull490" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull491" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull492" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull493" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull497" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull498" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -74, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull503" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull504" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -70, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull505" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull506" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull509" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull510" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull513" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull514" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull517" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull518" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull575" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull588" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull589" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull586" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull582" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull604" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull645" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull646" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull647" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull648" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull649" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull650" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull651" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull652" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull323" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull324" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull325" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull326" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull839" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull840" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull841" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull842" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull843" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull844" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull845" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull846" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, -30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull329" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, -10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull330" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, -6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull331" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, -2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull332" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 2)
material_override = ExtResource("17_86ax1")

[node name="FloorFull333" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 6)
material_override = ExtResource("17_86ax1")

[node name="FloorFull334" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 10)
material_override = ExtResource("17_86ax1")

[node name="FloorFull335" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull336" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 14)
material_override = ExtResource("17_86ax1")

[node name="FloorFull337" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull338" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 18)
material_override = ExtResource("17_86ax1")

[node name="FloorFull519" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull520" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 22)
material_override = ExtResource("17_86ax1")

[node name="FloorFull521" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull522" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 26)
material_override = ExtResource("17_86ax1")

[node name="FloorFull523" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull524" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 30)
material_override = ExtResource("17_86ax1")

[node name="FloorFull525" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull526" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 34)
material_override = ExtResource("17_86ax1")

[node name="FloorFull671" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull672" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 38)
material_override = ExtResource("17_86ax1")

[node name="FloorFull673" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull674" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 42)
material_override = ExtResource("17_86ax1")

[node name="FloorFull675" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull676" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 46)
material_override = ExtResource("17_86ax1")

[node name="FloorFull677" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull678" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 50)
material_override = ExtResource("17_86ax1")

[node name="FloorFull527" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull528" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull679" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull680" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 54)
material_override = ExtResource("17_86ax1")

[node name="FloorFull529" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull530" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull583" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull584" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 58)
material_override = ExtResource("17_86ax1")

[node name="FloorFull585" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull838" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull531" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull532" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull533" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull534" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull535" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull536" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull537" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull538" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull539" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull540" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull541" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull542" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull543" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull544" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull545" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull546" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull590" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull643" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull664" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull665" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull547" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull548" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull549" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull550" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull551" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull552" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull553" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull554" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull555" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull556" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull557" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull558" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull559" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull560" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull561" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull562" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull563" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull564" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull565" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull566" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull567" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull568" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull869" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull870" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull569" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull570" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 78)
material_override = ExtResource("17_86ax1")

[node name="FloorFull847" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 58, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull848" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 54, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull849" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull850" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull851" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull852" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull853" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull854" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull855" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull856" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull857" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull858" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull859" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull860" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull861" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull862" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull863" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull864" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull865" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull866" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull867" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull868" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 82)
material_override = ExtResource("17_86ax1")

[node name="FloorFull571" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull572" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull573" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull653" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull654" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull655" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull656" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull657" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 62)
material_override = ExtResource("17_86ax1")

[node name="FloorFull658" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull659" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull660" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull661" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull662" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull574" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 66)
material_override = ExtResource("17_86ax1")

[node name="FloorFull663" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull666" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull667" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull668" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull669" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull670" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 70)
material_override = ExtResource("17_86ax1")

[node name="FloorFull871" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -42, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull872" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull873" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -66, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull874" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -58, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull875" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull876" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull877" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull878" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 74)
material_override = ExtResource("17_86ax1")

[node name="FloorFull287" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull288" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull373" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull374" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull375" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull376" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull291" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull292" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull293" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull294" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull295" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull296" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull297" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull298" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull299" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull300" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull301" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull302" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull308" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull428" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull429" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull430" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull431" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull432" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull435" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull436" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull437" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull438" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull439" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull440" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull442" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull443" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull444" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull445" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull446" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull447" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull448" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull449" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull450" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull453" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull454" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull455" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull456" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull457" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull479" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull480" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull481" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull482" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull485" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull486" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull681" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull682" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 22)
material_override = ExtResource("2_lojfe")

[node name="FloorFull683" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull684" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 34)
material_override = ExtResource("2_lojfe")

[node name="FloorFull685" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull686" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 26)
material_override = ExtResource("2_lojfe")

[node name="FloorFull687" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull688" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 30)
material_override = ExtResource("2_lojfe")

[node name="FloorFull689" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 22)
material_override = ExtResource("2_lojfe")

[node name="FloorFull690" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 38)
material_override = ExtResource("2_lojfe")

[node name="FloorFull691" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull692" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 42)
material_override = ExtResource("2_lojfe")

[node name="FloorFull693" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -34, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull694" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 46)
material_override = ExtResource("2_lojfe")

[node name="FloorFull487" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull488" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull489" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull494" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull495" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull496" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull499" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull500" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull501" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull502" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull507" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull508" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull511" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull512" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull515" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull516" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull576" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull577" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull578" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 50)
material_override = ExtResource("2_lojfe")

[node name="FloorFull579" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull580" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull581" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull587" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull591" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull592" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull593" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull594" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull595" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull596" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull597" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull598" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="FloorFull599" parent="Ground" instance=ExtResource("2_3c11n")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 54)
material_override = ExtResource("2_lojfe")

[node name="GroundPathCorner" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -66, 0, 18)

[node name="GroundPathCorner5" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 58)

[node name="GroundPathCorner6" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 50, 0, 70)

[node name="GroundPathCorner8" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, -6)

[node name="GroundPathCorner9" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-1.19209e-07, 0, -2, 0, 2, 0, 2, 0, -1.19209e-07, 38, 0, -2)

[node name="GroundPathCorner10" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(2.94055e-07, 0, 2, 0, 2, 0, -2, 0, 2.94055e-07, 42, 0, -50)

[node name="GroundPathCorner11" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-2, 0, 2.06632e-07, 0, 2, 0, -2.06632e-07, 0, -2, -66, 0, -50)

[node name="GroundPathCorner4" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -38, 0, 54)

[node name="GroundPathCorner13" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -30, 0, 58)

[node name="GroundPathCorner7" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -42, 0, 62)

[node name="GroundPathCorner12" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -34, 0, 70)

[node name="GroundPathCorner2" parent="Ground" instance=ExtResource("18_gvb43")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 14)

[node name="GroundPathSide" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -62, 0, 18)

[node name="GroundPathSide2" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 18)

[node name="GroundPathSide6" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -58, 0, 14)

[node name="GroundPathSide123" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, 10)

[node name="GroundPathSide124" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, 6)

[node name="GroundPathSide125" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, 2)

[node name="GroundPathSide126" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -2)

[node name="GroundPathSide127" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -6)

[node name="GroundPathSide128" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -10)

[node name="GroundPathSide129" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -14)

[node name="GroundPathSide130" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -18)

[node name="GroundPathSide131" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -22)

[node name="GroundPathSide132" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -26)

[node name="GroundPathSide3" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 22)

[node name="GroundPathSide4" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 26)

[node name="GroundPathSide5" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 30)

[node name="GroundPathSide195" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 34)

[node name="GroundPathSide7" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 30)

[node name="GroundPathSide10" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 34)

[node name="GroundPathSide146" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 38)

[node name="GroundPathSide168" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 42)

[node name="GroundPathSide169" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 46)

[node name="GroundPathSide170" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 50)

[node name="GroundPathSide171" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 54)

[node name="GroundPathSide8" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 26)

[node name="GroundPathSide9" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -58, 0, 22)

[node name="GroundPathSide11" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -18, 0, 70)

[node name="GroundPathSide12" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -14, 0, 70)

[node name="GroundPathSide13" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -22, 0, 70)

[node name="GroundPathSide14" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -38, 0, 62)

[node name="GroundPathSide19" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 0, 70)

[node name="GroundPathSide20" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2, 0, 70)

[node name="GroundPathSide21" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -6, 0, 70)

[node name="GroundPathSide22" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -10, 0, 70)

[node name="GroundPathSide23" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 14, 0, 70)

[node name="GroundPathSide24" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 18, 0, 70)

[node name="GroundPathSide25" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 10, 0, 70)

[node name="GroundPathSide26" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 6, 0, 70)

[node name="GroundPathSide27" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 38, 0, 70)

[node name="GroundPathSide28" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 42, 0, 70)

[node name="GroundPathSide31" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 46, 0, 70)

[node name="GroundPathSide29" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 26, 0, 70)

[node name="GroundPathSide30" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 22, 0, 70)

[node name="GroundPathSide43" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 66)

[node name="GroundPathSide44" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 26)

[node name="GroundPathSide45" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 22)

[node name="GroundPathSide46" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 30)

[node name="GroundPathSide47" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 34)

[node name="GroundPathSide48" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 10)

[node name="GroundPathSide49" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 6)

[node name="GroundPathSide50" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 2)

[node name="GroundPathSide64" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 46, 0, -6)

[node name="GroundPathSide51" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 14)

[node name="GroundPathSide56" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, -2)

[node name="GroundPathSide52" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 18)

[node name="GroundPathSide53" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 10)

[node name="GroundPathSide54" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 14)

[node name="GroundPathSide55" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 6)

[node name="GroundPathSide57" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 2)

[node name="GroundPathSide63" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 3.49691e-07, 0, 2, 0, -3.49691e-07, 0, 2, 42, 0, -2)

[node name="GroundPathSide65" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -6)

[node name="GroundPathSide66" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -10)

[node name="GroundPathSide67" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -14)

[node name="GroundPathSide68" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -18)

[node name="GroundPathSide69" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -38)

[node name="GroundPathSide70" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -42)

[node name="GroundPathSide87" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -46)

[node name="GroundPathSide88" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -42)

[node name="GroundPathSide89" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -38)

[node name="GroundPathSide179" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -30)

[node name="GroundPathSide180" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -34)

[node name="GroundPathSide181" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -34)

[node name="GroundPathSide182" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -30)

[node name="GroundPathSide90" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -18)

[node name="GroundPathSide91" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -14)

[node name="GroundPathSide92" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -10)

[node name="GroundPathSide157" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -22)

[node name="GroundPathSide158" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 38, 0, -26)

[node name="GroundPathSide159" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -26)

[node name="GroundPathSide160" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-4.37114e-07, 0, 2, 0, 2, 0, -2, 0, -4.37114e-07, 42, 0, -22)

[node name="GroundPathSide71" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 26, 0, -46)

[node name="GroundPathSide72" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 22, 0, -46)

[node name="GroundPathSide73" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 18, 0, -46)

[node name="GroundPathSide74" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 14, 0, -46)

[node name="GroundPathSide75" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 10, 0, -46)

[node name="GroundPathSide76" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 6, 0, -46)

[node name="GroundPathSide77" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 2, 0, -46)

[node name="GroundPathSide78" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -2, 0, -46)

[node name="GroundPathSide79" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -6, 0, -46)

[node name="GroundPathSide80" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -10, 0, -46)

[node name="GroundPathSide81" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -14, 0, -46)

[node name="GroundPathSide82" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -18, 0, -46)

[node name="GroundPathSide83" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -22, 0, -46)

[node name="GroundPathSide84" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -26, 0, -46)

[node name="GroundPathSide85" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -54, 0, -46)

[node name="GroundPathSide86" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -58, 0, -46)

[node name="GroundPathSide93" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -10)

[node name="GroundPathSide110" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -6)

[node name="GroundPathSide94" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -14)

[node name="GroundPathSide95" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -26, 0, -50)

[node name="GroundPathSide96" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -22, 0, -50)

[node name="GroundPathSide97" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -26)

[node name="GroundPathSide137" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -38)

[node name="GroundPathSide138" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -38)

[node name="GroundPathSide139" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -42)

[node name="GroundPathSide140" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -42)

[node name="GroundPathSide98" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -14, 0, -50)

[node name="GroundPathSide111" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -58, 0, -50)

[node name="GroundPathSide112" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -62, 0, -50)

[node name="GroundPathSide113" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -54, 0, -50)

[node name="GroundPathSide114" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -18)

[node name="GroundPathSide115" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -22)

[node name="GroundPathSide172" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -30)

[node name="GroundPathSide176" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -62, 0, -34)

[node name="GroundPathSide177" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -34)

[node name="GroundPathSide178" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -30)

[node name="GroundPathSide118" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, 10)

[node name="GroundPathSide119" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, 14)

[node name="GroundPathSide120" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, 6)

[node name="GroundPathSide121" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, 2)

[node name="GroundPathSide122" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -2)

[node name="GroundPathSide116" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -18, 0, -50)

[node name="GroundPathSide144" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -30, 0, -46)

[node name="GroundPathSide149" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -34, 0, -46)

[node name="GroundPathSide150" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -38, 0, -46)

[node name="GroundPathSide151" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -38, 0, -50)

[node name="GroundPathSide154" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -42, 0, -46)

[node name="GroundPathSide155" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -42, 0, -50)

[node name="GroundPathSide197" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -46, 0, -46)

[node name="GroundPathSide198" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -46, 0, -50)

[node name="GroundPathSide199" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, -50, 0, -46)

[node name="GroundPathSide200" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -50, 0, -50)

[node name="GroundPathSide152" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -34, 0, -50)

[node name="GroundPathSide153" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -30, 0, -50)

[node name="GroundPathSide117" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(5.30489e-07, 0, -2, 0, 2, 0, 2, 0, 5.30489e-07, -66, 0, -46)

[node name="GroundPathSide99" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -10, 0, -50)

[node name="GroundPathSide100" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -6, 0, -50)

[node name="GroundPathSide101" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, -2, 0, -50)

[node name="GroundPathSide102" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 2, 0, -50)

[node name="GroundPathSide103" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 6, 0, -50)

[node name="GroundPathSide104" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 10, 0, -50)

[node name="GroundPathSide105" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 14, 0, -50)

[node name="GroundPathSide106" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 18, 0, -50)

[node name="GroundPathSide107" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 22, 0, -50)

[node name="GroundPathSide108" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 26, 0, -50)

[node name="GroundPathSide133" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 34, 0, -46)

[node name="GroundPathSide134" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 2.68221e-07, 0, 2, 0, -2.68221e-07, 0, 2, 30, 0, -46)

[node name="GroundPathSide135" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 30, 0, -50)

[node name="GroundPathSide136" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 34, 0, -50)

[node name="GroundPathSide109" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -4.43066e-07, 0, 2, 0, 4.43066e-07, 0, -2, 38, 0, -50)

[node name="GroundPathSide58" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 26)

[node name="GroundPathSide59" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 30)

[node name="GroundPathSide60" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 34)

[node name="GroundPathSide156" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 38)

[node name="GroundPathSide161" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 38)

[node name="GroundPathSide183" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 42)

[node name="GroundPathSide184" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 42)

[node name="GroundPathSide185" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 46)

[node name="GroundPathSide186" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 46)

[node name="GroundPathSide187" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 50)

[node name="GroundPathSide188" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 50)

[node name="GroundPathSide189" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 54)

[node name="GroundPathSide190" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 54)

[node name="GroundPathSide201" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 58)

[node name="GroundPathSide202" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 58)

[node name="GroundPathSide203" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 50, 0, 62)

[node name="GroundPathSide204" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 62)

[node name="GroundPathSide61" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 22)

[node name="GroundPathSide62" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 46, 0, 18)

[node name="GroundPathSide32" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 38, 0, 66)

[node name="GroundPathSide33" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 26, 0, 66)

[node name="GroundPathSide34" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 42, 0, 66)

[node name="GroundPathSide35" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 14, 0, 66)

[node name="GroundPathSide36" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 10, 0, 66)

[node name="GroundPathSide37" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 18, 0, 66)

[node name="GroundPathSide38" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 22, 0, 66)

[node name="GroundPathSide162" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 34, 0, 70)

[node name="GroundPathSide163" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 30, 0, 70)

[node name="GroundPathSide164" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 34, 0, 66)

[node name="GroundPathSide165" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 30, 0, 66)

[node name="GroundPathSide39" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -2, 0, 66)

[node name="GroundPathSide40" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -6, 0, 66)

[node name="GroundPathSide41" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 2, 0, 66)

[node name="GroundPathSide42" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, 6, 0, 66)

[node name="GroundPathSide15" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -18, 0, 66)

[node name="GroundPathSide16" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -22, 0, 66)

[node name="GroundPathSide166" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -26, 0, 70)

[node name="GroundPathSide191" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -30, 0, 70)

[node name="GroundPathSide192" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -34, 0, 66)

[node name="GroundPathSide167" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -26, 0, 66)

[node name="GroundPathSide193" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -30, 0, 62)

[node name="GroundPathSide194" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -34, 0, 58)

[node name="GroundPathSide17" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -14, 0, 66)

[node name="GroundPathSide18" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -10, 0, 66)

[node name="GroundPathSide141" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -50, 0, 58)

[node name="GroundPathSide143" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -54, 0, 58)

[node name="GroundPathSide145" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -50, 0, 54)

[node name="GroundPathSide142" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -46, 0, 58)

[node name="GroundPathSide147" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -46, 0, 54)

[node name="GroundPathSide148" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -42, 0, 54)

[node name="GroundPathCornerSmall" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(-2, 0, -3.01992e-07, 0, 2, 0, 3.01992e-07, 0, -2, -58, 0, 18)

[node name="GroundPathSide173" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 38)

[node name="GroundPathSide174" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 42)

[node name="GroundPathSide175" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 46)

[node name="GroundPathSide196" parent="Ground" instance=ExtResource("19_5vmb7")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -54, 0, 50)

[node name="GroundPathCornerSmall4" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(2, 0, 8.26528e-07, 0, 2, 0, -8.26528e-07, 0, 2, -38, 0, 58)

[node name="GroundPathCornerSmall13" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(2, 0, 8.26528e-07, 0, 2, 0, -8.26528e-07, 0, 2, -30, 0, 66)

[node name="GroundPathCornerSmall14" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(-2, 0, -1.00137e-06, 0, 2, 0, 1.00137e-06, 0, -2, -34, 0, 62)

[node name="GroundPathCornerSmall11" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(-2, 0, -1.00137e-06, 0, 2, 0, 1.00137e-06, 0, -2, -42, 0, 58)

[node name="GroundPathCornerSmall12" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(2, 0, 1.17622e-06, 0, 2, 0, -1.17622e-06, 0, 2, -54, 0, 54)

[node name="GroundPathCornerSmall5" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(-9.13951e-07, 0, 2, 0, 2, 0, -2, 0, -9.13951e-07, 46, 0, 66)

[node name="GroundPathCornerSmall7" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(-2, 0, -1.00137e-06, 0, 2, 0, 1.00137e-06, 0, -2, 38, 0, -46)

[node name="GroundPathCornerSmall10" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(9.53674e-07, 0, -2, 0, 2, 0, 2, 0, 9.53674e-07, -62, 0, -46)

[node name="GroundPathCornerSmall9" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(2, 0, 1.17622e-06, 0, 2, 0, -1.17622e-06, 0, 2, -62, 0, 14)

[node name="GroundPathCornerSmall6" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(-2, 0, -1.00137e-06, 0, 2, 0, 1.00137e-06, 0, -2, 46, 0, -2)

[node name="GroundPathCornerSmall8" parent="Ground" instance=ExtResource("20_gxqbw")]
transform = Transform3D(2, 0, 8.26528e-07, 0, 2, 0, -8.26528e-07, 0, 2, 42, 0, -6)

[node name="Water" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -6.29071, -4)
material_override = SubResource("ShaderMaterial_u7o4s")
mesh = SubResource("PlaneMesh_s3lv7")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.25673, 25.5625, -40)
shape = SubResource("BoxShape3D_hburm")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 27.7204, 25.5625, -17.8982)
shape = SubResource("BoxShape3D_7v71a")

[node name="CollisionShape3D10" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 35.8258, 25.5625, 28.0176)
shape = SubResource("BoxShape3D_2rrgf")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -44.0571, 25.5625, -12.4182)
shape = SubResource("BoxShape3D_l8e7f")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7.98912, 25.5625, -40.1003)
shape = SubResource("BoxShape3D_a6ja5")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -36.0509, 25.5625, 30.2626)
shape = SubResource("BoxShape3D_qcv6r")

[node name="CollisionShape3D6" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -39.8696, 25.5625, 15.8685)
shape = SubResource("BoxShape3D_c3fxy")

[node name="CollisionShape3D7" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -32.2563, 25.5625, 43.9696)
shape = SubResource("BoxShape3D_c3fxy")

[node name="CollisionShape3D8" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -28.0604, 25.5625, 47.8585)
shape = SubResource("BoxShape3D_c3fxy")

[node name="CollisionShape3D11" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 31.6612, 25.5625, 4.15732)
shape = SubResource("BoxShape3D_c3fxy")

[node name="CollisionShape3D9" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4.06008, 25.5625, 52.0667)
shape = SubResource("BoxShape3D_jwpe0")
