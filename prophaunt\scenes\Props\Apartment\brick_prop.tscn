[gd_scene load_steps=5 format=3 uid="uid://b3v2c3lcub5vj"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_6m4oi"]
[ext_resource type="PackedScene" uid="uid://bdhylo0782d6y" path="res://prophaunt/maps/Source/Apartment/Props/bricks.tscn" id="2_4hm2m"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.370924
height = 2.09535

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_ty8wq"]
data = PackedVector3Array(-0.1727, 0.1649, 0.4846, -0.1727, 0.1649, 0.732, -0.3869, 0.1649, 0.3609, -0.3869, 0.1649, 0.8557, -0.3869, 0.1649, 0.3609, -0.1727, 0.1649, 0.732, -0.6011, 0.1649, 0.4846, -0.3869, 0.1649, 0.3609, -0.3869, 0.1649, 0.8557, -0.6011, 0.1649, 0.732, -0.6011, 0.1649, 0.4846, -0.3869, 0.1649, 0.8557, -0.6725, 0, 0.4434, -0.3869, 0, 0.2785, -0.6011, 0.1649, 0.4846, -0.3869, 0.1649, 0.3609, -0.6011, 0.1649, 0.4846, -0.3869, 0, 0.2785, -0.1012, 0, 0.4434, -0.1012, 0, 0.7732, -0.1727, 0.1649, 0.4846, -0.1727, 0.1649, 0.732, -0.1727, 0.1649, 0.4846, -0.1012, 0, 0.7732, -0.3869, 0, 0.9381, -0.6725, 0, 0.7732, -0.3869, 0.1649, 0.8557, -0.6011, 0.1649, 0.732, -0.3869, 0.1649, 0.8557, -0.6725, 0, 0.7732, -0.3869, 0, 0.2785, -0.1012, 0, 0.4434, -0.3869, 0.1649, 0.3609, -0.1727, 0.1649, 0.4846, -0.3869, 0.1649, 0.3609, -0.1012, 0, 0.4434, -0.6725, 0, 0.4434, -0.6011, 0.1649, 0.4846, -0.6725, 0, 0.7732, -0.6011, 0.1649, 0.732, -0.6725, 0, 0.7732, -0.6011, 0.1649, 0.4846, -0.1012, 0, 0.7732, -0.3869, 0, 0.9381, -0.1727, 0.1649, 0.732, -0.3869, 0.1649, 0.8557, -0.1727, 0.1649, 0.732, -0.3869, 0, 0.9381, 0.0851, 0.1039, 0.6537, 0.0851, 0.1039, 0.8096, -0.0499, 0.1039, 0.5758, -0.0499, 0.1039, 0.8875, -0.0499, 0.1039, 0.5758, 0.0851, 0.1039, 0.8096, -0.1849, 0.1039, 0.6537, -0.0499, 0.1039, 0.5758, -0.0499, 0.1039, 0.8875, -0.1849, 0.1039, 0.8096, -0.1849, 0.1039, 0.6537, -0.0499, 0.1039, 0.8875, -0.2299, 0, 0.6277, -0.0499, 0, 0.5238, -0.1849, 0.1039, 0.6537, -0.0499, 0.1039, 0.5758, -0.1849, 0.1039, 0.6537, -0.0499, 0, 0.5238, 0.13, 0, 0.6277, 0.13, 0, 0.8355, 0.0851, 0.1039, 0.6537, 0.0851, 0.1039, 0.8096, 0.0851, 0.1039, 0.6537, 0.13, 0, 0.8355, -0.0499, 0, 0.9394, -0.2299, 0, 0.8355, -0.0499, 0.1039, 0.8875, -0.1849, 0.1039, 0.8096, -0.0499, 0.1039, 0.8875, -0.2299, 0, 0.8355, -0.0499, 0, 0.5238, 0.13, 0, 0.6277, -0.0499, 0.1039, 0.5758, 0.0851, 0.1039, 0.6537, -0.0499, 0.1039, 0.5758, 0.13, 0, 0.6277, -0.2299, 0, 0.6277, -0.1849, 0.1039, 0.6537, -0.2299, 0, 0.8355, -0.1849, 0.1039, 0.8096, -0.2299, 0, 0.8355, -0.1849, 0.1039, 0.6537, 0.13, 0, 0.8355, -0.0499, 0, 0.9394, 0.0851, 0.1039, 0.8096, -0.0499, 0.1039, 0.8875, 0.0851, 0.1039, 0.8096, -0.0499, 0, 0.9394, 0.5259, 0.4712, 0.1426, 0.4009, 0.589, 0.2676, 0.4426, 0.589, 0.0593, 0.6508, 0.589, 0.0177, 0.5259, 0.4712, 0.1426, 0.4426, 0.589, 0.0593, 0.2829, 0.2945, 0.3857, 0.9076, 0.2945, -0.2391, -0.1336, 0.2945, -0.0308, 0.4911, 0.2945, -0.6555, -0.1336, 0.2945, -0.0308, 0.9076, 0.2945, -0.2391, 0.9076, 0.589, -0.2391, 0.9076, 0.2945, -0.2391, 0.6508, 0.589, 0.0177, 0.5259, 0.4712, 0.1426, 0.6508, 0.589, 0.0177, 0.9076, 0.2945, -0.2391, 0.2829, 0.2945, 0.3857, 0.5259, 0.4712, 0.1426, 0.9076, 0.2945, -0.2391, 0.4009, 0.589, 0.2676, 0.5259, 0.4712, 0.1426, 0.2829, 0.2945, 0.3857, 0.2829, 0.589, 0.3857, 0.4009, 0.589, 0.2676, 0.2829, 0.2945, 0.3857, 0.2829, 0.2945, 0.3857, -0.1336, 0.2945, -0.0308, 0.2829, 0.589, 0.3857, -0.1336, 0.589, -0.0308, 0.2829, 0.589, 0.3857, -0.1336, 0.2945, -0.0308, 0.4911, 0.2945, -0.6555, 0.4911, 0.589, -0.6555, -0.1336, 0.2945, -0.0308, -0.1336, 0.589, -0.0308, -0.1336, 0.2945, -0.0308, 0.4911, 0.589, -0.6555, 0.4911, 0.2945, -0.6555, 0.9076, 0.2945, -0.2391, 0.4911, 0.589, -0.6555, 0.9076, 0.589, -0.2391, 0.4911, 0.589, -0.6555, 0.9076, 0.2945, -0.2391, 0.9076, 0.589, -0.2391, 0.6508, 0.589, 0.0177, 0.4911, 0.589, -0.6555, 0.4426, 0.589, 0.0593, 0.4911, 0.589, -0.6555, 0.6508, 0.589, 0.0177, 0.4911, 0.589, -0.6555, 0.4426, 0.589, 0.0593, -0.1336, 0.589, -0.0308, 0.4426, 0.589, 0.0593, 0.4009, 0.589, 0.2676, -0.1336, 0.589, -0.0308, 0.2829, 0.589, 0.3857, -0.1336, 0.589, -0.0308, 0.4009, 0.589, 0.2676, -0.6682, 0.589, -0.1382, -0.7566, 0.4712, -0.2912, -0.6546, 0.589, -0.3501, -0.6546, 0.589, -0.3501, -0.7566, 0.4712, -0.2912, -0.8449, 0.589, -0.4443, -0.6149, 0, -0.0458, 0.1502, 0, -0.4876, -0.9094, 0, -0.5559, -0.1443, 0, -0.9977, -0.9094, 0, -0.5559, 0.1502, 0, -0.4876, 0.1502, 0.589, -0.4876, 0.1502, 0, -0.4876, -0.6149, 0.589, -0.0458, -0.6149, 0, -0.0458, -0.6149, 0.589, -0.0458, 0.1502, 0, -0.4876, -0.6149, 0, -0.0458, -0.9094, 0, -0.5559, -0.6149, 0.589, -0.0458, -0.7566, 0.4712, -0.2912, -0.6149, 0.589, -0.0458, -0.9094, 0, -0.5559, -0.6682, 0.589, -0.1382, -0.6149, 0.589, -0.0458, -0.7566, 0.4712, -0.2912, -0.9094, 0, -0.5559, -0.9094, 0.589, -0.5559, -0.7566, 0.4712, -0.2912, -0.8449, 0.589, -0.4443, -0.7566, 0.4712, -0.2912, -0.9094, 0.589, -0.5559, -0.1443, 0, -0.9977, -0.1443, 0.589, -0.9977, -0.9094, 0, -0.5559, -0.9094, 0.589, -0.5559, -0.9094, 0, -0.5559, -0.1443, 0.589, -0.9977, -0.1443, 0, -0.9977, 0.1502, 0, -0.4876, -0.1443, 0.589, -0.9977, 0.1502, 0.589, -0.4876, -0.1443, 0.589, -0.9977, 0.1502, 0, -0.4876, 0.1502, 0.589, -0.4876, -0.6149, 0.589, -0.0458, -0.1443, 0.589, -0.9977, -0.6682, 0.589, -0.1382, -0.1443, 0.589, -0.9977, -0.6149, 0.589, -0.0458, -0.6546, 0.589, -0.3501, -0.1443, 0.589, -0.9977, -0.6682, 0.589, -0.1382, -0.8449, 0.589, -0.4443, -0.1443, 0.589, -0.9977, -0.6546, 0.589, -0.3501, -0.9094, 0.589, -0.5559, -0.1443, 0.589, -0.9977, -0.8449, 0.589, -0.4443, -0.1786, 0.589, -0.2951, 0.362, 0.589, -0.1015, -0.0496, 0.589, -0.6556, 0.4911, 0.589, -0.462, -0.0496, 0.589, -0.6556, 0.362, 0.589, -0.1015, -0.1786, 0.9718, -0.2951, -0.0496, 0.9718, -0.6556, 0.362, 0.9718, -0.1015, 0.4911, 0.9718, -0.462, 0.362, 0.9718, -0.1015, -0.0496, 0.9718, -0.6556, 0.362, 0.9718, -0.1015, 0.362, 0.589, -0.1015, -0.1786, 0.9718, -0.2951, -0.1786, 0.589, -0.2951, -0.1786, 0.9718, -0.2951, 0.362, 0.589, -0.1015, -0.1786, 0.589, -0.2951, -0.0496, 0.589, -0.6556, -0.1786, 0.9718, -0.2951, -0.0496, 0.9718, -0.6556, -0.1786, 0.9718, -0.2951, -0.0496, 0.589, -0.6556, 0.4911, 0.589, -0.462, 0.4911, 0.9718, -0.462, -0.0496, 0.589, -0.6556, -0.0496, 0.9718, -0.6556, -0.0496, 0.589, -0.6556, 0.4911, 0.9718, -0.462, 0.4911, 0.589, -0.462, 0.362, 0.589, -0.1015, 0.4911, 0.9718, -0.462, 0.362, 0.9718, -0.1015, 0.4911, 0.9718, -0.462, 0.362, 0.589, -0.1015, 0.4121, 0, 0.9977, 0.9094, 0, 0.7105, 0.2207, 0, 0.6661, 0.718, 0, 0.379, 0.2207, 0, 0.6661, 0.9094, 0, 0.7105, 0.4121, 0.3828, 0.9977, 0.2207, 0.3828, 0.6661, 0.9094, 0.3828, 0.7105, 0.718, 0.3828, 0.379, 0.9094, 0.3828, 0.7105, 0.2207, 0.3828, 0.6661, 0.9094, 0.3828, 0.7105, 0.9094, 0, 0.7105, 0.4121, 0.3828, 0.9977, 0.4121, 0, 0.9977, 0.4121, 0.3828, 0.9977, 0.9094, 0, 0.7105, 0.4121, 0, 0.9977, 0.2207, 0, 0.6661, 0.4121, 0.3828, 0.9977, 0.2207, 0.3828, 0.6661, 0.4121, 0.3828, 0.9977, 0.2207, 0, 0.6661, 0.718, 0, 0.379, 0.718, 0.3828, 0.379, 0.2207, 0, 0.6661, 0.2207, 0.3828, 0.6661, 0.2207, 0, 0.6661, 0.718, 0.3828, 0.379, 0.718, 0, 0.379, 0.9094, 0, 0.7105, 0.718, 0.3828, 0.379, 0.9094, 0.3828, 0.7105, 0.718, 0.3828, 0.379, 0.9094, 0, 0.7105, 0.7025, 0, 0.2326, 0.7964, 0, -0.6459, 0.1169, 0, 0.17, 0.2108, 0, -0.7085, 0.1169, 0, 0.17, 0.7964, 0, -0.6459, 0.7964, 0.2945, -0.6459, 0.7025, 0.2945, 0.2326, 0.2108, 0.2945, -0.7085, 0.1169, 0.2945, 0.17, 0.2108, 0.2945, -0.7085, 0.7025, 0.2945, 0.2326, 0.7964, 0.2945, -0.6459, 0.7964, 0, -0.6459, 0.7025, 0.2945, 0.2326, 0.7025, 0, 0.2326, 0.7025, 0.2945, 0.2326, 0.7964, 0, -0.6459, 0.7025, 0, 0.2326, 0.1169, 0, 0.17, 0.7025, 0.2945, 0.2326, 0.1169, 0.2945, 0.17, 0.7025, 0.2945, 0.2326, 0.1169, 0, 0.17, 0.2108, 0, -0.7085, 0.2108, 0.2945, -0.7085, 0.1169, 0, 0.17, 0.1169, 0.2945, 0.17, 0.1169, 0, 0.17, 0.2108, 0.2945, -0.7085, 0.2108, 0, -0.7085, 0.7964, 0, -0.6459, 0.2108, 0.2945, -0.7085, 0.7964, 0.2945, -0.6459, 0.2108, 0.2945, -0.7085, 0.7964, 0, -0.6459, -0.8347, 0, 0.4182, -0.28, 0, 0.5669, -0.7357, 0, 0.0484, -0.1809, 0, 0.1971, -0.7357, 0, 0.0484, -0.28, 0, 0.5669, -0.8347, 0.3828, 0.4182, -0.7357, 0.3828, 0.0484, -0.28, 0.3828, 0.5669, -0.1809, 0.3828, 0.1971, -0.28, 0.3828, 0.5669, -0.7357, 0.3828, 0.0484, -0.28, 0.3828, 0.5669, -0.28, 0, 0.5669, -0.8347, 0.3828, 0.4182, -0.8347, 0, 0.4182, -0.8347, 0.3828, 0.4182, -0.28, 0, 0.5669, -0.8347, 0, 0.4182, -0.7357, 0, 0.0484, -0.8347, 0.3828, 0.4182, -0.7357, 0.3828, 0.0484, -0.8347, 0.3828, 0.4182, -0.7357, 0, 0.0484, -0.1809, 0, 0.1971, -0.1809, 0.3828, 0.1971, -0.7357, 0, 0.0484, -0.7357, 0.3828, 0.0484, -0.7357, 0, 0.0484, -0.1809, 0.3828, 0.1971, -0.1809, 0, 0.1971, -0.28, 0, 0.5669, -0.1809, 0.3828, 0.1971, -0.28, 0.3828, 0.5669, -0.1809, 0.3828, 0.1971, -0.28, 0, 0.5669)

[node name="BricksProp" instance=ExtResource("1_6m4oi")]

[node name="Bricks" parent="Meshes" index="0" instance=ExtResource("2_4hm2m")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(-3.25917e-09, -0.0745611, 0.997216, 1, -4.37114e-08, 0, 4.35897e-08, 0.997216, 0.0745611, -0.456878, 0.266083, -0.0134408)
shape = SubResource("CapsuleShape3D_5q4rx")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(-3.25917e-09, -0.0745611, 0.997216, 1, -4.37114e-08, 0, 4.35897e-08, 0.997216, 0.0745611, 0.650318, 0.266083, -0.0134408)
shape = SubResource("CapsuleShape3D_5q4rx")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="2"]
shape = SubResource("ConcavePolygonShape3D_ty8wq")
