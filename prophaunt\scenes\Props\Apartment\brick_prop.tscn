[gd_scene load_steps=4 format=3 uid="uid://b3v2c3lcub5vj"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_6m4oi"]
[ext_resource type="PackedScene" uid="uid://bdhylo0782d6y" path="res://prophaunt/maps/Source/Apartment/Props/bricks.tscn" id="2_4hm2m"]

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_7e5cx"]
points = PackedVector3Array(0.909231, 0.382799, 0.710357, 0.411976, 0.382799, 0.997663, 0.909231, -7.53215e-08, 0.710357, 0.907451, 0.588944, -0.239259, 0.491024, 0.971839, -0.462113, 0.361948, 0.971839, -0.101563, -0.834812, 0.382799, 0.418167, -0.387051, 0.164859, 0.85567, -0.387051, -7.53215e-08, 0.938093, 0.411976, -7.53215e-08, 0.997663, -0.178748, 0.971839, -0.295119, 0.796356, -7.53215e-08, -0.646098, 0.907451, 0.294424, -0.239259, 0.796356, 0.294424, -0.646098, -0.144387, 0.588944, -0.997663, -0.0496721, 0.971743, -0.655669, -0.834812, -7.53215e-08, 0.418167, -0.672621, -7.53215e-08, 0.773052, -0.601228, 0.164859, 0.731841, -0.909409, 0.588944, -0.556059, -0.144387, -7.53215e-08, -0.997663, -0.909409, -7.53215e-08, -0.556059)

[node name="BricksProp" instance=ExtResource("1_6m4oi")]

[node name="Bricks" parent="Meshes" index="0" instance=ExtResource("2_4hm2m")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
shape = SubResource("ConvexPolygonShape3D_7e5cx")
