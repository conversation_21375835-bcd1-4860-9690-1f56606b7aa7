[gd_scene load_steps=4 format=4 uid="uid://c1jpkw52gx3g2"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_sjosw"]

[sub_resource type="ArrayMesh" id="ArrayMesh_jxg3r"]
_surfaces = [{
"aabb": AABB(-0.5, 0, -0.5, 1, 4.8, 1),
"format": 34359742465,
"index_count": 114,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIAAwAFAAQABQAGAAQABQAHAAYACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADQAPAA4ACwACAAQAAAACAAsABAANAAsACwAJAAAADwANAAQAAQAAAAkABAAGAA8ACQAIAAEADgAPAAYABgAHAA4AAwABAAgACAAKAAMADAAOAAcAAwAKAAwABwAFAAwADAAFAAMAEgAQABEAEQATABIAFgAUABUAFQAXABYAEQAQABUAFQAUABEAFgAXABIAEgATABYAEQAUABYAFgATABEA"),
"lods": [0.0830831, PackedByteArray("AwAEAAAAAwAFAAQACwAMAAgACwANAAwADQAOAAwACwAAAAQABAANAAsAAwAAAAsACwAIAAMADgANAAQABAAFAA4ADAAOAAUADAAFAAMAAwAIAAwAEgAQABEAEQATABIAFgAUABUAFQAXABYAEQAQABUAFQAUABEAFgAXABIAEgATABYAEQAUABYAFgATABEA"), 0.623231, PackedByteArray("AwAEAAwABAADAAwAEgAQABEAEQATABIAFgAUABUAFQAXABYAEQAQABUAFQAUABEAFgAXABIAEgATABYAEQAUABYAFgATABEA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("AAAAP83MTD/NzMy+AAAAP83MTD/NzMw+zczMPs3MTD8AAAC/zczMPs3MTD8AAAA/zczMvs3MTD8AAAC/zczMvs3MTD8AAAA/AAAAv83MTD/NzMy+AAAAv83MTD/NzMw+AAAAPwAAAADNzMw+AAAAPwAAAADNzMy+zczMPgAAAAAAAAA/zczMPgAAAAAAAAC/zczMvgAAAAAAAAA/zczMvgAAAAAAAAC/AAAAvwAAAADNzMw+AAAAvwAAAADNzMy+zczMvs3MTD/NzMy+zczMvpqZmUDNzMy+zczMvs3MTD/NzMw+zczMvpqZmUDNzMw+zczMPpqZmUDNzMy+zczMPs3MTD/NzMy+zczMPpqZmUDNzMw+zczMPs3MTD/NzMw+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_8m6nn"]
resource_name = "Column_column"
_surfaces = [{
"aabb": AABB(-0.5, 0, -0.5, 1, 4.8, 1),
"attribute_data": PackedByteArray("/QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpPyktnT75q3A/KS2dPvmrcD8pLZ0++atwPyktnT75q3A/KS2dPvmrcD8pLZ0++atwPyktnT75q3A/KS2dPvmrcD8pLZ0++atwPyktnT75q3A/KS2dPvmrcD8pLZ0++atwPyktnT75q3A/KS2dPvmrcD8pLZ0++atwPyktnT75q3A/KS2dPvmrcD8pLZ0++atwPyktnT75q3A/KS2dPvmrcD/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpP/0A2T4hOWk//QDZPiE5aT/9ANk+ITlpPw=="),
"format": 34359742487,
"index_count": 114,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIAAwAFAAQABQAGAAQABQAHAAYACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADQAPAA4AEgAQABEAEwAQABIAEQAUABIAEgAVABMAFgAUABEAFwATABUAEQAYABYAFQAZABcAGgAWABgAGAAbABoAHAAXABkAGQAdABwAHgAaABsAHAAdAB4AGwAfAB4AHgAfABwAIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIA"),
"lods": [0.0830831, PackedByteArray("AwAEAAAAAwAFAAQACwAMAAgACwANAAwADQAOAAwAPAA+ADoAOgAUADwAQwATAD0APQAZAEMAQQA/ADsAOwBGAEEARQBCAB8ARQAfAEQARABAAEUAIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIA"), 0.623231, PackedByteArray("NgA0ADgANQA3ADkAIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIA")],
"material": ExtResource("1_sjosw"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 71,
"vertex_data": PackedByteArray("AAAAP83MTD/NzMy+AAAAP83MTD/NzMw+zczMPs3MTD8AAAC/zczMPs3MTD8AAAA/zczMvs3MTD8AAAC/zczMvs3MTD8AAAA/AAAAv83MTD/NzMy+AAAAv83MTD/NzMw+AAAAPwAAAADNzMw+AAAAPwAAAADNzMy+zczMPgAAAAAAAAA/zczMPgAAAAAAAAC/zczMvgAAAAAAAAA/zczMvgAAAAAAAAC/AAAAvwAAAADNzMw+AAAAvwAAAADNzMy+zczMPs3MTD8AAAC/zczMvs3MTD8AAAC/zczMPgAAAAAAAAC/AAAAP83MTD/NzMy+zczMvgAAAAAAAAC/AAAAPwAAAADNzMy+AAAAvwAAAADNzMy+AAAAP83MTD/NzMw+AAAAv83MTD/NzMy+AAAAPwAAAADNzMw+AAAAvwAAAADNzMw+AAAAv83MTD/NzMw+zczMPs3MTD8AAAA/zczMPgAAAAAAAAA/zczMvgAAAAAAAAA/zczMvs3MTD8AAAA/zczMvs3MTD/NzMy+zczMvpqZmUDNzMy+zczMvs3MTD/NzMw+zczMvpqZmUDNzMw+zczMPpqZmUDNzMy+zczMPs3MTD/NzMy+zczMPpqZmUDNzMw+zczMPs3MTD/NzMw+zczMvs3MTD/NzMy+zczMPs3MTD/NzMy+zczMvpqZmUDNzMy+zczMPpqZmUDNzMy+zczMPs3MTD/NzMw+zczMvs3MTD/NzMw+zczMPpqZmUDNzMw+zczMvpqZmUDNzMw+zczMPpqZmUDNzMy+zczMPpqZmUDNzMw+zczMvpqZmUDNzMy+zczMvpqZmUDNzMw+zczMvs3MTD8AAAC/zczMvs3MTD8AAAC/zczMPs3MTD8AAAA/zczMPs3MTD8AAAA/zczMvgAAAAAAAAA/zczMvgAAAAAAAAA/zczMvs3MTD8AAAC/zczMvs3MTD8AAAC/zczMPgAAAAAAAAC/zczMPgAAAAAAAAC/AAAAP83MTD/NzMy+zczMvgAAAAAAAAC/AAAAPwAAAADNzMw+AAAAvwAAAADNzMw+AAAAvwAAAADNzMw+zczMPs3MTD8AAAA/zczMPs3MTD8AAAA/zczMvgAAAAAAAAA/zczMvs3MTD8AAAA//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7///4Ha////vwAAgdr///+///+B2v///7///32l////vwAAgdr///+///99pf///78AAH2l////v4Ha/3////8/AAB9pf///7+B2v9/////P30l/3////8/fSX/f////z99pf9/////P32l/3////8/gVr/f////z+BWv9/////PwAA/3////+/AAD/f////78AAP9/////vwAA/3////+/////f////7////9/////v////3////+/////f////7//////////v/////////+//////////7//////////v/9//3////8//3//f////z//f/9/////P/9//3////8//3///////7//f///////v/9///////+//3///////78/7Qkm////v8Vpb9j///+/Ge2TXv///z+KbDfL////P9i/XSv///8/L01mm////z8AAN7z////vwAAmof///+///+R+f///7///7mA////v///auD///+/AADOm////7/Yof9/////P0UI/3////8/qkH/f////z/E8P9/////P3eI/3////8/Xmb/f////z8mF/9/////Pw==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_jxg3r")

[node name="Column" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_8m6nn")
skeleton = NodePath("")
