[gd_scene load_steps=6 format=4 uid="uid://bn5hjcsfomaj4"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_13e80"]

[sub_resource type="ArrayMesh" id="ArrayMesh_sshc2"]
_surfaces = [{
"aabb": AABB(-2.39452, 0, -2.39452, 4.78903, 0.8, 4.78903),
"format": 34359742465,
"index_count": 252,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADQAMAAUABQAOAA0AEQAPABAAEAASABEAFAAMABMAEwAVABQAFwAWAA0ADQAOABcADQAYABMAEwAMAA0ABgAVABMAEwAEAAYAGgAZABcAFwAOABoAEwAYABoAGgAEABMADAAUAAcABwAFAAwAGgAYABsAGwAZABoAHgAcAB0AHQAfAB4ABAAaAA4ADgAFAAQACQAgACEAIQALAAkAIwAiAB0AHQAcACMACgAkACUAJQAIAAoAHgAfACYAJgAnAB4AIQAgACUAJQAkACEAEQASAAIAAgADABEAJgAiACMAIwAnACYAAQAAABAAEAAPAAEADQAWABsAGwAYAA0AFgAdACIAFgAfAB0AGwAWACIAHwAWABcAIgAZABsAJgAfABcAGQAiACYAJgAXABkACgALAAMAAwAkAAoACwARAAMAAwABACQAEQALACEAIQAkAAEADwARACEAAQAPACEAAAACACMAIwAQAAAAJwAjAAIAIwAcABAAAgASACcAHAAeABAAEgAeACcAEgAQAB4AIAAUABUAIAAHABQAJQAgABUAIAAJAAcAFQAGACUABgAHAAkACAAlAAYABgAJAAgAHwAmACIAIgAdAB8AKgAoACkAKQArACoA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 44,
"vertex_data": PackedByteArray("en+yP5qZmT56f7K/en+yP83MTD96f7K/en+yP5qZmT56f7I/en+yP83MTD96f7I/R0z/P5qZmT5HTP8/R0z/v5qZmT5HTP8/8HIMQJqZGT/wcgxA8HIMwJqZGT/wcgxAvT8ZQJqZGT+9PxlAvT8ZwJqZGT+9PxlAvT8ZQM3MTD+9PxlAvT8ZwM3MTD+9PxlAR0z/v5qZmT5HTP+/mZkJwM3MTD6ZmQnAmZkJwM3MTD6ZmQlAen+yv83MTD96f7K/en+yv5qZmT56f7K/en+yv83MTD96f7I/en+yv5qZmT56f7I/R0z/P5qZmT5HTP+/8HIMwJqZGT/wcgzA8HIMQJqZGT/wcgzAmZkJwKkTUCiZmQnAmZkJwKkTUCiZmQlAmZkJQM3MTD6ZmQnAmZkJQKkTUCiZmQlAmZkJQM3MTD6ZmQlAmZkJQKkTUCiZmQnAjZh+v5qZmT6MmH6/jZh+vwAAAACMmH6/jZh+v5qZmT6MmH4/jZh+vwAAAACMmH4/vT8ZwJqZGT+9PxnAvT8ZwM3MTD+9PxnAjZh+PwAAAACMmH6/jZh+P5qZmT6MmH6/vT8ZQM3MTD+9PxnAvT8ZQJqZGT+9PxnAjZh+PwAAAACMmH4/jZh+P5qZmT6MmH4/jZh+P83MTD6MmH6/jZh+P83MTD6MmH4/jZh+v83MTD6MmH6/jZh+v83MTD6MmH4/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_rp2af"]
resource_name = "RoofFlatSquare_roof-flat-square"
_surfaces = [{
"aabb": AABB(-2.39452, 0, -2.39452, 4.78903, 0.8, 4.78903),
"attribute_data": PackedByteArray("etWcPogdcT961Zw+pUxwP3rVnD6IHXE/etWcPqVMcD961Zw+iB1xP3rVnD6IHXE/etWcPjOgcD961Zw+M6BwP3rVnD4zoHA/etWcPjOgcD961Zw+pUxwP3rVnD6lTHA/etWcPogdcT961Zw+iB1xP3rVnD5PR3E/etWcPk9HcT961Zw+pUxwP3rVnD6IHXE/etWcPqVMcD961Zw+iB1xP3rVnD6IHXE/etWcPogdcT961Zw+M6BwP3rVnD4zoHA/etWcPt2acT961Zw+T0dxP3rVnD7dmnE/etWcPk9HcT961Zw+T0dxP3rVnD6IHXE/etWcPk9HcT961Zw+iB1xP3rVnD4zoHA/etWcPogdcT961Zw+M6BwP3rVnD6IHXE/etWcPt2acT961Zw+3ZpxP3rVnD5PR3E/etWcPk9HcT961Zw+T0dxP3rVnD5PR3E/etWcPogdcT961Zw+iB1xP3rVnD4zoHA/etWcPjOgcD961Zw+iB1xP3rVnD6IHXE/etWcPk9HcT961Zw+3ZpxP3rVnD5PR3E/etWcPt2acT961Zw+iB1xP3rVnD7dmnE/etWcPogdcT961Zw+3ZpxP3rVnD5PR3E/etWcPk9HcT961Zw+iB1xP3rVnD6IHXE/etWcPjOgcD961Zw+pUxwP3rVnD4zoHA/etWcPqVMcD961Zw+3ZpxP3rVnD7dmnE/etWcPogdcT961Zw+iB1xP3rVnD6lTHA/etWcPjOgcD961Zw+pUxwP3rVnD4zoHA/etWcPt2acT961Zw+3ZpxP3rVnD6IHXE/etWcPogdcT961Zw+M6BwP3rVnD4zoHA/etWcPqVMcD961Zw+pUxwP3rVnD6IHXE/etWcPogdcT961Zw+pUxwP3rVnD6lTHA/etWcPt2acT961Zw+iB1xP3rVnD7dmnE/etWcPogdcT961Zw+iB1xP3rVnD6IHXE/etWcPqVMcD961Zw+pUxwP3rVnD7dmnE/etWcPt2acT961Zw+T0dxP3rVnD5PR3E/etWcPt2acT961Zw+3ZpxP3rVnD7dmnE/etWcPt2acT961Zw+3ZpxP3rVnD7dmnE/etWcPt2acT961Zw+3ZpxP3rVnD6lTHA/etWcPqVMcD961Zw+pUxwP3rVnD6lTHA/etWcPqVMcD961Zw+pUxwP3rVnD6lTHA/etWcPqVMcD961Zw+iB1xP3rVnD6IHXE/etWcPogdcT961Zw+iB1xP3rVnD6IHXE/etWcPogdcT961Zw+iB1xP3rVnD6IHXE/etWcPjOgcD961Zw+M6BwP3rVnD4zoHA/etWcPjOgcD961Zw+M6BwP3rVnD4zoHA/etWcPjOgcD961Zw+M6BwP3rVnD5PR3E/etWcPk9HcT961Zw+T0dxP3rVnD5PR3E/hnSdPmFkbT+GdJ0+YWRtP4Z0nT5hZG0/hnSdPmFkbT8="),
"format": 34359742487,
"index_count": 252,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYgBjAGAAZABiAGEAYwBiAGUAYQBmAGQAZwBjAGUAZgBhAGcAZwBlAGYAagBoAGkAaQBrAGoAaABsAGkAaQBtAGsAbABoAG4AbgBrAG0AbwBsAG4AbQBvAG4AcgBwAHEAcQBzAHIAdABxAHAAcQB1AHMAcAB2AHQAdQB3AHMAdgB3AHQAdgBzAHcAegB4AHkAegB7AHgAfAB6AHkAegB9AHsAeQB+AHwAfgB7AH0AfwB8AH4AfgB9AH8AggCAAIEAgQCDAIIAhgCEAIUAhQCHAIYA"),
"material": ExtResource("1_13e80"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 136,
"vertex_data": PackedByteArray("en+yP5qZmT56f7K/en+yP83MTD96f7K/en+yP5qZmT56f7I/en+yP83MTD96f7I/R0z/P5qZmT5HTP8/R0z/v5qZmT5HTP8/8HIMQJqZGT/wcgxA8HIMwJqZGT/wcgxAvT8ZQJqZGT+9PxlAvT8ZwJqZGT+9PxlAvT8ZQM3MTD+9PxlAvT8ZwM3MTD+9PxlAR0z/v5qZmT5HTP+/R0z/v5qZmT5HTP8/mZkJwM3MTD6ZmQnAmZkJwM3MTD6ZmQlAen+yv83MTD96f7K/en+yv5qZmT56f7K/en+yv83MTD96f7I/en+yv5qZmT56f7I/R0z/v5qZmT5HTP+/R0z/P5qZmT5HTP+/8HIMwJqZGT/wcgzA8HIMQJqZGT/wcgzAmZkJwKkTUCiZmQnAmZkJwM3MTD6ZmQnAmZkJwKkTUCiZmQlAmZkJwM3MTD6ZmQlAmZkJQM3MTD6ZmQnAR0z/P5qZmT5HTP+/mZkJwM3MTD6ZmQnAR0z/v5qZmT5HTP+/8HIMQJqZGT/wcgzAR0z/P5qZmT5HTP+/8HIMQJqZGT/wcgxAR0z/P5qZmT5HTP8/mZkJQKkTUCiZmQlAmZkJwKkTUCiZmQlAmZkJQM3MTD6ZmQlAmZkJwM3MTD6ZmQlAmZkJQM3MTD6ZmQnAmZkJQM3MTD6ZmQlAR0z/P5qZmT5HTP+/R0z/P5qZmT5HTP8/8HIMwJqZGT/wcgzA8HIMwJqZGT/wcgxAR0z/v5qZmT5HTP+/R0z/v5qZmT5HTP8/mZkJQM3MTD6ZmQnAmZkJQKkTUCiZmQnAmZkJQM3MTD6ZmQlAmZkJQKkTUCiZmQlAjZh+v5qZmT6MmH6/jZh+vwAAAACMmH6/jZh+v5qZmT6MmH4/jZh+vwAAAACMmH4/mZkJQM3MTD6ZmQlAmZkJwM3MTD6ZmQlAR0z/P5qZmT5HTP8/R0z/v5qZmT5HTP8/vT8ZwJqZGT+9PxnAvT8ZwM3MTD+9PxnAvT8ZwJqZGT+9PxlAvT8ZwM3MTD+9PxlAjZh+PwAAAACMmH6/jZh+vwAAAACMmH6/jZh+P5qZmT6MmH6/jZh+v5qZmT6MmH6/vT8ZQM3MTD+9PxnAvT8ZQJqZGT+9PxnAvT8ZQM3MTD+9PxlAvT8ZQJqZGT+9PxlAjZh+vwAAAACMmH4/jZh+PwAAAACMmH4/jZh+v5qZmT6MmH4/jZh+P5qZmT6MmH4/vT8ZwJqZGT+9PxnAvT8ZQJqZGT+9PxnAvT8ZwM3MTD+9PxnAvT8ZQM3MTD+9PxnAen+yv5qZmT56f7I/en+yP5qZmT56f7I/en+yv83MTD96f7I/en+yP83MTD96f7I/jZh+PwAAAACMmH6/jZh+P5qZmT6MmH6/jZh+PwAAAACMmH4/jZh+P5qZmT6MmH4/en+yP5qZmT56f7K/en+yv5qZmT56f7K/en+yP83MTD96f7K/en+yv83MTD96f7K/mZkJwKkTUCiZmQnAmZkJQKkTUCiZmQnAmZkJwM3MTD6ZmQnAmZkJQM3MTD6ZmQnAjZh+vwAAAACMmH6/jZh+PwAAAACMmH6/mZkJwKkTUCiZmQnAjZh+vwAAAACMmH4/mZkJQKkTUCiZmQnAmZkJwKkTUCiZmQlAmZkJQKkTUCiZmQlAjZh+PwAAAACMmH4/vT8ZwM3MTD+9PxlAen+yP83MTD96f7I/vT8ZQM3MTD+9PxlAvT8ZQM3MTD+9PxnAen+yv83MTD96f7I/en+yP83MTD96f7K/vT8ZwM3MTD+9PxnAen+yv83MTD96f7K/en+yP5qZmT56f7I/jZh+P5qZmT6MmH6/en+yP5qZmT56f7K/en+yv5qZmT56f7K/jZh+P5qZmT6MmH4/jZh+v5qZmT6MmH6/en+yv5qZmT56f7I/jZh+v5qZmT6MmH4/8HIMwJqZGT/wcgzA8HIMQJqZGT/wcgzAvT8ZwJqZGT+9PxnA8HIMwJqZGT/wcgxAvT8ZQJqZGT+9PxnAvT8ZwJqZGT+9PxlA8HIMQJqZGT/wcgxAvT8ZQJqZGT+9PxlAjZh+PwAAAACMmH4/jZh+PwAAAACMmH6/jZh+vwAAAACMmH4/jZh+vwAAAACMmH6/jZh+P83MTD6MmH6/jZh+P83MTD6MmH4/jZh+v83MTD6MmH6/jZh+v83MTD6MmH4/AAD/f////78AAP9/////vwAA/3////+/AAD/f////7//f8xM////P/9/zEz///8//3/MTP///z//f8xM////P/9//3////8//3//f////z+oqqqq////P1RVqKr///8/5k3mzf///7/mTebN////v84hzaH///+/ziHNof///7////9/////v////3////+/////f////7////9/////v8zMAAD///+/zMwAAP///7/MzAAA////v8zMAAD///+/AAD/f////7/OIc2h////vwAA/3////+/ziHNof///78w3v//////vxiy//////+/MN7//////78Ysv//////v8zMzEz///+/zMzMTP///7/MzMxM////v8zMzEz///+//3//f////z//f/9/////P/9/zaH///8//3/Nof///z8w3s2h////vzDezaH///+/GLLmzf///78YsubN////vzIzzEz///+/MjPMTP///78yM8xM////vzIzzEz///+/MN7Nof///7////9/////vzDezaH///+/////f////7////9/////v////3////+/////f////7////9/////v/9/zaH///8//3/Nof///z//f+bN////P/9/5s3///8/AAD/f////7+pKlPV////vwAA/3////+/VFWqqv///z//f/9/////P/9//3////8//3//f////z//f/9/////P1PVU9X///+/////f////7+qqqiq////P////3////+//////////7//////////v/////////+//////////7//////////v/////////+/qypV1f///79V1VPV////v/////////+//////////7//////////v/////////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////7//f/9/////P/9//3////8//3//f////z//f/9/////P/////////+//////////78w3v//////vzDe//////+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v1ZVqqr///8//3///////7+qqqqq////P1PVVdX///+//3///////7//f///////v6sqU9X///+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//3///////7//f///////v/9///////+//3///////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_sshc2")

[sub_resource type="BoxShape3D" id="BoxShape3D_mc7ex"]
size = Vector3(4.28809, 0.341663, 4.30115)

[sub_resource type="BoxShape3D" id="BoxShape3D_864if"]
size = Vector3(1.40942, 0.341663, 4.80368)

[node name="RoofFlatSquare" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_rp2af")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00244141, 0.156733, 0.00701904)
shape = SubResource("BoxShape3D_mc7ex")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.69446, 0.756592, 0.0182343)
shape = SubResource("BoxShape3D_864if")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.67685, 0.756592, 0.0182343)
shape = SubResource("BoxShape3D_864if")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0.00810766, 0.756592, 1.69825)
shape = SubResource("BoxShape3D_864if")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0.00810766, 0.756592, -1.70688)
shape = SubResource("BoxShape3D_864if")
