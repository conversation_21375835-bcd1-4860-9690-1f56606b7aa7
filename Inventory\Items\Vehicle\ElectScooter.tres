[gd_resource type="Resource" script_class="VehicleItem" load_steps=5 format=3 uid="uid://de03wbv31bxvc"]

[ext_resource type="PackedScene" uid="uid://bkop6titbax25" path="res://Scenes/Vehicle/Scenes/ElectScooter/ClientElectScooter.tscn" id="1_76lda"]
[ext_resource type="PackedScene" uid="uid://bn7krkknfaslg" path="res://Scenes/Vehicle/Scenes/ElectScooter/ElectScooter.tscn" id="2_2c1fs"]
[ext_resource type="Texture2D" uid="uid://dd6qygc17pct8" path="res://Scenes/Vehicle/Scenes/ElectScooter/Electscooterrender.png" id="3_oyqfp"]
[ext_resource type="Script" path="res://Inventory/Items/Vehicle/VehicleItem.gd" id="4_m5sjt"]

[resource]
script = ExtResource("4_m5sjt")
VehicleScene = ExtResource("2_2c1fs")
ClientSyncScene = ExtResource("1_76lda")
image = ExtResource("3_oyqfp")
id = 12
name = "ElectScooter"
price = 0
type = "Food"
in_hand = true
usable = false
remove_on_ban = false
