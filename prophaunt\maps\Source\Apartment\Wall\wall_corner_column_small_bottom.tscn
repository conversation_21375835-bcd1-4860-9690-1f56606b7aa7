[gd_scene load_steps=5 format=4 uid="uid://cm3vlkfb24sul"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_gk4n2"]

[sub_resource type="ArrayMesh" id="ArrayMesh_wnmd6"]
_surfaces = [{
"aabb": AABB(4.33147e-15, 0, -1.73259e-14, 2.25, 4.8, 2.25),
"format": 34359742465,
"index_count": 180,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACQAIAAYABgAHAAkACQADAAEAAQAIAAkADAAKAAsACwANAAwADAANAA4ADgAPAAwAEgAQABEAEQATABIAFAATABEAEQAVABQAFgAUABUAFQAXABYAGAAWABcAFwAZABgAGAAZABoAGgAbABgAHAAbABoAGgAdABwAHgAcAB0AHQAfAB4AGQAXABUAFQARABkAEQAgABkAEAAgABEAIAAaABkAIAAfABoAHwAdABoAAQAAABIAEgATAAEAGAABABMAGAAIAAEAGAAGAAgAFgAYABMAEwAUABYAGAAbAAYABgAbAB4AGwAcAB4AHgAEAAYAAgADAAkACQAhAAIACQAHACEABwAFACEAIgAKAAwADAAjACIADAAPACMADwAkACMAJgAlACIAIgAjACYAJAAnACYAJgAjACQADgAnACQAJAAPAA4AJQALAAoACgAiACUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("////P83MTD8yM9M/mZkJQM3MTD8yM9M/////P5qZmUAyM9M/mZkJQJqZmUAyM9M/MjPTP83MTD////8/MjPTP5qZmUD///8/MjPTP83MTD+ZmQlAMjPTP5qZmUCZmQlAmZkJQM3MTD+ZmQlAmZkJQJqZmUCZmQlAAAAAQJqZmUC/DpyoAAAAQAAAAAC/DpyoAAAAQJqZmUAAAABAAAAAQAAAAAAAAABAvw6cJwAAAAAAAABAvw6cJ5qZmUAAAABA////P/Xc0CZmZsY/mZkJQPXc0CZmZsY/////P83MTD9mZsY/mZkJQM3MTD9mZsY/AAAQQM3MTD8yM9M/AAAQQPXc0CYyM9M/AAAQQM3MTD+ZmQlAAAAQQPXc0CaZmQlAmZkJQM3MTD8AABBAmZkJQPXc0CYAABBAMjPTP/Xc0CYAABBAMjPTP83MTD8AABBAZmbGP83MTD+ZmQlAZmbGP/Xc0CaZmQlAZmbGP83MTD////8/ZmbGP/Xc0Cb///8/////P/Xc0Cb///8/////P5qZmUD///8/ZmbmP5qZmUC/DpyoZmbmP5qZmUBmZuY/vw6cJ5qZmUBmZuY/ZmbmP6kT0CO/DpyoZmbmP6kT0CNmZuY/vw6cJ6kT0CNmZuY/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_8sws5"]
resource_name = "WallCornerColumnSmalBottom_wall-corner-column-small-bottom"
_surfaces = [{
"aabb": AABB(4.33147e-15, 0, -1.73259e-14, 2.25, 4.8, 2.25),
"attribute_data": PackedByteArray("fnOlPsO+cD9+c6U+w75wP35zpT7DvnA/fnOlPsO+cD9+c6U+w75wP35zpT7DvnA/fnOlPsO+cD9+c6U+w75wP35zpT7DvnA/fnOlPsO+cD9+c6U+w75wP35zpT7DvnA/fnOlPsO+cD9+c6U+w75wP35zpT7DvnA/fnOlPsO+cD857qQ+LjFsPznupD7f3W0/Oe6kPi4xbD857qQ+391tPznupD7f3W0/Oe6kPt/dbT857qQ+LjFsPznupD4uMWw/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPyltpT47BHE/KW2lPjsEcT8pbaU+OwRxPxB+pT4jsHA/EH6lPiOwcD8QfqU+I7BwPxB+pT4jsHA/EH6lPiOwcD8QfqU+I7BwPxLXpD5W8HA/EtekPlbwcD8S16Q+VvBwPxLXpD5W8HA/EtekPlbwcD8S16Q+VvBwP/oFpT77znE/+gWlPsYucD/6BaU++85xP/oFpT7GLnA/+gWlPvvOcT/6BaU++85xP/oFpT7GLnA/+gWlPsYucD/6BaU++85xP/oFpT7GLnA/+gWlPvvOcT/6BaU+xi5wP/oFpT77znE/+gWlPsYucD/6BaU++85xP/oFpT7GLnA/"),
"format": 34359742487,
"index_count": 180,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHAAbABkAGQAdABwAHgAcAB0AHQAfAB4AIAAeAB8AHwAhACAAIAAhACIAIgAjACAAJAAjACIAIgAlACQAJgAkACUAJQAnACYAKgAoACkAKQArACoAKwAsACoALQAsACsALAAuACoALAAvAC4ALwAwAC4AMwAxADIAMgA0ADMANQAzADQANQA2ADMANQA3ADYAOAA1ADQANAA5ADgANQA6ADcANwA6ADsAOgA8ADsAOwA9ADcAQAA+AD8APwBBAEAAPwBCAEEAQgBDAEEARgBEAEUARQBHAEYARQBIAEcASABJAEcATABKAEsASwBNAEwAUABOAE8ATwBRAFAAVABSAFMAUwBVAFQAWABWAFcAVwBZAFgA"),
"material": ExtResource("1_gk4n2"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 90,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_wnmd6")

[sub_resource type="BoxShape3D" id="BoxShape3D_33na6"]
size = Vector3(1.97979, 4.8, 0.2)

[node name="WallCornerColumnSmallBottom" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_8sws5")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.95, 2.4, 1.9)
shape = SubResource("BoxShape3D_33na6")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 1.90081, 2.4, 0.998197)
shape = SubResource("BoxShape3D_33na6")
