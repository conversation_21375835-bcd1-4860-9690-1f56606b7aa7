extends Control
class_name WeaponSelector

# Weapon Selector for ProphauntLobby
# Similar to VehicleGarage HUD but for weapons

# UI References
@onready var hud: ExitableControl = $HUD
@onready var weapon_texture: TextureRect = $HUD/WeaponTexture
@onready var name_label: Label = $HUD/WeaponTexture/NameLabel
@onready var damage_label: Label = $HUD/StatsPanel/VBoxContainer/DamageLabel
@onready var ammo_label: Label = $HUD/StatsPanel/VBoxContainer/AmmoLabel
@onready var type_label: Label = $HUD/StatsPanel/VBoxContainer/TypeLabel
@onready var lock_panel: Control = $HUD/Lock
@onready var select_button: Control = $HUD/SelectButton
@onready var purchase_button: Control = $HUD/Lock/VBoxContainer/PurchaseButton
@onready var price_label: Label = $HUD/Lock/VBoxContainer/PurchaseButton/price
@onready var loading: Control = $HUD/Loading
@onready var list_http_request: HTTPRequest = $ListHTTPRequest
@onready var purchase_http_request: HTTPRequest = $PurchaseHTTPRequest

# Data
var my_weapons_data = []
var weapons_data = []
var selected_weapon_id = 15  # Default pistol ID
var selected_weapon_index = 0
var to_select_id = -1

# Signals
signal weapon_selected(weapon_id: int, weapon_icon: Texture2D)


func _ready():
	# Hide initially
	hud.visible = false
	hud.process_mode = Node.PROCESS_MODE_DISABLED
	
	# Connect signals
	list_http_request.request_completed.connect(_on_list_http_request_completed)
	purchase_http_request.request_completed.connect(_on_purchase_http_request_completed)


func show_selector():
	"""Show the weapon selector"""
	hud.visible = true
	hud.process_mode = Node.PROCESS_MODE_INHERIT
	to_select_id = -1
	loading.visible = false
	send_list_request()


func hide_selector():
	"""Hide the weapon selector"""
	hud.visible = false
	hud.process_mode = Node.PROCESS_MODE_DISABLED


func send_list_request():
	"""Send request to get weapon data"""
	loading.visible = true
	
	var url = Constants.BACKEND_URL + "/prophunt/weapons/list/"
	
	var data = {}
	var json = JSON.stringify(data)
	
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	list_http_request.cancel_request()
	list_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_list_http_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray):
	"""Handle weapon list response"""
	if response_code != 200:
		hide_selector()
		return
	
	var json = JSON.parse_string(body.get_string_from_utf8())
	my_weapons_data = json.get("my_weapons", [])
	weapons_data = json.get("weapons", [])
	#print(my_weapons_data)
	#print(weapons_data)
	for w in weapons_data:
		w["weapon_item"] = load(w["resource_path"])
	
	# Select initial weapon
	if to_select_id == -1:
		var id = Selector.my_prophaunt_item_data["current_selected_weapon"]["id"]
		
		select(id)
	else:
		select(to_select_id)
	
	loading.visible = false


func select(weapon_id: int):
	print("select: ", weapon_id)
	"""Select a weapon by ID"""
	var weapon_item: ProphauntGunItem
	var weapon_data = null
	
	for w in weapons_data:
		if w["id"] == weapon_id:
			weapon_item = w["weapon_item"]
			weapon_data = w
	
	# Update display
	weapon_texture.texture = weapon_item.icon
	name_label.text = tr(weapon_item.name)
	damage_label.text = tr("DAMAGE") + ": " + str(weapon_item.damage)
	ammo_label.text = tr("AMMO") + ": " + str(weapon_item.ammo)
	type_label.text = tr("TYPE") + ": " + weapon_item.gun_type.to_upper()
	
	selected_weapon_id = weapon_id
	selected_weapon_index = find_index_by_weapon_id(weapon_id)
	
	# Check ownership
	if find_my_weapon_data(weapon_id) == null:
		# Not owned - show purchase option
		lock_panel.visible = true
		select_button.visible = false
		price_label.text = str(weapon_data["price"])
	else:
		# Owned - show select option
		lock_panel.visible = false
		select_button.visible = true


func find_my_weapon_data(id: int):
	"""Find weapon in player's owned weapons"""
	for weapon in my_weapons_data:
		if weapon["weapon"]["id"] == id:
			return weapon
	return null


func find_index_by_weapon_id(weapon_id: int) -> int:
	"""Find weapon index in weapons_data"""
	var index = 0
	for weapon in weapons_data:
		if weapon["id"] == weapon_id:
			return index
		index += 1
	return 0


func _on_next_button_pressed():
	"""Navigate to next weapon"""
	if selected_weapon_index >= weapons_data.size() - 1:
		select(weapons_data[0]["id"])
	else:
		select(weapons_data[selected_weapon_index + 1]["id"])


func _on_previous_button_pressed():
	"""Navigate to previous weapon"""
	if selected_weapon_index <= 0:
		select(weapons_data[weapons_data.size() - 1]["id"])
	else:
		select(weapons_data[selected_weapon_index - 1]["id"])


func _on_purchase_button_pressed():
	"""Purchase the selected weapon"""
	send_purchase_request()


func send_purchase_request():
	"""Send weapon purchase request"""
	loading.visible = true
	var url = Constants.BACKEND_URL + "/prophunt/weapons/purchase/"
	var data = {
		"weapon_id": selected_weapon_id,
		"type": "full"
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	purchase_http_request.cancel_request()
	purchase_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_purchase_http_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray):
	"""Handle purchase response"""
	loading.visible = false
	if response_code == 400:
		Constants.show_toast(tr("NOT_ENOUGH_COIN"))
	elif response_code == 401:
		Constants.show_toast(str(response_code))
	elif response_code == 200:
		to_select_id = selected_weapon_id
		var json = JSON.parse_string(body.get_string_from_utf8())
		Selector.my_coin = json["remaining_coins"]
		send_list_request()
	else:
		Constants.show_toast(str(response_code))
		print_debug("Purchase failed: ", response_code)


func _on_select_button_pressed():
	"""Select the weapon and close selector"""
	var weapon_item = ProphauntWeaponManager.get_weapon_by_id(selected_weapon_id)
	if weapon_item:
		weapon_selected.emit(selected_weapon_id, weapon_item.icon)
	hide_selector()


func _on_exit_pressed():
	"""Handle exit button"""
	hide_selector()
