[gd_scene load_steps=6 format=4 uid="uid://br6rhl3i8oatn"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_gfesl"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_r2ipj"]
resource_name = "colormap"
cull_mode = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_nuwet"]
_surfaces = [{
"aabb": AABB(-0.1, 0, -2, 0.2, 4.8, 4),
"format": 34359742465,
"index_count": 30,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABQAEAAIAAgADAAUAAAAGAAcABwABAAAAAQAHAAUABQADAAEABQAHAAYABgAEAAUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("0szMvT/NTDEAAADAyMzMvZqZmUAAAADA0szMvT/NTDEAAABAyMzMvZqZmUAAAABAyMzMPQAAAAAAAABA0szMPZqZmUAAAABAyMzMPQAAAAAAAADA0szMPZqZmUAAAADA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4kpbh"]
resource_name = "Wall_wall"
_surfaces = [{
"aabb": AABB(-0.1, 0, -2, 0.2, 4.8, 4),
"attribute_data": PackedByteArray("xuicPpBobT/G6Jw+kGhtP8bonD6QaG0/xuicPpBobT+aMJ0+c5JxP5ownT5zknE/mjCdPo1dcD+aMJ0+jV1wP5ownT5zknE/mjCdPo1dcD+aMJ0+c5JxP5ownT6NXXA/ag+dPkA4cT9qD50+QDhxP2oPnT5AOHE/ag+dPkA4cT/G6Jw+kGhtP8bonD6QaG0/xuicPpBobT/G6Jw+kGhtPw=="),
"format": 34359742487,
"index_count": 30,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIA"),
"material": SubResource("StandardMaterial3D_r2ipj"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 20,
"vertex_data": PackedByteArray("0szMvT/NTDEAAADAyMzMvZqZmUAAAADA0szMvT/NTDEAAABAyMzMvZqZmUAAAABAyMzMPQAAAAAAAABA0szMvT/NTDEAAABA0szMPZqZmUAAAABAyMzMvZqZmUAAAABAyMzMPQAAAAAAAADA0szMPZqZmUAAAADA0szMvT/NTDEAAADAyMzMvZqZmUAAAADA0szMPZqZmUAAAADA0szMPZqZmUAAAABAyMzMvZqZmUAAAADAyMzMvZqZmUAAAABA0szMPZqZmUAAAADAyMzMPQAAAAAAAADA0szMPZqZmUAAAABAyMzMPQAAAAAAAABAAAD/f////78AAP9/////vwAA/3////+/AAD/f////7//f/9/////P/9//3////8//3//f////z//f/9/////P/////////+//////////7//////////v/////////+//3///////7//f///////v/9///////+//3///////7////9/////v////3////+/////f////7////9/////vw==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_nuwet")

[sub_resource type="BoxShape3D" id="BoxShape3D_43pop"]
size = Vector3(0.267578, 4.88647, 4.05957)

[node name="wall" type="MeshInstance3D"]
material_override = ExtResource("1_gfesl")
mesh = SubResource("ArrayMesh_4kpbh")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0107422, 2.40002, 0.00927734)
shape = SubResource("BoxShape3D_43pop")
