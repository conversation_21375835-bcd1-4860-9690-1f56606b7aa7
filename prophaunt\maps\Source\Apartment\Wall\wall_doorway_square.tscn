[gd_scene load_steps=6 format=4 uid="uid://csucnhe612l3m"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_te8r3"]

[sub_resource type="ArrayMesh" id="ArrayMesh_7osim"]
_surfaces = [{
"aabb": AABB(-0.201329, -1.44382e-14, -2, 0.4, 4.8, 4),
"format": 34359742465,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABgADAAUABgAFAAcACAAGAAcABwAJAAgACgAGAAgACAALAAoACwAMAA0ADQAKAAsADQAOAAoADwAKAA4ADwAOABAADwAQABEAEgAPABEAEQATABIAEgACAAMAAwAPABIAAwAGAAoACgAPAAMAFAAJAAcABwAVABQAFQAHAAUABQAWABUAFwAWAAUABQAEABcAGAAXAAQABAABABgAGAABAAAAAAAZABgAGwAaABMAEwARABsAGwARABAAEAAcABsAHQAcABAAEAAOAB0AHQAOAA0ADQAeAB0AHgANAAwADAAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAHgAfACcAJwAlAB4AJQAiAB4AGwAeACIAGwAdAB4AGwAcAB0AGgAbACIAIgAjABoAGAAZACEAIQAgABgAIAAkABgAFQAYACQAFQAXABgAFQAWABcAFQAkACYAJgAUABUAJQAkACAAIAAiACUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("IBTKPaH4L6jMzIy/IBTKPZmZiUDMzIy/MBTKPQCTSSYAAADAMBTKPZqZmUAAAADAIBTKPc3MjED+/3+/ABTKPc3MjEADAIA/8BPKPZqZmUACAABAABTKPZmZiUDQzIw/8BPKPQCTSSYCAABAABTKPfqgM6jQzIw/oIXPvZqZmUACAABAoIXPvaATUCYCAABAoIXPvQAQqabQzIw/oIXPvZmZiUDQzIw/oIXPvc3MjEADAIA/YIXPvZqZmUAAAADAgIXPvc3MjED+/3+/gIXPvZmZiUDMzIy/YIXPvaATUCYAAADAgIXPvWCPoqbMzIy/aHBLPkoMgqjQzIw/aHBLPpmZiUDQzIw/aHBLPs3MjEADAIA/eHBLPs3MjED+/3+/eHBLPpmZiUDMzIy/eHBLPveXfajMzIy/IClOvqATUCbMzIy/IClOvpmZiUDMzIy/IClOvs3MjED+/3+/MClOvs3MjEADAIA/MClOvpmZiUDQzIw/MClOvqATUCbQzIw/eHBLPmZmhkBkZma/eHBLPveXfahkZma/IClOvmZmhkBkZma/IClOvqATUCZkZma/aHBLPmZmhkBsZmY/MClOvmZmhkBsZmY/aHBLPveXfahsZmY/MClOvqATUCZsZmY/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_6tlxv"]
resource_name = "WallDoorwaySquare_wall-doorway-square"
_surfaces = [{
"aabb": AABB(-0.201329, -1.44382e-14, -2, 0.4, 4.8, 4),
"attribute_data": PackedByteArray("E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tPxP0pD40fm0/E/SkPjR+bT8T9KQ+NH5tP38ppT7Uv3E/fymlPhmAcD9/KaU+1L9xP38ppT4ZgHA/fymlPqp4cD9/KaU+qnhwP38ppT6qeHA/fymlPqp4cD9/KaU+GYBwP38ppT4ZgHA/fymlPtS/cT9/KaU+1L9xP38ppT7Uv3E/fymlPtS/cT9/KaU+GYBwP38ppT4ZgHA/fymlPqp4cD9/KaU+qnhwP38ppT6qeHA/fymlPqp4cD9/KaU+GYBwP38ppT4ZgHA/fymlPtS/cT9/KaU+1L9xP38ppT6Jh3A/fymlPtS/cT9/KaU+iYdwP38ppT7Uv3E/fymlPomHcD9/KaU+iYdwP38ppT7Uv3E/fymlPtS/cT9/KaU+1L9xP38ppT7Uv3E/fymlPhmAcD9/KaU+iYdwP38ppT6Jh3A/fymlPhmAcD9/KaU+qnhwP38ppT6qeHA/fymlPtS/cT9/KaU+1L9xP38ppT7Uv3E/fymlPtS/cT9/KaU+GYBwP38ppT6Jh3A/fymlPomHcD9/KaU+GYBwP38ppT6qeHA/fymlPqp4cD9/KaU+1L9xP38ppT7Uv3E/fymlPomHcD9/KaU+iYdwP38ppT6Jh3A/fymlPomHcD8="),
"format": 34359742487,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABgADAAUABgAFAAcACAAGAAcABwAJAAgADAAKAAsACwANAAwAEAAOAA8ADwARABAADwASABEAEwARABIAEwASABQAEwAUABUAFgATABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAIwAhACQAJAAlACMAJgAlACQAJAAnACYAKAAmACcAJwApACgAKAApACoAKgArACgALgAsAC0ALQAvAC4ALgAvADAAMAAxAC4AMgAxADAAMAAzADIAMgAzADQANAA1ADIANQA0ADYANgA3ADUAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIAQwBEAEIARQBCAEQARQBGAEIARQBHAEYASABFAEQARABJAEgATABKAEsASwBNAEwATQBOAEwATwBMAE4ATwBQAEwATwBRAFAATwBOAFIAUgBTAE8AVgBUAFUAVQBXAFYA"),
"material": ExtResource("1_te8r3"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 88,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_7osim")

[sub_resource type="BoxShape3D" id="BoxShape3D_f8k8r"]
size = Vector3(0.4, 4.7, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_sbdkd"]
size = Vector3(0.4, 4.01582, 0.564453)

[node name="WallDoorwaySquare" type="MeshInstance3D"]
transform = Transform3D(-1.19209e-07, 0, 1, 0, 1, 0, -1, 0, -1.19209e-07, 0, 0, 0)
mesh = SubResource("ArrayMesh_6tlxv")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.63912e-07, 2.45, 1.5)
shape = SubResource("BoxShape3D_f8k8r")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.93715e-07, 2.45, -1.5)
shape = SubResource("BoxShape3D_f8k8r")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, -1.19209e-07, 1.19209e-07, 1.19209e-07, -4.37114e-08, -1, 1.19209e-07, 1, -4.37114e-08, -5.22702e-09, 4.51895, -9.75132e-05)
shape = SubResource("BoxShape3D_sbdkd")
