extends Control

@onready var texture_rect = $TextureRect

@export var speed: float = 60
@export var color: Color = Color.WHITE
@export var force_process = false

func _ready() -> void:
	process_mode = PROCESS_MODE_DISABLED
	texture_rect.modulate = color
	if force_process:
		process_mode = PROCESS_MODE_ALWAYS
		return

func _process(delta):
	rotation += deg_to_rad(speed) * delta
	if rotation > 2 * PI:
		rotation -= 2 * PI


func _on_visibility_changed() -> void:
	if force_process:
		process_mode = PROCESS_MODE_ALWAYS
		return
	
	if visible:
		process_mode = PROCESS_MODE_INHERIT
	else:
		process_mode = PROCESS_MODE_DISABLED
