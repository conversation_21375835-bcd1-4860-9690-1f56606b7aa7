[gd_scene load_steps=5 format=4 uid="uid://ylxgfwkynh18"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_npk6v"]

[sub_resource type="ArrayMesh" id="ArrayMesh_gu4yy"]
_surfaces = [{
"aabb": AABB(4.33147e-15, 0, -1.73259e-14, 2.15, 4.8, 2.15548),
"format": 34359742465,
"index_count": 96,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACQAIAAYABgAHAAkACQADAAEAAQAIAAkADAAKAAsACwANAAwADAANAA4ADgAPAAwAAAAQAAEACAABABAAEAAGAAgAEAAEAAYAAgADAAkACQARAAIACQAHABEABwAFABEAEgAKAAwADAATABIADAAPABMADwAUABMAFgAVABIAEgATABYAFAAXABYAFgATABQADgAXABQAFAAPAA4AFQALAAoACgASABUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("////P9RKySLs5tM/mZkJQNRKySLs5tM/////P5qZmUDs5tM/mZkJQJqZmUDs5tM/MjPTP9RKySLcWQBAMjPTP5qZmUDcWQBAMjPTP9RKySJ28wlAMjPTP5qZmUB28wlAmZkJQNRKySJ28wlAmZkJQJqZmUB28wlAAAAAQJqZmUC/DpyoAAAAQAAAAAC/DpyoAAAAQJqZmUAAAABAAAAAQAAAAAAAAABAvw6cJwAAAAAAAABAvw6cJ5qZmUAAAABA////P9RKySLcWQBA////P5qZmUDcWQBAZmbmP5qZmUC/DpyoZmbmP5qZmUBmZuY/vw6cJ5qZmUBmZuY/ZmbmP6kT0CO/DpyoZmbmP6kT0CNmZuY/vw6cJ6kT0CNmZuY/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_oakik"]
resource_name = "WallCornerColumnSmall_wall-corner-column-small"
_surfaces = [{
"aabb": AABB(4.33147e-15, 0, -1.73259e-14, 2.15, 4.8, 2.15548),
"attribute_data": PackedByteArray("Hg+lPukNcT8eD6U+6Q1xPx4PpT7pDXE/Hg+lPukNcT8eD6U+6Q1xPx4PpT7pDXE/Hg+lPukNcT8eD6U+6Q1xPx4PpT7pDXE/Hg+lPukNcT8eD6U+6Q1xPx4PpT7pDXE/Hg+lPukNcT8eD6U+6Q1xPx4PpT7pDXE/Hg+lPukNcT8A0KQ+WNxtPwDQpD6oG2w/ANCkPljcbT8A0KQ+qBtsPwDQpD6oG2w/ANCkPqgbbD8A0KQ+WNxtPwDQpD5Y3G0/b9ekPtOgcD9v16Q+06BwP2/XpD7ToHA/b9ekPtOgcD9v16Q+06BwP2/XpD7ToHA/etOkPqGscT9606Q+oaxxP3rTpD6hrHE/etOkPqGscT9606Q+oaxxP3rTpD6hrHE/ZK6kPlRTcT9krqQ+VFNxP2SupD5UU3E/ZK6kPlRTcT9krqQ+VFNxP2SupD5UU3E/PSylPqvIbT89LKU+zSVsPz0spT6ryG0/PSylPs0lbD89LKU+q8htPz0spT6ryG0/PSylPs0lbD89LKU+zSVsP9WRpT6SznE/1ZGlPvhbcD/VkaU+ks5xP9WRpT74W3A/1ZGlPpLOcT/VkaU++FtwP9WRpT6SznE/1ZGlPvhbcD8="),
"format": 34359742487,
"index_count": 96,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGwAZABgAGAAcABsAGAAdABwAIAAeAB8AHwAhACAAHwAiACEAIgAjACEAJgAkACUAJQAnACYAJQAoACcAKAApACcALAAqACsAKwAtACwAMAAuAC8ALwAxADAANAAyADMAMwA1ADQAOAA2ADcANwA5ADgA"),
"material": ExtResource("1_npk6v"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 58,
"vertex_data": PackedByteArray("////P9RKySLs5tM/mZkJQNRKySLs5tM/////P5qZmUDs5tM/mZkJQJqZmUDs5tM/MjPTP9RKySLcWQBAMjPTP5qZmUDcWQBAMjPTP9RKySJ28wlAMjPTP5qZmUB28wlAmZkJQNRKySJ28wlAMjPTP9RKySJ28wlAmZkJQJqZmUB28wlAMjPTP5qZmUB28wlAmZkJQJqZmUDs5tM/mZkJQNRKySLs5tM/mZkJQJqZmUB28wlAmZkJQNRKySJ28wlAAAAAQJqZmUC/DpyoAAAAQAAAAAC/DpyoAAAAQJqZmUAAAABAAAAAQAAAAAAAAABAAAAAQAAAAAAAAABAvw6cJwAAAAAAAABAAAAAQJqZmUAAAABAvw6cJ5qZmUAAAABA////P9RKySLcWQBAmZkJQNRKySLs5tM/////P9RKySLs5tM/mZkJQNRKySJ28wlAMjPTP9RKySJ28wlAMjPTP9RKySLcWQBAmZkJQJqZmUDs5tM/mZkJQJqZmUB28wlA////P5qZmUDs5tM/////P5qZmUDcWQBAMjPTP5qZmUB28wlAMjPTP5qZmUDcWQBAAAAAQJqZmUC/DpyoAAAAQJqZmUAAAABAZmbmP5qZmUC/DpyoZmbmP5qZmUBmZuY/vw6cJ5qZmUAAAABAvw6cJ5qZmUBmZuY/ZmbmP6kT0CO/DpyoZmbmP5qZmUC/DpyoZmbmP6kT0CNmZuY/ZmbmP5qZmUBmZuY/vw6cJ6kT0CNmZuY/ZmbmP6kT0CNmZuY/vw6cJ5qZmUBmZuY/ZmbmP5qZmUBmZuY/vw6cJ6kT0CNmZuY/vw6cJ5qZmUBmZuY/vw6cJwAAAAAAAABAvw6cJ5qZmUAAAABAAAAAQAAAAAC/DpyoAAAAQJqZmUC/DpyoZmbmP6kT0CO/DpyoZmbmP5qZmUC/Dpyo/////////7//////////v/////////+//////////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////v/9//3////8//3//f////z//f/9/////P/9//3////8/////f////7////9/////v////3////+/////f////7////9/////v////3////+/////f////7////9/////v/9//3////8//3//f////z//f/9/////P/9//3////8//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////7//////////v/////////+//////////7//////////vwAA/3////+/AAD/f////78AAP9/////vwAA/3////+//////////7//////////v/////////+//////////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_gu4yy")

[sub_resource type="BoxShape3D" id="BoxShape3D_1qroa"]
size = Vector3(2, 4.8, 0.2)

[node name="WallCornerColumnSmall" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_oakik")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 2.4, 1.9)
shape = SubResource("BoxShape3D_1qroa")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 1.9, 2.4, 1)
shape = SubResource("BoxShape3D_1qroa")
