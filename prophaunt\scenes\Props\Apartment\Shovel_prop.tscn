[gd_scene load_steps=5 format=3 uid="uid://c3ek6xyh6w4hr"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_rnql7"]
[ext_resource type="PackedScene" uid="uid://cbn6v7njhux8y" path="res://Scenes/FreeRide/Assets/NetworkNodes/MiniGames/PvP/BattleHeroes/Weapon/Shovel.tscn" id="2_6njgu"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.0284635
height = 1.12792

[sub_resource type="BoxShape3D" id="BoxShape3D_fhvsn"]
size = Vector3(0.3391, 0.452663, 0.030243)

[node name="ShovelProp" instance=ExtResource("1_rnql7")]

[node name="Shovel" parent="Meshes" index="0" instance=ExtResource("2_6njgu")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1.91069e-15, 4.37114e-08, 1, 1, -4.37114e-08, 0, 4.37114e-08, 1, -4.37114e-08, 0.00131172, 0.00412726, 0.183717)
shape = SubResource("CapsuleShape3D_5q4rx")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(0.999806, -0.019686, -8.60504e-10, 0, -4.37114e-08, 1, -0.019686, -0.999806, -4.37029e-08, -0.00109291, 0.00665562, 0.954766)
shape = SubResource("BoxShape3D_fhvsn")
