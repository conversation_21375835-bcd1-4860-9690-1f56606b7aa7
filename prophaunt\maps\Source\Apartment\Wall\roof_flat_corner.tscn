[gd_scene load_steps=6 format=4 uid="uid://c4lyoyh22048g"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_qg7mg"]

[sub_resource type="ArrayMesh" id="ArrayMesh_hj52a"]
_surfaces = [{
"aabb": AABB(-2, -5.41434e-15, -2, 4.4, 0.8, 4.4),
"format": 34359742465,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEAAAAAIAAgARABAACwAJABIAEgATAAsAEwAUAAsAEwAVABQADQAMAAEAAQAAAA0AAAAQAA0AEAAEAA0AEAASAAQAEgAFAAQAEgAJAAUACQAIAAUADQAEAAYABgAPAA0ABgAWAA8AFgAXAA8AEgAQABEAEQATABIAGQAYABcAFwAWABkAFgAaABkAGgAbABkAGwAcABkAFQAbABoAGgAdABUAHQAUABUAAwAZABwAHAACAAMACwAUAB0AHQAKAAsAAwABAAwADAAOAAMADgAZAAMADgAYABkAGAAOAA8ADwAXABgAGgAWAAYABgAHABoAEQACABwAHAAbABEAEQAbABUAFQATABEABQAIAAoACgAHAAUACgAdAAcAHQAaAAcAGAAeAB8AHwAgABgAGAAgACEAIQAiABgAIQAgAB8AHwAjACEAIQAjACQAJAAiACEA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 37,
"vertex_data": PackedByteArray("dvMJQM3MTD4AAADAdvMJQDCaAiYAAADAdvMJQM3MTD528wlAdvMJQLRl0yd28wlANDOzP5qZmT4AAADANDOzP83MTD8AAADAMzOzP5qZmT40M7M/MzOzP83MTD80M7M/mpkZQM3MTD8AAADAmpkZQJqZGT8AAADAmpkZQM3MTD+amRlAmpkZQJqZGT+amRlAAACAPzCaAiYAAADAAACAP5qZmT4AAADA//9/P24Sw6cAAIA///9/P5qZmT4AAIA/AAAAQJqZmT4AAADAAAAAQJqZmT4AAABAzcwMQJqZGT8AAADAzcwMQJqZGT/NzAxAAAAAwJqZGT+amRlAAAAAwJqZGT/NzAxAAAAAwJqZmT40M7M/AAAAwJqZmT4AAIA/AAAAwAAAAAAAAIA/AAAAwAAAAAB28wlAAAAAwM3MTD80M7M/AAAAwJqZmT4AAABAAAAAwM3MTD528wlAAAAAwM3MTD+amRlAAACAPwAAAAAAAIA/AACAPwAAAAAAAADAAAAAwAAAAAAAAADAAAAAwM3MTD4AAADAAAAAwM3MTD4AAIA/AACAP83MTD4AAADAAACAP83MTD4AAIA/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_fcpsc"]
resource_name = "RoofFlatCorner_roof-flat-corner"
_surfaces = [{
"aabb": AABB(-2, -5.41434e-15, -2, 4.4, 0.8, 4.4),
"attribute_data": PackedByteArray("gfGcPqVdcT+B8Zw+L69xP4HxnD6lXXE/gfGcPi+vcT+B8Zw+4DRxP4HxnD4HaXA/gfGcPuA0cT+B8Zw+B2lwP4HxnD4HaXA/gfGcPpG6cD+B8Zw+B2lwP4HxnD6RunA/gfGcPi+vcT+B8Zw+4DRxP4HxnD4vr3E/gfGcPuA0cT+B8Zw+pV1xP4HxnD6lXXE/gfGcPuA0cT+B8Zw+4DRxP4HxnD6RunA/gfGcPpG6cD+B8Zw+kbpwP4HxnD6RunA/gfGcPpG6cD+B8Zw+kbpwP4HxnD4vr3E/gfGcPi+vcT+B8Zw+4DRxP4HxnD6lXXE/gfGcPuA0cT+B8Zw+4DRxP4HxnD6RunA/gfGcPgdpcD+B8Zw+kbpwP4HxnD4HaXA/gfGcPuA0cT+B8Zw+4DRxP4HxnD7gNHE/gfGcPuA0cT+B8Zw+4DRxP4HxnD7gNHE/gfGcPuA0cT+B8Zw+4DRxP4HxnD6RunA/gfGcPpG6cD+B8Zw+L69xP4HxnD7gNHE/gfGcPi+vcT+B8Zw+4DRxP4HxnD4HaXA/gfGcPuA0cT+B8Zw+pV1xP4HxnD6RunA/gfGcPgdpcD+B8Zw+kbpwP4HxnD4vr3E/gfGcPqVdcT+B8Zw+L69xP4HxnD6lXXE/gfGcPpG6cD+B8Zw+B2lwP4HxnD6RunA/gfGcPgdpcD+B8Zw+L69xP4HxnD4vr3E/gfGcPi+vcT+B8Zw+L69xP4HxnD4vr3E/gfGcPi+vcT+B8Zw+L69xP4HxnD7gNHE/gfGcPi+vcT+B8Zw+4DRxP4HxnD7gNHE/gfGcPuA0cT+B8Zw+B2lwP4HxnD4HaXA/gfGcPqVdcT+B8Zw+pV1xP4HxnD7gNHE/gfGcPuA0cT+B8Zw+4DRxP4HxnD6RunA/gfGcPuA0cT+B8Zw+kbpwP4HxnD4HaXA/gfGcPgdpcD+B8Zw+B2lwP4HxnD4HaXA/gfGcPgdpcD+B8Zw+B2lwP4HxnD6lXXE/gfGcPqVdcT+B8Zw+pV1xP4HxnD6lXXE/gfGcPqVdcT+B8Zw+kbpwP4HxnD6lXXE/gfGcPpG6cD+B8Zw+pV1xP4HxnD6lXXE/gfGcPpG6cD+B8Zw+kbpwPwAQnT4za20/ABCdPjNrbT8AEJ0+M2ttPwAQnT4za20/"),
"format": 34359742487,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAFwAYABYAFwAZABgAHAAaABsAGwAdABwAHQAeABwAHgAfABwAHgAgAB8AIAAhAB8AIAAiACEAIgAjACEAJgAkACUAJQAnACYAJQAoACcAKAApACcALAAqACsAKwAtACwAMAAuAC8ALwAxADAAMQAyADAAMgAzADAAMwA0ADAANQAzADIAMgA2ADUANgA3ADUAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIAQwBEAEIAQwBFAEQASABGAEcARwBJAEgATABKAEsASwBNAEwAUABOAE8ATwBRAFAAVABSAFMAUwBVAFQAWABWAFcAVwBZAFgAVwBaAFkAWgBbAFkAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoA"),
"material": ExtResource("1_qg7mg"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 108,
"vertex_data": PackedByteArray("dvMJQM3MTD4AAADAdvMJQDCaAiYAAADAdvMJQM3MTD528wlAdvMJQLRl0yd28wlANDOzP5qZmT4AAADANDOzP83MTD8AAADAMzOzP5qZmT40M7M/MzOzP83MTD80M7M/mpkZQM3MTD8AAADAmpkZQJqZGT8AAADAmpkZQM3MTD+amRlAmpkZQJqZGT+amRlAAACAPzCaAiYAAADAAACAP5qZmT4AAADA//9/P24Sw6cAAIA///9/P5qZmT4AAIA/dvMJQM3MTD4AAADAdvMJQM3MTD528wlAAAAAQJqZmT4AAADAAAAAQJqZmT4AAABAmpkZQJqZGT8AAADAzcwMQJqZGT8AAADAmpkZQJqZGT+amRlAzcwMQJqZGT/NzAxAAAAAwJqZGT+amRlAAAAAwJqZGT/NzAxAAACAPzCaAiYAAADAdvMJQDCaAiYAAADAAACAP5qZmT4AAADAdvMJQM3MTD4AAADAAAAAQJqZmT4AAADANDOzP5qZmT4AAADAzcwMQJqZGT8AAADANDOzP83MTD8AAADAmpkZQJqZGT8AAADAmpkZQM3MTD8AAADANDOzP5qZmT4AAADAMzOzP5qZmT40M7M/AACAP5qZmT4AAADA//9/P5qZmT4AAIA/AAAAwJqZmT40M7M/AAAAwJqZmT4AAIA/AAAAQJqZmT4AAADAAAAAQJqZmT4AAABAzcwMQJqZGT8AAADAzcwMQJqZGT/NzAxAAAAAwAAAAAAAAIA/AAAAwJqZmT4AAIA/AAAAwAAAAAB28wlAAAAAwJqZmT40M7M/AAAAwM3MTD80M7M/AAAAwJqZmT4AAABAAAAAwM3MTD528wlAAAAAwJqZGT/NzAxAAAAAwM3MTD+amRlAAAAAwJqZGT+amRlAAAAAwAAAAAB28wlAAAAAwM3MTD528wlAdvMJQLRl0yd28wlAdvMJQM3MTD528wlAAAAAwJqZGT+amRlAAAAAwM3MTD+amRlAmpkZQJqZGT+amRlAmpkZQM3MTD+amRlAdvMJQDCaAiYAAADAAACAPzCaAiYAAADAdvMJQLRl0yd28wlA//9/P24Sw6cAAIA/AAAAwAAAAAB28wlAAAAAwAAAAAAAAIA///9/P24Sw6cAAIA///9/P5qZmT4AAIA/AAAAwAAAAAAAAIA/AAAAwJqZmT4AAIA/AAAAwJqZmT40M7M/MzOzP5qZmT40M7M/AAAAwM3MTD80M7M/MzOzP83MTD80M7M/dvMJQM3MTD528wlAAAAAwM3MTD528wlAAAAAQJqZmT4AAABAAAAAwJqZmT4AAABAAAAAwJqZmT4AAABAAAAAwJqZGT/NzAxAAAAAQJqZmT4AAABAzcwMQJqZGT/NzAxAmpkZQM3MTD8AAADAmpkZQM3MTD+amRlANDOzP83MTD8AAADAMzOzP83MTD80M7M/AAAAwM3MTD+amRlAAAAAwM3MTD80M7M/AACAPwAAAAAAAIA/AACAPwAAAAAAAADAAAAAwAAAAAAAAIA/AAAAwAAAAAAAAADAAAAAwAAAAAAAAADAAAAAwM3MTD4AAADAAAAAwAAAAAAAAIA/AAAAwM3MTD4AAIA/AAAAwAAAAAAAAADAAACAPwAAAAAAAADAAAAAwM3MTD4AAADAAACAP83MTD4AAADAAACAP83MTD4AAADAAACAP83MTD4AAIA/AAAAwM3MTD4AAADAAAAAwM3MTD4AAIA/zdIxrf///7////9/////vzDezaH///+/////f////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////v/+//7////+/////f////7+qqqiq////P////3////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////7/N0jGt////vzDezaH///+/GLLmzf///78YsubN////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/////////+//////////7//////////v/////////+//////////7//////////v/////////+//////////7//////////v/////////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+/zMzMTP///7/MzMxM////v8zMzEz///+/zMzMTP///78AAP9/////vwAA/3////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////v/9//3////8//38xrf///z//f/9/////P/9/zaH///8//3//f////z//f/+/////P/9//3////8/qKqqqv///z//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//////////v/////////+//////////7//////////v/////////+//////////7//////////v/////////+//3/Nof///z//fzGt////P/9/5s3///8//3/mzf///z//f8xM////P/9/zEz///8//3/MTP///z//f8xM////P/+//7////+/qqqqqv///z//f///////v/9///////+//3//v////z//f///////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////7//////////v/////////+//////////7//////////v/9///////+//3///////7//f///////v/9///////+/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_hj52a")

[sub_resource type="BoxShape3D" id="BoxShape3D_dj80h"]
size = Vector3(4.17484, 0.239258, 4.13675)

[sub_resource type="BoxShape3D" id="BoxShape3D_imxng"]
size = Vector3(4.39015, 0.560913, 1.428)

[node name="RoofFlatCorner" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_fcpsc")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0699005, 0.11084, 0.0879669)
shape = SubResource("BoxShape3D_dj80h")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0699005, 0.544911, 1.69377)
shape = SubResource("BoxShape3D_imxng")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 1.69054, 0.544911, 0.201426)
shape = SubResource("BoxShape3D_imxng")
