[gd_scene load_steps=15 format=3 uid="uid://dmu6rpse3pie5"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntGameUI.gd" id="1_ui_script"]
[ext_resource type="Texture2D" uid="uid://bnc6lussdm5be" path="res://Scenes/ui/bg-main.png" id="3_dymwt"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="3_g6rjn"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="4_pqsi2"]
[ext_resource type="Texture2D" uid="uid://dppsb0nxm6r5q" path="res://Scenes/ui/assets/bg_open.png" id="5_y50l3"]
[ext_resource type="Texture2D" uid="uid://bwpbkk86b7quq" path="res://Scenes/ui/assets/smart.png" id="7_es8xd"]
[ext_resource type="Texture2D" uid="uid://0pg6iqc4n45o" path="res://Scenes/ui/assets/yellow.png" id="7_yuvhv"]
[ext_resource type="Texture2D" uid="uid://da3wo8wpnqf4n" path="res://Scenes/ui/prizebg.png" id="8_ohuoa"]
[ext_resource type="Texture2D" uid="uid://8q8ki5ld0acy" path="res://Scenes/ui/assets/Coin.png" id="9_rjljl"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.7)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_m77nf"]
bg_color = Color(0.262932, 0.262932, 0.262932, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_vsjpv"]
bg_color = Color(1, 1, 1, 1)
border_width_left = 1
border_width_top = 1
border_width_bottom = 1
corner_radius_top_left = 3
corner_radius_bottom_left = 3

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_v48jx"]
texture = ExtResource("3_dymwt")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fmf4w"]

[node name="ProphauntGameUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_ui_script")
metadata/_edit_lock_ = true

[node name="TopPanel" type="Panel" parent="."]
layout_mode = 1
offset_right = 198.0
offset_bottom = 80.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="TopContainer" type="HBoxContainer" parent="TopPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="RoundInfo" type="VBoxContainer" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="RoundLabel" type="Label" parent="TopPanel/TopContainer/RoundInfo"]
layout_mode = 2
text = "ROUND 1"
horizontal_alignment = 1

[node name="TimerLabel" type="Label" parent="TopPanel/TopContainer/RoundInfo"]
layout_mode = 2
text = "03:00"
horizontal_alignment = 1

[node name="VSeparator" type="VSeparator" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="TeamInfo" type="VBoxContainer" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="TeamLabel" type="Label" parent="TopPanel/TopContainer/TeamInfo"]
layout_mode = 2
text = "PROPS"
horizontal_alignment = 1

[node name="PropsAliveLabel" type="Label" parent="TopPanel/TopContainer/TeamInfo"]
visible = false
layout_mode = 2
text = "5/6 Alive"
horizontal_alignment = 1

[node name="DebugPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
offset_left = 200.0
offset_right = 540.0
offset_bottom = 80.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="TopContainer" type="HBoxContainer" parent="DebugPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="FPS" type="Label" parent="DebugPanel/TopContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "FPS"
horizontal_alignment = 1
text_direction = 1

[node name="PlayerListPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -250.0
offset_top = 100.0
offset_bottom = 400.0
grow_horizontal = 0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="PlayerListContainer" type="VBoxContainer" parent="PlayerListPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="PlayerListLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "PLAYERS"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="PropsLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "PROPS"
horizontal_alignment = 1

[node name="PropsList" type="VBoxContainer" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="HSeparator2" type="HSeparator" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="HauntersLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "HAUNTERS"
horizontal_alignment = 1

[node name="HauntersList" type="VBoxContainer" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="NotificationPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="NotificationLabel" type="Label" parent="NotificationPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "NOTIFICATION"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HealthBar" type="ProgressBar" parent="."]
custom_minimum_size = Vector2(200, 30)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -60.0
offset_right = 100.0
offset_bottom = -30.0
grow_horizontal = 2
grow_vertical = 0
theme_override_styles/background = SubResource("StyleBoxFlat_m77nf")
theme_override_styles/fill = SubResource("StyleBoxFlat_vsjpv")
step = 0.1
value = 62.8
show_percentage = false

[node name="Results" type="Control" parent="."]
layout_direction = 2
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BGPanel" type="Panel" parent="Results"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -13.0
offset_right = 22.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_v48jx")

[node name="NinePatchRect" type="NinePatchRect" parent="Results"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
region_rect = Rect2(-1.64912, -3.68098, 147.162, 127.539)
patch_margin_left = 10
patch_margin_top = 11
patch_margin_right = 12
patch_margin_bottom = 16

[node name="BG" type="TextureRect" parent="Results/NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(844, 390)
mouse_filter = 2
texture = ExtResource("5_y50l3")
stretch_mode = 4

[node name="CenterContainer" type="CenterContainer" parent="Results/NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="HBoxContainer" type="HBoxContainer" parent="Results/NinePatchRect/CenterContainer"]
layout_mode = 2

[node name="CoinContainer" type="TextureRect" parent="Results/NinePatchRect/CenterContainer/HBoxContainer"]
custom_minimum_size = Vector2(245, 128)
layout_mode = 2
mouse_filter = 2
texture = ExtResource("8_ohuoa")
expand_mode = 1
stretch_mode = 4

[node name="TextureRect" type="TextureRect" parent="Results/NinePatchRect/CenterContainer/HBoxContainer/CoinContainer"]
layout_mode = 0
offset_left = 30.0
offset_top = -10.0
offset_right = 142.0
offset_bottom = 103.0
mouse_filter = 2
texture = ExtResource("9_rjljl")

[node name="CoinLabel" type="Label" parent="Results/NinePatchRect/CenterContainer/HBoxContainer/CoinContainer"]
layout_mode = 0
offset_left = 121.0
offset_top = 8.0
offset_right = 258.0
offset_bottom = 83.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("3_g6rjn")
theme_override_font_sizes/font_size = 45
text = "+12"
vertical_alignment = 1

[node name="SmartContainer" type="TextureRect" parent="Results/NinePatchRect/CenterContainer/HBoxContainer"]
custom_minimum_size = Vector2(245, 128)
layout_mode = 2
mouse_filter = 2
texture = ExtResource("8_ohuoa")
expand_mode = 1
stretch_mode = 4

[node name="TextureRect" type="TextureRect" parent="Results/NinePatchRect/CenterContainer/HBoxContainer/SmartContainer"]
layout_mode = 0
offset_left = 39.0
offset_top = 11.0
offset_right = 107.0
offset_bottom = 77.0
mouse_filter = 2
texture = ExtResource("7_es8xd")
expand_mode = 1
stretch_mode = 4

[node name="SmartLabel" type="Label" parent="Results/NinePatchRect/CenterContainer/HBoxContainer/SmartContainer"]
layout_mode = 0
offset_left = 108.0
offset_top = 6.0
offset_right = 232.0
offset_bottom = 81.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("3_g6rjn")
theme_override_font_sizes/font_size = 45
text = "+12"
vertical_alignment = 1

[node name="KillContainer" type="TextureRect" parent="Results/NinePatchRect/CenterContainer/HBoxContainer"]
visible = false
custom_minimum_size = Vector2(245, 128)
layout_mode = 2
mouse_filter = 2
texture = ExtResource("8_ohuoa")
expand_mode = 1
stretch_mode = 4

[node name="TextureRect" type="TextureRect" parent="Results/NinePatchRect/CenterContainer/HBoxContainer/KillContainer"]
layout_mode = 0
offset_left = 39.0
offset_top = 11.0
offset_right = 107.0
offset_bottom = 77.0
mouse_filter = 2
texture = ExtResource("7_es8xd")
expand_mode = 1
stretch_mode = 4

[node name="KillLabel" type="Label" parent="Results/NinePatchRect/CenterContainer/HBoxContainer/KillContainer"]
layout_mode = 0
offset_left = 108.0
offset_top = 6.0
offset_right = 232.0
offset_bottom = 81.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("3_g6rjn")
theme_override_font_sizes/font_size = 45
text = "+12"
vertical_alignment = 1

[node name="ClaimButton" parent="Results/NinePatchRect" instance=ExtResource("4_pqsi2")]
custom_minimum_size = Vector2(150, 50)
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -235.0
offset_top = -116.0
offset_right = -70.0
offset_bottom = -45.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(82.5, 35.5)

[node name="TextureRect" type="TextureRect" parent="Results/NinePatchRect/ClaimButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = 10.0
offset_right = 5.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("7_yuvhv")
expand_mode = 1

[node name="label" type="Label" parent="Results/NinePatchRect/ClaimButton"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -133.0
offset_top = -64.0
offset_right = 138.0
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 7
theme_override_constants/shadow_outline_size = 6
theme_override_fonts/font = ExtResource("3_g6rjn")
theme_override_font_sizes/font_size = 35
text = "CLAIM"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Button" type="Button" parent="Results/NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_fmf4w")
flat = true

[connection signal="pressed" from="Results/NinePatchRect/ClaimButton" to="." method="_on_result_exit_button_pressed"]
[connection signal="pressed" from="Results/NinePatchRect/Button" to="." method="_on_result_exit_button_pressed"]
