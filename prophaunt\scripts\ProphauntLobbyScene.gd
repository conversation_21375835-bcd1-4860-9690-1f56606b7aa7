extends Control
class_name ProphauntLobbyScene

# Prophaunt Lobby Scene
# Displays character, weapon, and abilities before starting a Prophaunt game

# UI References
@onready var character_preview_image: TextureRect = $CharacterPreviewImage
@onready var character_preview:CharacterPreview = $CharacterContainer/CharacterPreview
@onready var weapon_icon = $WeaponPanel/WeaponContainer/WeaponIcon
@onready var weapon_selector_button = $WeaponPanel/WeaponSelectorButton
@onready var grenades_count = $AbilitiesPanel/AbilitiesContainer/GrenadesContainer/GrenadesCount
@onready var hexes_count = $AbilitiesPanel/AbilitiesContainer/HexesContainer/HexesCount
@onready var smart_label = $TopPanel/CurrencyPanel/SmartShow/SmartLabel
@onready var loading_panel = $LoadingPanel
@onready var http_request = $HTTPRequest
@onready var weapon_selector = $WeaponSelector
@onready var weapon_select_http_request = $WeaponSelectHTTPRequest

# Player data
var selected_weapon_id: int = 15  # Default pistol
var grenades_available: int = 0
var hexes_available: int = 0

const purchase_count = 3
var grenade_price = 0
var hex_price = 0


func _ready():
	# Set game mode
	Selector.selected_game_mode = Constants.GameMode.Prophaunt

	# Initialize the lobby
	setup_ui()
	load_character_preview()
	fetch_item_data()

	# Connect weapon selector signals
	weapon_select_http_request.request_completed.connect(_on_weapon_select_http_request_completed)


func setup_ui():
	"""Initialize UI elements"""
	# Update currency displays
	smart_label.text = str(Selector.my_smart)

	# Set default values
	grenades_count.text = str(grenades_available)
	hexes_count.text = str(hexes_available)

	# Load previously selected weapon
	selected_weapon_id = DataSaver.get_item("prophaunt_selected_weapon", 15)  # Default pistol

	# Hide loading panel initially
	loading_panel.visible = false


func load_character_preview():
	"""Load and display the selected character"""
	character_preview_image.camera_changed.connect(character_preview.on_camera_changed)
	character_preview_image.preview = character_preview
	character_preview.update(Selector.selected_character)
	character_preview.model.rifleIdleState.start_state()
	character_preview.model.handle_animation()


func fetch_item_data():
	"""Fetch current items from backend"""
	show_loading(true)
	
	var url = Constants.BACKEND_URL + "/prophunt/item_data/client/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	
	http_request.cancel_request()
	http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_http_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray):
	"""Handle HTTP request completion"""
	show_loading(false)
	
	if response_code == 200:
		var json_response = JSON.parse_string(body.get_string_from_utf8())
		if json_response:
			handle_item_data_response(json_response["item_data"])
			grenade_price = json_response["grenade_price"]
			hex_price = json_response["hex_price"]
		else:
			print("ProphauntLobby: Failed to parse JSON response")
	else:
		print("ProphauntLobby: HTTP request failed with code: ", response_code)


func handle_item_data_response(data: Dictionary):
	"""Process the item data response from backend"""
	Selector.my_prophaunt_item_data = data
	var w = Selector.my_prophaunt_item_data["current_selected_weapon"]
	Selector.my_prophaunt_item_data["selected_weapon_item"] = load(w["resource_path"])
	# Extract weapon information
	load_selected_weapon()

	
	# Extract grenades count
	if data.has("grenades"):
		grenades_available = data["grenades"]
		grenades_count.text = str(grenades_available)
	
	# Extract hexes count
	if data.has("hexes"):
		hexes_available = data["hexes"]
		hexes_count.text = str(hexes_available)


func load_selected_weapon():
	"""Load the selected weapon icon"""
	var weapon_item:ProphauntGunItem = Selector.my_prophaunt_item_data["selected_weapon_item"]
	weapon_icon.texture = weapon_item.icon
	weapon_item.on_player_equip(character_preview.model)


func show_loading(is_show: bool):
	"""Show or hide loading panel"""
	loading_panel.visible = is_show


func _on_back_button_pressed():
	"""Handle back button press - return to main menu"""
	SoundManager.play_click_sound()
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func _on_start_button_pressed():
	"""Handle start button press - go to ProphauntGame"""
	SoundManager.play_click_sound()

	# Save player name and selected weapon before starting
	DataSaver.set_item("handle", Selector.my_name, false)
	DataSaver.set_item("prophaunt_selected_weapon", selected_weapon_id, false)
	DataSaver.send_save_request()

	# Transition to ProphauntGame scene
	get_tree().change_scene_to_file("res://prophaunt/scenes/ProphauntGame.tscn")


func _on_weapon_selector_button_pressed():
	"""Handle weapon selector button press - show weapon selector"""
	SoundManager.play_click_sound()
	weapon_selector.show_selector()


func _on_weapon_selected(weapon_id: int, selected_weapon_icon: Texture2D):
	"""Handle weapon selection from weapon selector"""
	selected_weapon_id = weapon_id
	Selector.my_prophaunt_item_data["selected_weapon_item"] = ProphauntWeaponManager.get_weapon_by_id(selected_weapon_id)
	load_selected_weapon()

	# Update weapon panel display
	update_weapon_panel(selected_weapon_icon)

	# Send weapon selection to backend
	send_weapon_select_request(selected_weapon_id)


func update_weapon_panel(texture):
	"""Update the weapon panel with selected weapon"""
	weapon_icon.texture = texture

	# Save selected weapon
	DataSaver.set_item("prophaunt_selected_weapon", selected_weapon_id, false)


func send_weapon_select_request(weapon_id: int):
	"""Send weapon selection request to backend"""
	print("ProphauntLobby: Sending weapon select request for weapon ID: ", weapon_id)

	var url = Constants.BACKEND_URL + "/prophunt/weapons/select/"
	var data = {
		"weapon_id": weapon_id
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]

	weapon_select_http_request.cancel_request()
	weapon_select_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func _on_weapon_select_http_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray):
	"""Handle weapon select response from backend"""
	print("ProphauntLobby: Weapon select response code: ", response_code)

	if response_code == 200:
		print("ProphauntLobby: Weapon selection saved successfully")
		#var json = JSON.parse_string(body.get_string_from_utf8())
		#if json:
			#print("ProphauntLobby: Server response: ", json)
	else:
		print("ProphauntLobby: Weapon select failed with code: ", response_code)
		var error_message = body.get_string_from_utf8()
		print("ProphauntLobby: Error message: ", error_message)
		Constants.show_toast(str(response_code))


func _notification(what: int) -> void:
	"""Handle system notifications"""
	if what == NOTIFICATION_WM_CLOSE_REQUEST or what == NOTIFICATION_WM_GO_BACK_REQUEST:
		# Handle back button on mobile or window close
		_on_back_button_pressed()


func _on_purchase_grenade_pressed() -> void:
	pass # Replace with function body.


func _on_purchase_hex_pressed() -> void:
	pass # Replace with function body.
