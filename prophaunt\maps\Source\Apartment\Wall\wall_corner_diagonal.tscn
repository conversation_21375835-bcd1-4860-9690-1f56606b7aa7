[gd_scene load_steps=6 format=4 uid="uid://s38nuc76lq6d"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_gdmdh"]

[sub_resource type="ArrayMesh" id="ArrayMesh_3o0lw"]
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4, 4.8, 4),
"format": 34359742465,
"index_count": 66,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYABwAAAAIAAgAGAAcAAQAIAAkACQADAAEADAAKAAsACwANAAwADwAOAAwADAANAA8ACQAIAA4ADgAPAAkACgAFAAQABAALAAoACwAEAAYABgANAAsABgACAA0AAgAPAA0AAgADAA8AAwAJAA8A"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("SYbrMgAAAAAAAABAAAAAwAAAAAAAAABAsQAAs5qZmUAAAABAAAAAwJqZmUAAAABAAAAAQJqZmUAAAADAAAAAQAAAAAAAAADAAAAAQJqZmUCrAICyAAAAQAAAAACgAICyAAAAwAAAAABmZuY/AAAAwJqZmUBmZuY/ZmbmPwAAAAAAAADAZmbmP5qZmUAAAADAZmbmPwAAAAB4qam9ZmbmP5qZmUB4qam9eqmpvQAAAABmZuY/eqmpvZqZmUBmZuY/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_mxv04"]
resource_name = "WallCornerDiagonal_wall-corner-diagonal"
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4, 4.8, 4),
"attribute_data": PackedByteArray("Tb2kPnjObT9NvaQ+eM5tP029pD7ULWw/Tb2kPtQtbD9NvaQ+1C1sP029pD54zm0/Tb2kPtQtbD9NvaQ+eM5tP029pD54zm0/Tb2kPtQtbD9NvaQ+eM5tP029pD7ULWw/gAClPqC4cT+AAKU+9VdwP4AApT6guHE/gAClPvVXcD+AAKU+oLhxP4AApT71V3A/gAClPqC4cT+AAKU+9VdwP4AApT6guHE/gAClPqC4cT+AAKU+9VdwP4AApT71V3A/gAClPqC4cT+AAKU+oLhxP4AApT71V3A/gAClPvVXcD+AAKU+oLhxP4AApT71V3A/gAClPqC4cT+AAKU+9VdwP8C4pD61n3A/wLikPrWfcD/AuKQ+tZ9wP8C4pD61n3A/wLikPrWfcD/AuKQ+tZ9wP8C4pD61n3A/wLikPrWfcD8="),
"format": 34359742487,
"index_count": 66,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAIQAkACMAJAAlACMAJAAmACUAJgAnACUA"),
"material": ExtResource("1_gdmdh"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("SYbrMgAAAAAAAABAAAAAwAAAAAAAAABAsQAAs5qZmUAAAABAAAAAwJqZmUAAAABAAAAAQJqZmUAAAADAAAAAQAAAAAAAAADAAAAAQJqZmUCrAICyAAAAQAAAAACgAICySYbrMgAAAAAAAABAsQAAs5qZmUAAAABAAAAAQAAAAACgAICyAAAAQJqZmUCrAICyAAAAwAAAAABmZuY/AAAAwJqZmUBmZuY/AAAAwAAAAAAAAABAAAAAwJqZmUAAAABAZmbmPwAAAAAAAADAZmbmP5qZmUAAAADAZmbmPwAAAAB4qam9ZmbmP5qZmUB4qam9eqmpvQAAAABmZuY/ZmbmPwAAAAB4qam9eqmpvZqZmUBmZuY/ZmbmP5qZmUB4qam9AAAAwAAAAABmZuY/eqmpvQAAAABmZuY/AAAAwJqZmUBmZuY/eqmpvZqZmUBmZuY/AAAAQAAAAAAAAADAAAAAQJqZmUAAAADAZmbmPwAAAAAAAADAZmbmP5qZmUAAAADAAAAAQJqZmUAAAADAAAAAQJqZmUCrAICyZmbmP5qZmUAAAADAZmbmP5qZmUB4qam9sQAAs5qZmUAAAABAeqmpvZqZmUBmZuY/AAAAwJqZmUAAAABAAAAAwJqZmUBmZuY//3//f////z//f/9/////P/9//3////8//3//f////z////9/////v////3////+/////f////7////9/////v/+//3////8//7//f////z//v/9/////P/+//3////8/AAD/f////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////vwAA/7////+/AAD/v////78AAP+/////vwAA/7////+//////////7//////////v/////////+//////////7//////////v/////////+//////////7//////////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_3o0lw")

[sub_resource type="BoxShape3D" id="BoxShape3D_bneqh"]
size = Vector3(0.2, 4.8, 2)

[sub_resource type="BoxShape3D" id="BoxShape3D_hcp8d"]
size = Vector3(0.209766, 4.8, 2.8526)

[node name="WallCornerDiagonal" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_mxv04")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.9, 2.4, -1)
shape = SubResource("BoxShape3D_bneqh")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -1, 2.4, 1.9)
shape = SubResource("BoxShape3D_bneqh")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(0.707107, 0, -0.707107, 0, 1, 0, 0.707107, 0, 0.707107, 0.927913, 2.4, 0.933694)
shape = SubResource("BoxShape3D_hcp8d")
