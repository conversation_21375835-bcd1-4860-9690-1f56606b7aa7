[gd_scene load_steps=4 format=3 uid="uid://cy2v6fjmwvk4j"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_wapht"]
[ext_resource type="PackedScene" uid="uid://c885immana41d" path="res://prophaunt/maps/Source/ArenaProp/weapon_rack.tscn" id="2_mp72f"]

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_tr8ma"]
data = PackedVector3Array(0.9749, 0, 0.4717, 0.9749, 0, -0.4717, 0.6604, 0, 0.4717, 0.6604, 0, -0.4717, 0.6604, 0, 0.4717, 0.9749, 0, -0.4717, 0.9749, 1.4676, -0.1572, 0.9749, 1.4676, 0.1572, 0.6604, 1.4676, -0.1572, 0.6604, 1.4676, 0.1572, 0.6604, 1.4676, -0.1572, 0.9749, 1.4676, 0.1572, 0.6604, 0, -0.4717, 0.6604, 0.1572, -0.4717, 0.6604, 0, 0.4717, 0.6604, 0.3145, -0.1572, 0.6604, 0, 0.4717, 0.6604, 0.1572, -0.4717, 0.6604, 0.3145, 0.1572, 0.6604, 0, 0.4717, 0.6604, 0.3145, -0.1572, 0.6604, 0.1572, 0.4717, 0.6604, 0, 0.4717, 0.6604, 0.3145, 0.1572, 0.6604, 1.4676, -0.1572, 0.6604, 0.3145, 0.1572, 0.6604, 0.3145, -0.1572, 0.6604, 0.3145, 0.1572, 0.6604, 1.4676, -0.1572, 0.6604, 1.4676, 0.1572, 0.6604, 0.3145, -0.1572, 0.9749, 0.3145, -0.1572, 0.6604, 1.4676, -0.1572, 0.9749, 1.4676, -0.1572, 0.6604, 1.4676, -0.1572, 0.9749, 0.3145, -0.1572, 0.9749, 0.3145, 0.1572, 0.6604, 0.3145, 0.1572, 0.9749, 1.4676, 0.1572, 0.6604, 1.4676, 0.1572, 0.9749, 1.4676, 0.1572, 0.6604, 0.3145, 0.1572, 0.9749, 0, 0.4717, 0.6604, 0, 0.4717, 0.9749, 0.1572, 0.4717, 0.6604, 0.1572, 0.4717, 0.9749, 0.1572, 0.4717, 0.6604, 0, 0.4717, 0.9749, 0.3145, 0.1572, 0.9749, 0.1572, 0.4717, 0.6604, 0.3145, 0.1572, 0.6604, 0.1572, 0.4717, 0.6604, 0.3145, 0.1572, 0.9749, 0.1572, 0.4717, 0.6604, 0, -0.4717, 0.9749, 0, -0.4717, 0.6604, 0.1572, -0.4717, 0.9749, 0.1572, -0.4717, 0.6604, 0.1572, -0.4717, 0.9749, 0, -0.4717, 0.9749, 1.4676, -0.1572, 0.9749, 0.3145, -0.1572, 0.9749, 1.4676, 0.1572, 0.9749, 1.4676, 0.1572, 0.9749, 0.3145, -0.1572, 0.9749, 0.3145, 0.1572, 0.9749, 0.3145, 0.1572, 0.9749, 0.3145, -0.1572, 0.9749, 0, 0.4717, 0.9749, 0.1572, 0.4717, 0.9749, 0.3145, 0.1572, 0.9749, 0, 0.4717, 0.9749, 0, 0.4717, 0.9749, 0.3145, -0.1572, 0.9749, 0, -0.4717, 0.9749, 0.1572, -0.4717, 0.9749, 0, -0.4717, 0.9749, 0.3145, -0.1572, 0.9749, 0.1572, -0.4717, 0.9749, 0.3145, -0.1572, 0.6604, 0.1572, -0.4717, 0.6604, 0.3145, -0.1572, 0.6604, 0.1572, -0.4717, 0.9749, 0.3145, -0.1572, -0.6604, 0, 0.4717, -0.6604, 0, -0.4717, -0.9749, 0, 0.4717, -0.9749, 0, -0.4717, -0.9749, 0, 0.4717, -0.6604, 0, -0.4717, -0.6604, 1.4676, -0.1572, -0.6604, 1.4676, 0.1572, -0.9749, 1.4676, -0.1572, -0.9749, 1.4676, 0.1572, -0.9749, 1.4676, -0.1572, -0.6604, 1.4676, 0.1572, -0.9749, 0, -0.4717, -0.9749, 0.1572, -0.4717, -0.9749, 0, 0.4717, -0.9749, 0.3145, -0.1572, -0.9749, 0, 0.4717, -0.9749, 0.1572, -0.4717, -0.9749, 0.3145, 0.1572, -0.9749, 0, 0.4717, -0.9749, 0.3145, -0.1572, -0.9749, 0.1572, 0.4717, -0.9749, 0, 0.4717, -0.9749, 0.3145, 0.1572, -0.9749, 1.4676, -0.1572, -0.9749, 0.3145, 0.1572, -0.9749, 0.3145, -0.1572, -0.9749, 0.3145, 0.1572, -0.9749, 1.4676, -0.1572, -0.9749, 1.4676, 0.1572, -0.9749, 0.3145, -0.1572, -0.6604, 0.3145, -0.1572, -0.9749, 1.4676, -0.1572, -0.6604, 1.4676, -0.1572, -0.9749, 1.4676, -0.1572, -0.6604, 0.3145, -0.1572, -0.6604, 0.3145, 0.1572, -0.9749, 0.3145, 0.1572, -0.6604, 1.4676, 0.1572, -0.9749, 1.4676, 0.1572, -0.6604, 1.4676, 0.1572, -0.9749, 0.3145, 0.1572, -0.6604, 0, 0.4717, -0.9749, 0, 0.4717, -0.6604, 0.1572, 0.4717, -0.9749, 0.1572, 0.4717, -0.6604, 0.1572, 0.4717, -0.9749, 0, 0.4717, -0.6604, 0.3145, 0.1572, -0.6604, 0.1572, 0.4717, -0.9749, 0.3145, 0.1572, -0.9749, 0.1572, 0.4717, -0.9749, 0.3145, 0.1572, -0.6604, 0.1572, 0.4717, -0.9749, 0, -0.4717, -0.6604, 0, -0.4717, -0.9749, 0.1572, -0.4717, -0.6604, 0.1572, -0.4717, -0.9749, 0.1572, -0.4717, -0.6604, 0, -0.4717, -0.6604, 1.4676, -0.1572, -0.6604, 0.3145, -0.1572, -0.6604, 1.4676, 0.1572, -0.6604, 1.4676, 0.1572, -0.6604, 0.3145, -0.1572, -0.6604, 0.3145, 0.1572, -0.6604, 0.3145, 0.1572, -0.6604, 0.3145, -0.1572, -0.6604, 0, 0.4717, -0.6604, 0.1572, 0.4717, -0.6604, 0.3145, 0.1572, -0.6604, 0, 0.4717, -0.6604, 0, 0.4717, -0.6604, 0.3145, -0.1572, -0.6604, 0, -0.4717, -0.6604, 0.1572, -0.4717, -0.6604, 0, -0.4717, -0.6604, 0.3145, -0.1572, -0.6604, 0.1572, -0.4717, -0.6604, 0.3145, -0.1572, -0.9749, 0.1572, -0.4717, -0.9749, 0.3145, -0.1572, -0.9749, 0.1572, -0.4717, -0.6604, 0.3145, -0.1572, 0.6604, 0.8387, -0.3145, 0.9749, 0.8387, -0.3145, 0.6604, 1.1531, -0.3145, 0.9749, 1.1531, -0.3145, 0.6604, 1.1531, -0.3145, 0.9749, 0.8387, -0.3145, 0.9749, 0.8387, -0.1572, 0.9749, 1.1531, -0.1572, 0.9749, 0.8387, 0.1572, 0.9749, 1.1531, 0.1572, 0.9749, 0.8387, 0.1572, 0.9749, 1.1531, -0.1572, 0.9749, 0.8387, -0.1572, 0.6604, 0.8387, -0.1572, 0.9749, 1.1531, -0.1572, 0.6604, 1.1531, -0.1572, 0.9749, 1.1531, -0.1572, 0.6604, 0.8387, -0.1572, 0.6604, 0.8387, 0.1572, 0.9749, 0.8387, 0.1572, 0.6604, 1.1531, 0.1572, 0.9749, 1.1531, 0.1572, 0.6604, 1.1531, 0.1572, 0.9749, 0.8387, 0.1572, 0.9749, 0.8387, 0.3145, 0.6604, 0.8387, 0.3145, 0.9749, 1.1531, 0.3145, 0.6604, 1.1531, 0.3145, 0.9749, 1.1531, 0.3145, 0.6604, 0.8387, 0.3145, 1.1322, 1.1531, 0.1572, 1.1322, 0.8387, 0.1572, 0.9749, 1.1531, 0.3145, 0.9749, 0.8387, 0.3145, 0.9749, 1.1531, 0.3145, 1.1322, 0.8387, 0.1572, 1.1322, 1.1531, -0.1572, 1.1322, 0.8387, -0.1572, 1.1322, 1.1531, 0.1572, 1.1322, 0.8387, 0.1572, 1.1322, 1.1531, 0.1572, 1.1322, 0.8387, -0.1572, 0.9749, 1.1531, -0.3145, 0.9749, 0.8387, -0.3145, 1.1322, 1.1531, -0.1572, 1.1322, 0.8387, -0.1572, 1.1322, 1.1531, -0.1572, 0.9749, 0.8387, -0.3145, 0.6604, 0.8387, 0.1572, 0.6604, 0.8387, 0.3145, 0.9749, 0.8387, 0.1572, 0.9749, 0.8387, 0.3145, 0.9749, 0.8387, 0.1572, 0.6604, 0.8387, 0.3145, 0.9749, 0.8387, 0.1572, 0.9749, 0.8387, 0.3145, 0.9749, 0.8387, -0.3145, 0.9749, 0.8387, -0.1572, 0.9749, 0.8387, 0.1572, 0.9749, 0.8387, -0.3145, 0.6604, 0.8387, -0.1572, 0.9749, 0.8387, -0.1572, 0.9749, 0.8387, -0.3145, 0.6604, 0.8387, -0.3145, 0.6604, 0.8387, -0.1572, 0.9749, 0.8387, -0.3145, 0.9749, 0.8387, -0.3145, 0.9749, 0.8387, 0.3145, 1.1322, 0.8387, -0.1572, 1.1322, 0.8387, 0.1572, 1.1322, 0.8387, -0.1572, 0.9749, 0.8387, 0.3145, 0.6604, 1.1531, -0.1572, 0.6604, 1.1531, -0.3145, 0.9749, 1.1531, -0.1572, 0.9749, 1.1531, -0.3145, 0.9749, 1.1531, -0.1572, 0.6604, 1.1531, -0.3145, 0.9749, 1.1531, -0.1572, 0.9749, 1.1531, -0.3145, 0.9749, 1.1531, 0.3145, 0.9749, 1.1531, 0.1572, 0.9749, 1.1531, -0.1572, 0.9749, 1.1531, 0.3145, 0.6604, 1.1531, 0.1572, 0.9749, 1.1531, 0.1572, 0.9749, 1.1531, 0.3145, 0.6604, 1.1531, 0.3145, 0.6604, 1.1531, 0.1572, 0.9749, 1.1531, 0.3145, 0.9749, 1.1531, 0.3145, 0.9749, 1.1531, -0.3145, 1.1322, 1.1531, 0.1572, 1.1322, 1.1531, -0.1572, 1.1322, 1.1531, 0.1572, 0.9749, 1.1531, -0.3145, -0.6604, 0.8387, 0.3145, -0.9749, 0.8387, 0.3145, -0.6604, 1.1531, 0.3145, -0.9749, 1.1531, 0.3145, -0.6604, 1.1531, 0.3145, -0.9749, 0.8387, 0.3145, -0.9749, 0.8387, 0.1572, -0.9749, 1.1531, 0.1572, -0.9749, 0.8387, -0.1572, -0.9749, 1.1531, -0.1572, -0.9749, 0.8387, -0.1572, -0.9749, 1.1531, 0.1572, -0.9749, 0.8387, 0.1572, -0.6604, 0.8387, 0.1572, -0.9749, 1.1531, 0.1572, -0.6604, 1.1531, 0.1572, -0.9749, 1.1531, 0.1572, -0.6604, 0.8387, 0.1572, -0.6604, 0.8387, -0.1572, -0.9749, 0.8387, -0.1572, -0.6604, 1.1531, -0.1572, -0.9749, 1.1531, -0.1572, -0.6604, 1.1531, -0.1572, -0.9749, 0.8387, -0.1572, -0.9749, 0.8387, -0.3145, -0.6604, 0.8387, -0.3145, -0.9749, 1.1531, -0.3145, -0.6604, 1.1531, -0.3145, -0.9749, 1.1531, -0.3145, -0.6604, 0.8387, -0.3145, -1.1322, 1.1531, -0.1572, -1.1322, 0.8387, -0.1572, -0.9749, 1.1531, -0.3145, -0.9749, 0.8387, -0.3145, -0.9749, 1.1531, -0.3145, -1.1322, 0.8387, -0.1572, -1.1322, 1.1531, 0.1572, -1.1322, 0.8387, 0.1572, -1.1322, 1.1531, -0.1572, -1.1322, 0.8387, -0.1572, -1.1322, 1.1531, -0.1572, -1.1322, 0.8387, 0.1572, -0.9749, 1.1531, 0.3145, -0.9749, 0.8387, 0.3145, -1.1322, 1.1531, 0.1572, -1.1322, 0.8387, 0.1572, -1.1322, 1.1531, 0.1572, -0.9749, 0.8387, 0.3145, -0.6604, 0.8387, -0.1572, -0.6604, 0.8387, -0.3145, -0.9749, 0.8387, -0.1572, -0.9749, 0.8387, -0.3145, -0.9749, 0.8387, -0.1572, -0.6604, 0.8387, -0.3145, -0.9749, 0.8387, -0.1572, -0.9749, 0.8387, -0.3145, -0.9749, 0.8387, 0.3145, -0.9749, 0.8387, 0.1572, -0.9749, 0.8387, -0.1572, -0.9749, 0.8387, 0.3145, -0.6604, 0.8387, 0.1572, -0.9749, 0.8387, 0.1572, -0.9749, 0.8387, 0.3145, -0.6604, 0.8387, 0.3145, -0.6604, 0.8387, 0.1572, -0.9749, 0.8387, 0.3145, -0.9749, 0.8387, 0.3145, -0.9749, 0.8387, -0.3145, -1.1322, 0.8387, 0.1572, -1.1322, 0.8387, -0.1572, -1.1322, 0.8387, 0.1572, -0.9749, 0.8387, -0.3145, -0.6604, 1.1531, 0.1572, -0.6604, 1.1531, 0.3145, -0.9749, 1.1531, 0.1572, -0.9749, 1.1531, 0.3145, -0.9749, 1.1531, 0.1572, -0.6604, 1.1531, 0.3145, -0.9749, 1.1531, 0.1572, -0.9749, 1.1531, 0.3145, -0.9749, 1.1531, -0.3145, -0.9749, 1.1531, -0.1572, -0.9749, 1.1531, 0.1572, -0.9749, 1.1531, -0.3145, -0.6604, 1.1531, -0.1572, -0.9749, 1.1531, -0.1572, -0.9749, 1.1531, -0.3145, -0.6604, 1.1531, -0.3145, -0.6604, 1.1531, -0.1572, -0.9749, 1.1531, -0.3145, -0.9749, 1.1531, -0.3145, -0.9749, 1.1531, 0.3145, -1.1322, 1.1531, -0.1572, -1.1322, 1.1531, 0.1572, -1.1322, 1.1531, -0.1572, -0.9749, 1.1531, 0.3145, 0.6604, 0.8387, -0.4717, 0.6604, 1.1531, -0.4717, 0.4994, 0.8387, -0.4717, 0.4994, 1.1531, -0.4717, 0.4994, 0.8387, -0.4717, 0.6604, 1.1531, -0.4717, 0.4994, 0.8387, -0.4717, 0.4994, 1.1531, -0.4717, 0.4994, 0.8387, -0.2621, 0.4994, 1.1531, -0.2621, 0.4994, 0.8387, -0.2621, 0.4994, 1.1531, -0.4717, 0.6604, 0.8387, 0.4717, 0.6604, 0.8387, -0.4717, 0.4994, 0.8387, 0.4717, 0.4994, 0.8387, -0.4717, 0.4994, 0.8387, 0.4717, 0.6604, 0.8387, -0.4717, 0.4994, 0.8387, 0.2621, 0.4994, 0.8387, 0.4717, 0.4994, 0.8387, -0.4717, 0.4994, 0.8387, -0.4717, 0.4994, 0.8387, -0.2621, 0.4994, 0.8387, 0.2621, 0.3302, 0.8387, 0.1572, 0.4994, 0.8387, 0.2621, 0.4994, 0.8387, -0.2621, 0.3302, 0.8387, -0.1572, 0.3302, 0.8387, 0.1572, 0.4994, 0.8387, -0.2621, 0.1611, 0.8387, 0.2621, 0.3302, 0.8387, 0.1572, 0.3302, 0.8387, -0.1572, 0.1611, 0.8387, -0.2621, 0.1611, 0.8387, 0.2621, 0.3302, 0.8387, -0.1572, 0.1611, 0.8387, 0.4717, 0.1611, 0.8387, 0.2621, 0.1611, 0.8387, -0.2621, -0.1611, 0.8387, 0.4717, 0.1611, 0.8387, 0.4717, 0.1611, 0.8387, -0.2621, -0.1611, 0.8387, -0.4717, -0.1611, 0.8387, 0.4717, 0.1611, 0.8387, -0.2621, 0.1611, 0.8387, -0.2621, 0.1611, 0.8387, -0.4717, -0.1611, 0.8387, -0.4717, -0.1611, 0.8387, 0.2621, -0.1611, 0.8387, 0.4717, -0.1611, 0.8387, -0.4717, -0.1611, 0.8387, -0.4717, -0.1611, 0.8387, -0.2621, -0.1611, 0.8387, 0.2621, -0.3302, 0.8387, 0.1572, -0.1611, 0.8387, 0.2621, -0.1611, 0.8387, -0.2621, -0.3302, 0.8387, -0.1572, -0.3302, 0.8387, 0.1572, -0.1611, 0.8387, -0.2621, -0.4994, 0.8387, 0.2621, -0.3302, 0.8387, 0.1572, -0.3302, 0.8387, -0.1572, -0.4994, 0.8387, -0.2621, -0.4994, 0.8387, 0.2621, -0.3302, 0.8387, -0.1572, -0.4994, 0.8387, 0.4717, -0.4994, 0.8387, 0.2621, -0.4994, 0.8387, -0.2621, -0.6604, 0.8387, 0.4717, -0.4994, 0.8387, 0.4717, -0.4994, 0.8387, -0.2621, -0.6604, 0.8387, 0.4717, -0.4994, 0.8387, -0.2621, -0.6604, 0.8387, -0.4717, -0.4994, 0.8387, -0.4717, -0.6604, 0.8387, -0.4717, -0.4994, 0.8387, -0.2621, -0.4994, 1.1531, -0.4717, -0.4994, 0.8387, -0.4717, -0.4994, 1.1531, -0.2621, -0.4994, 0.8387, -0.2621, -0.4994, 1.1531, -0.2621, -0.4994, 0.8387, -0.4717, -0.1611, 0.8387, -0.4717, -0.1611, 1.1531, -0.4717, -0.1611, 0.8387, -0.2621, -0.1611, 1.1531, -0.2621, -0.1611, 0.8387, -0.2621, -0.1611, 1.1531, -0.4717, 0.1611, 1.1531, -0.4717, 0.1611, 0.8387, -0.4717, 0.1611, 1.1531, -0.2621, 0.1611, 0.8387, -0.2621, 0.1611, 1.1531, -0.2621, 0.1611, 0.8387, -0.4717, -0.1611, 0.8387, -0.4717, 0.1611, 0.8387, -0.4717, -0.1611, 1.1531, -0.4717, 0.1611, 1.1531, -0.4717, -0.1611, 1.1531, -0.4717, 0.1611, 0.8387, -0.4717, 0.1611, 1.1531, -0.4717, 0.1611, 1.1531, -0.2621, -0.1611, 1.1531, -0.4717, -0.1611, 1.1531, -0.4717, 0.1611, 1.1531, -0.2621, -0.1611, 1.1531, 0.4717, -0.1611, 1.1531, 0.2621, -0.1611, 1.1531, -0.4717, -0.1611, 1.1531, 0.4717, -0.1611, 1.1531, 0.4717, 0.1611, 1.1531, -0.2621, 0.1611, 1.1531, 0.4717, -0.1611, 1.1531, -0.4717, -0.1611, 1.1531, 0.2621, -0.1611, 1.1531, -0.2621, 0.1611, 1.1531, 0.4717, 0.1611, 1.1531, -0.2621, 0.1611, 1.1531, 0.2621, -0.3302, 1.1531, 0.1572, -0.1611, 1.1531, -0.2621, -0.1611, 1.1531, 0.2621, 0.1611, 1.1531, -0.2621, 0.3302, 1.1531, -0.1572, 0.1611, 1.1531, 0.2621, -0.3302, 1.1531, -0.1572, -0.1611, 1.1531, -0.2621, -0.3302, 1.1531, 0.1572, 0.1611, 1.1531, 0.2621, 0.3302, 1.1531, -0.1572, 0.3302, 1.1531, 0.1572, -0.4994, 1.1531, 0.2621, -0.3302, 1.1531, -0.1572, -0.3302, 1.1531, 0.1572, 0.3302, 1.1531, -0.1572, 0.4994, 1.1531, -0.2621, 0.3302, 1.1531, 0.1572, 0.3302, 1.1531, 0.1572, 0.4994, 1.1531, -0.2621, 0.4994, 1.1531, 0.2621, -0.4994, 1.1531, -0.2621, -0.3302, 1.1531, -0.1572, -0.4994, 1.1531, 0.2621, 0.4994, 1.1531, -0.4717, 0.4994, 1.1531, 0.2621, 0.4994, 1.1531, -0.2621, 0.4994, 1.1531, 0.2621, 0.4994, 1.1531, -0.4717, 0.4994, 1.1531, 0.4717, 0.4994, 1.1531, 0.4717, 0.4994, 1.1531, -0.4717, 0.6604, 1.1531, 0.4717, 0.6604, 1.1531, -0.4717, 0.6604, 1.1531, 0.4717, 0.4994, 1.1531, -0.4717, -0.4994, 1.1531, 0.4717, -0.4994, 1.1531, -0.2621, -0.4994, 1.1531, 0.2621, -0.6604, 1.1531, 0.4717, -0.4994, 1.1531, -0.2621, -0.4994, 1.1531, 0.4717, -0.6604, 1.1531, -0.4717, -0.4994, 1.1531, -0.2621, -0.6604, 1.1531, 0.4717, -0.4994, 1.1531, -0.4717, -0.4994, 1.1531, -0.2621, -0.6604, 1.1531, -0.4717, -0.6604, 0.8387, 0.4717, -0.6604, 1.1531, 0.4717, -0.4994, 0.8387, 0.4717, -0.4994, 1.1531, 0.4717, -0.4994, 0.8387, 0.4717, -0.6604, 1.1531, 0.4717, 0.1611, 0.8387, 0.4717, -0.1611, 0.8387, 0.4717, 0.1611, 1.1531, 0.4717, -0.1611, 1.1531, 0.4717, 0.1611, 1.1531, 0.4717, -0.1611, 0.8387, 0.4717, 0.1611, 1.1531, 0.2621, 0.1611, 0.8387, 0.2621, 0.1611, 1.1531, 0.4717, 0.1611, 0.8387, 0.4717, 0.1611, 1.1531, 0.4717, 0.1611, 0.8387, 0.2621, 0.6604, 1.1531, -0.4717, 0.6604, 0.8387, -0.4717, 0.6604, 1.1531, 0.4717, 0.6604, 0.8387, 0.4717, 0.6604, 1.1531, 0.4717, 0.6604, 0.8387, -0.4717, -0.4994, 0.8387, -0.4717, -0.4994, 1.1531, -0.4717, -0.6604, 0.8387, -0.4717, -0.6604, 1.1531, -0.4717, -0.6604, 0.8387, -0.4717, -0.4994, 1.1531, -0.4717, -0.6604, 1.1531, -0.4717, -0.6604, 1.1531, 0.4717, -0.6604, 0.8387, -0.4717, -0.6604, 0.8387, 0.4717, -0.6604, 0.8387, -0.4717, -0.6604, 1.1531, 0.4717, -0.1611, 0.8387, 0.2621, -0.1611, 1.1531, 0.2621, -0.1611, 0.8387, 0.4717, -0.1611, 1.1531, 0.4717, -0.1611, 0.8387, 0.4717, -0.1611, 1.1531, 0.2621, 0.4994, 0.8387, 0.4717, 0.4994, 1.1531, 0.4717, 0.6604, 0.8387, 0.4717, 0.6604, 1.1531, 0.4717, 0.6604, 0.8387, 0.4717, 0.4994, 1.1531, 0.4717, -0.4994, 1.1531, 0.2621, -0.4994, 0.8387, 0.2621, -0.4994, 1.1531, 0.4717, -0.4994, 0.8387, 0.4717, -0.4994, 1.1531, 0.4717, -0.4994, 0.8387, 0.2621, 0.4994, 0.8387, 0.2621, 0.4994, 1.1531, 0.2621, 0.4994, 0.8387, 0.4717, 0.4994, 1.1531, 0.4717, 0.4994, 0.8387, 0.4717, 0.4994, 1.1531, 0.2621, -0.3302, 0.8387, 0.1572, -0.3302, 1.1531, 0.1572, -0.1611, 0.8387, 0.2621, -0.1611, 1.1531, 0.2621, -0.1611, 0.8387, 0.2621, -0.3302, 1.1531, 0.1572, -0.4994, 0.8387, 0.2621, -0.4994, 1.1531, 0.2621, -0.3302, 0.8387, 0.1572, -0.3302, 1.1531, 0.1572, -0.3302, 0.8387, 0.1572, -0.4994, 1.1531, 0.2621, 0.3302, 0.8387, 0.1572, 0.3302, 1.1531, 0.1572, 0.4994, 0.8387, 0.2621, 0.4994, 1.1531, 0.2621, 0.4994, 0.8387, 0.2621, 0.3302, 1.1531, 0.1572, 0.1611, 0.8387, 0.2621, 0.1611, 1.1531, 0.2621, 0.3302, 0.8387, 0.1572, 0.3302, 1.1531, 0.1572, 0.3302, 0.8387, 0.1572, 0.1611, 1.1531, 0.2621, 0.3302, 0.8387, -0.1572, 0.3302, 1.1531, -0.1572, 0.1611, 0.8387, -0.2621, 0.1611, 1.1531, -0.2621, 0.1611, 0.8387, -0.2621, 0.3302, 1.1531, -0.1572, 0.4994, 0.8387, -0.2621, 0.4994, 1.1531, -0.2621, 0.3302, 0.8387, -0.1572, 0.3302, 1.1531, -0.1572, 0.3302, 0.8387, -0.1572, 0.4994, 1.1531, -0.2621, -0.3302, 0.8387, -0.1572, -0.3302, 1.1531, -0.1572, -0.4994, 0.8387, -0.2621, -0.4994, 1.1531, -0.2621, -0.4994, 0.8387, -0.2621, -0.3302, 1.1531, -0.1572, -0.1611, 0.8387, -0.2621, -0.1611, 1.1531, -0.2621, -0.3302, 0.8387, -0.1572, -0.3302, 1.1531, -0.1572, -0.3302, 0.8387, -0.1572, -0.1611, 1.1531, -0.2621)

[node name="WeaponRackProp" instance=ExtResource("1_wapht")]

[node name="WeaponRack" parent="Meshes" index="0" instance=ExtResource("2_mp72f")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
shape = SubResource("ConcavePolygonShape3D_tr8ma")
