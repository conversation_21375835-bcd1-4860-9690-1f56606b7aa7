[preset.0]

name="Windows Desktop"
platform="Windows Desktop"
runnable=true
advanced_options=true
dedicated_server=false
custom_features=""
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../animalrush.exe"
encryption_include_filters="*.gd, *.tscn"
encryption_exclude_filters=""
encrypt_pck=true
encrypt_directory=true
script_export_mode=2

[preset.0.options]

custom_template/debug=""
custom_template/release="C:/develop/godot/bin/godot.windows.template_release.x86_64.exe"
debug/export_console_wrapper=1
binary_format/embed_pck=true
texture_format/s3tc_bptc=true
texture_format/etc2_astc=false
binary_format/architecture="x86_64"
codesign/enable=false
codesign/timestamp=true
codesign/timestamp_server_url=""
codesign/digest_algorithm=1
codesign/description=""
codesign/custom_options=PackedStringArray()
application/modify_resources=true
application/icon=""
application/console_wrapper_icon=""
application/icon_interpolation=4
application/file_version=""
application/product_version=""
application/company_name=""
application/product_name=""
application/file_description=""
application/copyright=""
application/trademarks=""
application/export_angle=0
application/export_d3d12=0
application/d3d12_agility_sdk_multiarch=true
ssh_remote_deploy/enabled=false
ssh_remote_deploy/host="user@host_ip"
ssh_remote_deploy/port="22"
ssh_remote_deploy/extra_args_ssh=""
ssh_remote_deploy/extra_args_scp=""
ssh_remote_deploy/run_script="Expand-Archive -LiteralPath '{temp_dir}\\{archive_name}' -DestinationPath '{temp_dir}'
$action = New-ScheduledTaskAction -Execute '{temp_dir}\\{exe_name}' -Argument '{cmd_args}'
$trigger = New-ScheduledTaskTrigger -Once -At 00:00
$settings = New-ScheduledTaskSettingsSet
$task = New-ScheduledTask -Action $action -Trigger $trigger -Settings $settings
Register-ScheduledTask godot_remote_debug -InputObject $task -Force:$true
Start-ScheduledTask -TaskName godot_remote_debug
while (Get-ScheduledTask -TaskName godot_remote_debug | ? State -eq running) { Start-Sleep -Milliseconds 100 }
Unregister-ScheduledTask -TaskName godot_remote_debug -Confirm:$false -ErrorAction:SilentlyContinue"
ssh_remote_deploy/cleanup_script="Stop-ScheduledTask -TaskName godot_remote_debug -ErrorAction:SilentlyContinue
Unregister-ScheduledTask -TaskName godot_remote_debug -Confirm:$false -ErrorAction:SilentlyContinue
Remove-Item -Recurse -Force '{temp_dir}'"

[preset.1]

name="Linux/X11"
platform="Linux"
runnable=true
advanced_options=false
dedicated_server=false
custom_features=""
export_filter="all_resources"
include_filter="*.env,*.csv"
exclude_filter=""
export_path="../Release/Linux/animalrush.x86_64"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.1.options]

custom_template/debug=""
custom_template/release=""
debug/export_console_wrapper=2
binary_format/embed_pck=true
texture_format/s3tc_bptc=true
texture_format/etc2_astc=false
binary_format/architecture="x86_64"
ssh_remote_deploy/enabled=false
ssh_remote_deploy/host="user@host_ip"
ssh_remote_deploy/port="22"
ssh_remote_deploy/extra_args_ssh=""
ssh_remote_deploy/extra_args_scp=""
ssh_remote_deploy/run_script="#!/usr/bin/env bash
export DISPLAY=:0
unzip -o -q \"{temp_dir}/{archive_name}\" -d \"{temp_dir}\"
\"{temp_dir}/{exe_name}\" {cmd_args}"
ssh_remote_deploy/cleanup_script="#!/usr/bin/env bash
kill $(pgrep -x -f \"{temp_dir}/{exe_name} {cmd_args}\")
rm -rf \"{temp_dir}\""
texture_format/bptc=true
texture_format/s3tc=true
texture_format/etc=false
texture_format/etc2=false
debug/export_console_script=1

[preset.2]

name="Android Myket V7"
platform="Android"
runnable=false
advanced_options=false
dedicated_server=false
custom_features="Myket"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../Release/1.26.2/MyketV7.apk"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.2.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=0
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=false
plugins/GodotBilling_myket=true
plugins/GodotGooglePlayBilling=false
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=true
architectures/arm64-v8a=false
architectures/x86=false
architectures/x86_64=false
version/code=143
version/name="1.27.21"
package/unique_name="ir.sizakgames.animalrushmmo"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false

[preset.3]

name="Android Myket V8"
platform="Android"
runnable=true
advanced_options=true
dedicated_server=false
custom_features="Myket"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../Release/1.26.2/MyketV8.apk"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.3.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=0
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=false
plugins/GodotBilling_myket=true
plugins/GodotGooglePlayBilling=false
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=false
architectures/arm64-v8a=true
architectures/x86=false
architectures/x86_64=false
version/code=150
version/name="1.28"
package/unique_name="ir.sizakgames.animalrushmmo"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false

[preset.4]

name="Android Cafebazaar AAB"
platform="Android"
runnable=false
advanced_options=false
dedicated_server=false
custom_features="Bazaar"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../Release/aab/141.aab"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.4.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=1
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=true
plugins/GodotBilling_myket=false
plugins/GodotGooglePlayBilling=false
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=false
architectures/arm64-v8a=true
architectures/x86=false
architectures/x86_64=false
version/code=144
version/name="1.27.21"
package/unique_name="ir.sizakgames.animalrushmmo"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false

[preset.5]

name="Android NoCache"
platform="Android"
runnable=false
advanced_options=false
dedicated_server=false
custom_features="NoCache, Zarinpal"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../Release/1.26.2/nocache.apk"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.5.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=0
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=false
plugins/GodotBilling_myket=true
plugins/GodotGooglePlayBilling=false
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=true
architectures/arm64-v8a=true
architectures/x86=false
architectures/x86_64=false
version/code=144
version/name="1.27.21"
package/unique_name="ir.sizakgames.animalrushmmo"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false

[preset.6]

name="IPG V7"
platform="Android"
runnable=false
advanced_options=false
dedicated_server=false
custom_features="Zarinpal"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../Release/1.26.2/IPGV7.apk"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.6.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=0
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=false
plugins/GodotBilling_myket=false
plugins/GodotGooglePlayBilling=false
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=true
architectures/arm64-v8a=false
architectures/x86=false
architectures/x86_64=false
version/code=150
version/name="1.28"
package/unique_name="ir.sizakgames.animalrushmmo"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false

[preset.7]

name="IPG V8"
platform="Android"
runnable=false
advanced_options=false
dedicated_server=false
custom_features="Zarinpal"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="../Release/1.26.2/tesr.apk"
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.7.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=0
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=false
plugins/GodotBilling_myket=false
plugins/GodotGooglePlayBilling=false
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=false
architectures/arm64-v8a=true
architectures/x86=false
architectures/x86_64=false
version/code=151
version/name="1.28"
package/unique_name="ir.sizakgames.animalrushmmo"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false

[preset.8]

name="GooglePlay"
platform="Android"
runnable=false
advanced_options=false
dedicated_server=false
custom_features="Google"
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path=""
encryption_include_filters=""
encryption_exclude_filters=""
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.8.options]

custom_template/debug=""
custom_template/release=""
gradle_build/use_gradle_build=true
gradle_build/gradle_build_directory=""
gradle_build/android_source_template=""
gradle_build/compress_native_libraries=false
gradle_build/export_format=1
gradle_build/min_sdk=""
gradle_build/target_sdk="34"
plugins/AFirebase=true
plugins/FirebaseCloudMessaging=true
plugins/GodotAdivery=true
plugins/GodotBilling_bazaar=false
plugins/GodotBilling_myket=false
plugins/GodotGooglePlayBilling=true
plugins/GodotLivekit=true
plugins/GodotTenjin=true
plugins/LocalNotification=true
plugins/NativeMethods=true
plugins/Tenjin=true
architectures/armeabi-v7a=true
architectures/arm64-v8a=true
architectures/x86=false
architectures/x86_64=false
version/code=150
version/name="1.27.21"
package/unique_name="com.sizak.animalrp"
package/name="Animal Rush"
package/signed=true
package/app_category=2
package/retain_data_on_uninstall=false
package/exclude_from_recents=false
package/show_in_android_tv=false
package/show_in_app_library=true
package/show_as_launcher_app=false
launcher_icons/main_192x192="res://icon.png"
launcher_icons/adaptive_foreground_432x432="res://icon.png"
launcher_icons/adaptive_background_432x432="res://icon.png"
graphics/opengl_debug=false
xr_features/xr_mode=0
screen/immersive_mode=true
screen/support_small=true
screen/support_normal=true
screen/support_large=true
screen/support_xlarge=true
user_data_backup/allow=false
command_line/extra_args=""
apk_expansion/enable=false
apk_expansion/SALT="mygame"
apk_expansion/public_key="MIIDWzCCAkOgAwIBAgIEcSmY2TANBgkqhkiG9w0BAQsFADBdMQswCQYDVQQGEwJJ
UjEQMA4GA1UECBMHVW5rbm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRa
YW5kMQ0wCwYDVQQLEwRaYW5kMQ0wCwYDVQQDEwRTaW5hMCAXDTIzMDcxMjEyNDQz
NVoYDzIwNTAxMTI3MTI0NDM1WjBdMQswCQYDVQQGEwJJUjEQMA4GA1UECBMHVW5r
bm93bjEPMA0GA1UEBxMGVGVocmFuMQ0wCwYDVQQKEwRaYW5kMQ0wCwYDVQQLEwRa
YW5kMQ0wCwYDVQQDEwRTaW5hMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAxe71jvjHaLFVv57F6H/JUQ407NCeRBqKZLOF3U/pL/0onwP60TWgOj0IZ7BA
VUyNDkREJPBJlcF/9miqim1avIpjsZdkp+JBDBHQ6PG0fJGSYlt5sB4ENZ3H8QRm
V3XKZ1gz6+vTyAlljAsISMw20jUiJJHeSgio8Gn5+GTNcW2qrNu5hSv0eZxwWp21
1yRWOejMGBgi2xCybay4lwcF73MDzYntRC5GT8NtxiC+3md77a7fG+Rse6JcL22q
tA+vYggjAvJZJyknzS9jPnTNuIw+nSeGYji61eYz6Kd5FEu8PLVW/5JGUaVQgs9a
qpdFamGVsz74nkNcSYTSoz6CKQIDAQABoyEwHzAdBgNVHQ4EFgQUEfPC4jQsu9PV
AF9pu1MCujVqin4wDQYJKoZIhvcNAQELBQADggEBAKZ5QD9H0CyjW5PVtKUPSsXg
Btj0o7kUxe1J5vXNkcZmucvYS0EDvG0h28HpDpH5KsqWt+nWtjCzHB93PQDl69qE
wv+96BDjTfHFCbcCjq4kbJymBviqGZR2EZQqGR3S+pGHr7NZg3cPQklJ5iwyYPDZ
N1P/xdRcIYuSRPe8dFlQOoMaR5VLnNDt0f53Kz5psO4TvAbu3uRx/jOe2i4cU1nB
AM5/86dLM+jw9/vycoYAJCtkDahhrn6dQwi2EJrDBbhTCGtfAeRnWlasa70AAEdu
1tPFc39ZOpArqksF76XAKKFAQvBVVa3qD+bMcjag5+UH+UShGNbueLk0NCki15U="
permissions/custom_permissions=PackedStringArray("android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM")
permissions/access_checkin_properties=false
permissions/access_coarse_location=false
permissions/access_fine_location=false
permissions/access_location_extra_commands=false
permissions/access_mock_location=false
permissions/access_network_state=true
permissions/access_surface_flinger=false
permissions/access_wifi_state=false
permissions/account_manager=false
permissions/add_voicemail=false
permissions/authenticate_accounts=false
permissions/battery_stats=false
permissions/bind_accessibility_service=false
permissions/bind_appwidget=false
permissions/bind_device_admin=false
permissions/bind_input_method=false
permissions/bind_nfc_service=false
permissions/bind_notification_listener_service=false
permissions/bind_print_service=false
permissions/bind_remoteviews=false
permissions/bind_text_service=false
permissions/bind_vpn_service=false
permissions/bind_wallpaper=false
permissions/bluetooth=false
permissions/bluetooth_admin=false
permissions/bluetooth_privileged=false
permissions/brick=false
permissions/broadcast_package_removed=false
permissions/broadcast_sms=false
permissions/broadcast_sticky=false
permissions/broadcast_wap_push=false
permissions/call_phone=false
permissions/call_privileged=false
permissions/camera=false
permissions/capture_audio_output=false
permissions/capture_secure_video_output=false
permissions/capture_video_output=false
permissions/change_component_enabled_state=false
permissions/change_configuration=false
permissions/change_network_state=false
permissions/change_wifi_multicast_state=false
permissions/change_wifi_state=false
permissions/clear_app_cache=false
permissions/clear_app_user_data=false
permissions/control_location_updates=false
permissions/delete_cache_files=false
permissions/delete_packages=false
permissions/device_power=false
permissions/diagnostic=false
permissions/disable_keyguard=false
permissions/dump=false
permissions/expand_status_bar=false
permissions/factory_test=false
permissions/flashlight=false
permissions/force_back=false
permissions/get_accounts=false
permissions/get_package_size=false
permissions/get_tasks=false
permissions/get_top_activity_info=false
permissions/global_search=false
permissions/hardware_test=false
permissions/inject_events=false
permissions/install_location_provider=false
permissions/install_packages=false
permissions/install_shortcut=false
permissions/internal_system_window=false
permissions/internet=true
permissions/kill_background_processes=false
permissions/location_hardware=false
permissions/manage_accounts=false
permissions/manage_app_tokens=false
permissions/manage_documents=false
permissions/manage_external_storage=false
permissions/master_clear=false
permissions/media_content_control=false
permissions/modify_audio_settings=false
permissions/modify_phone_state=false
permissions/mount_format_filesystems=false
permissions/mount_unmount_filesystems=false
permissions/nfc=false
permissions/persistent_activity=false
permissions/post_notifications=false
permissions/process_outgoing_calls=false
permissions/read_calendar=false
permissions/read_call_log=false
permissions/read_contacts=false
permissions/read_external_storage=false
permissions/read_frame_buffer=false
permissions/read_history_bookmarks=false
permissions/read_input_state=false
permissions/read_logs=false
permissions/read_phone_state=false
permissions/read_profile=false
permissions/read_sms=false
permissions/read_social_stream=false
permissions/read_sync_settings=false
permissions/read_sync_stats=false
permissions/read_user_dictionary=false
permissions/reboot=false
permissions/receive_boot_completed=false
permissions/receive_mms=false
permissions/receive_sms=false
permissions/receive_wap_push=false
permissions/record_audio=false
permissions/reorder_tasks=false
permissions/restart_packages=false
permissions/send_respond_via_message=false
permissions/send_sms=false
permissions/set_activity_watcher=false
permissions/set_alarm=true
permissions/set_always_finish=false
permissions/set_animation_scale=false
permissions/set_debug_app=false
permissions/set_orientation=true
permissions/set_pointer_speed=false
permissions/set_preferred_applications=false
permissions/set_process_limit=false
permissions/set_time=false
permissions/set_time_zone=false
permissions/set_wallpaper=false
permissions/set_wallpaper_hints=false
permissions/signal_persistent_processes=false
permissions/status_bar=false
permissions/subscribed_feeds_read=false
permissions/subscribed_feeds_write=false
permissions/system_alert_window=false
permissions/transmit_ir=false
permissions/uninstall_shortcut=false
permissions/update_device_stats=false
permissions/use_credentials=false
permissions/use_sip=false
permissions/vibrate=false
permissions/wake_lock=false
permissions/write_apn_settings=false
permissions/write_calendar=false
permissions/write_call_log=false
permissions/write_contacts=false
permissions/write_external_storage=false
permissions/write_gservices=false
permissions/write_history_bookmarks=false
permissions/write_profile=false
permissions/write_secure_settings=false
permissions/write_settings=false
permissions/write_sms=false
permissions/write_social_stream=false
permissions/write_sync_settings=false
permissions/write_user_dictionary=false
launcher_icons/adaptive_monochrome_432x432=""
plugins/GameAnalytics=false
xr_features/hand_tracking=0
xr_features/hand_tracking_frequency=0
xr_features/passthrough=0
plugins/AdiveryPlugin=false
plugins/libadivery=false
