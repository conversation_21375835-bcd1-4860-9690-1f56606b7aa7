[gd_scene load_steps=23 format=3 uid="uid://b1au20ihj4qs6"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntLobbyScene.gd" id="1_lobby"]
[ext_resource type="PackedScene" uid="uid://c0bvc7tlsk7xo" path="res://Scenes/ui/character_preview.tscn" id="2_char_preview"]
[ext_resource type="Texture2D" uid="uid://dxe6urc0q6q75" path="res://Scenes/ui/assets/main-menubg.PNG" id="2_n87jp"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="3_custom_button"]
[ext_resource type="PackedScene" uid="uid://dfw3vd3t4jtva" path="res://Scenes/ui/coin_show.tscn" id="4_coin_show"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="5_font"]
[ext_resource type="Texture2D" uid="uid://cdi8kfb3vhhfs" path="res://Scenes/ui/assets/button-back.png" id="6_08ri4"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="7_bg"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="8_loading"]
[ext_resource type="Script" path="res://Scenes/CharacterPreviewImage.gd" id="8_qbsso"]
[ext_resource type="Texture2D" uid="uid://cl4cgb04g3uco" path="res://Scenes/ui/assets/show.png" id="9_ahhs3"]
[ext_resource type="Texture2D" uid="uid://cwyfrihlqvhba" path="res://prophaunt/assets/ui/smart.png" id="9_iskbf"]
[ext_resource type="PackedScene" uid="uid://7hwa1a8r5cqj" path="res://prophaunt/scenes/WeaponSelector.tscn" id="9_weapon_selector"]
[ext_resource type="Texture2D" uid="uid://b7fw16alcvxhr" path="res://prophaunt/assets/Guns/Scene/blaster_d.png" id="10_q32ip"]
[ext_resource type="Texture2D" uid="uid://c58ekxw3b2tuj" path="res://prophaunt/assets/ui/k1.png" id="10_ruo68"]
[ext_resource type="Texture2D" uid="uid://ffpbl5v0pvqj" path="res://prophaunt/assets/ui/h1.png" id="11_o4sja"]
[ext_resource type="Texture2D" uid="uid://cekkihvqp513q" path="res://Scenes/ui/plus.png" id="11_yu2im"]
[ext_resource type="Texture2D" uid="uid://0pg6iqc4n45o" path="res://Scenes/ui/assets/yellow.png" id="16_s3qn6"]

[sub_resource type="ViewportTexture" id="ViewportTexture_vfqyo"]
viewport_path = NodePath(".")

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ij3db"]
bg_color = Color(0.227451, 0.227451, 0.227451, 0.827451)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_l1rqa"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_bg"]
texture = ExtResource("7_bg")

[node name="ProphauntLobby" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_lobby")

[node name="BG" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_n87jp")
expand_mode = 2
stretch_mode = 6
metadata/_edit_lock_ = true

[node name="CharacterPreviewImage" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = -68.0
offset_right = 5.0
offset_bottom = -218.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("ViewportTexture_vfqyo")
expand_mode = 2
stretch_mode = 5
script = ExtResource("8_qbsso")
metadata/_edit_lock_ = true

[node name="LoadingElement" parent="CharacterPreviewImage" instance=ExtResource("8_loading")]
visible = false
custom_minimum_size = Vector2(40, 40)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -19.9999
offset_top = 69.0
offset_right = 20.0001
offset_bottom = 109.0

[node name="TopPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0
grow_horizontal = 2

[node name="BackButton" parent="TopPanel" instance=ExtResource("3_custom_button")]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 0.5
offset_left = 20.0
offset_top = -40.0
offset_right = 120.0
offset_bottom = 40.0
grow_horizontal = 1
pivot_offset = Vector2(50, 40)

[node name="TextureRect" type="TextureRect" parent="TopPanel/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_right = 7.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("6_08ri4")
expand_mode = 1
stretch_mode = 4
metadata/_edit_lock_ = true

[node name="CurrencyPanel" type="Control" parent="TopPanel"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -320.0
offset_bottom = 100.0
grow_horizontal = 0

[node name="CoinShow" parent="TopPanel/CurrencyPanel" instance=ExtResource("4_coin_show")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -150.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 70.0
grow_horizontal = 0
grow_vertical = 1
link_to_shop = false

[node name="SmartShow" type="Control" parent="TopPanel/CurrencyPanel"]
custom_minimum_size = Vector2(120, 50)
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -314.0
offset_top = 20.0
offset_right = -194.0
offset_bottom = 70.0
grow_horizontal = 0

[node name="BG" type="NinePatchRect" parent="TopPanel/CurrencyPanel/SmartShow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("9_ahhs3")
patch_margin_left = 33
patch_margin_top = 17
patch_margin_right = 25
patch_margin_bottom = 21

[node name="smart" type="TextureRect" parent="TopPanel/CurrencyPanel/SmartShow"]
offset_left = 11.0
offset_top = 5.0
offset_right = 56.0
offset_bottom = 40.0
mouse_filter = 2
texture = ExtResource("9_iskbf")
expand_mode = 1
stretch_mode = 4

[node name="SmartLabel" type="Label" parent="TopPanel/CurrencyPanel/SmartShow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 32.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 20
text = "100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CharacterContainer" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="CharacterPreview" parent="CharacterContainer" instance=ExtResource("2_char_preview")]
transparent_bg = true

[node name="WeaponPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 50.0
offset_top = -100.0
offset_right = 200.0
offset_bottom = 59.0
grow_vertical = 2

[node name="Panel" type="Panel" parent="WeaponPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_ij3db")

[node name="WeaponContainer" type="VBoxContainer" parent="WeaponPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="WeaponTitle" type="Label" parent="WeaponPanel/WeaponContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "WEAPON"
horizontal_alignment = 1

[node name="WeaponIcon" type="TextureRect" parent="WeaponPanel/WeaponContainer"]
custom_minimum_size = Vector2(100, 100)
layout_mode = 2
size_flags_horizontal = 4
texture = ExtResource("10_q32ip")
expand_mode = 1
stretch_mode = 6

[node name="WeaponSelectorButton" type="Button" parent="WeaponPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="AbilitiesPanel" type="Control" parent="."]
custom_minimum_size = Vector2(0, 140)
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -70.0
offset_right = -50.0
offset_bottom = 70.0
grow_horizontal = 0
grow_vertical = 2

[node name="Panel" type="Panel" parent="AbilitiesPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_ij3db")

[node name="AbilitiesContainer" type="VBoxContainer" parent="AbilitiesPanel"]
custom_minimum_size = Vector2(0, 140)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 7

[node name="GrenadesContainer" type="HBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
custom_minimum_size = Vector2(0, 70)
layout_mode = 2
theme_override_constants/separation = 10
alignment = 1

[node name="TextureRect" type="TextureRect" parent="AbilitiesPanel/AbilitiesContainer/GrenadesContainer"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
texture = ExtResource("10_ruo68")
expand_mode = 1
stretch_mode = 5

[node name="GrenadesCount" type="Label" parent="AbilitiesPanel/AbilitiesContainer/GrenadesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
text = "0"

[node name="PurchaseGrenade" type="Button" parent="AbilitiesPanel/AbilitiesContainer/GrenadesContainer"]
custom_minimum_size = Vector2(30, 30)
layout_mode = 2
size_flags_vertical = 4
theme_override_styles/focus = SubResource("StyleBoxEmpty_l1rqa")
flat = true

[node name="TextureRect" type="TextureRect" parent="AbilitiesPanel/AbilitiesContainer/GrenadesContainer/PurchaseGrenade"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("11_yu2im")
expand_mode = 1
stretch_mode = 4

[node name="HexesContainer" type="HBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
custom_minimum_size = Vector2(0, 70)
layout_mode = 2
theme_override_constants/separation = 10
alignment = 1

[node name="TextureRect" type="TextureRect" parent="AbilitiesPanel/AbilitiesContainer/HexesContainer"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
texture = ExtResource("11_o4sja")
expand_mode = 1
stretch_mode = 5

[node name="HexesCount" type="Label" parent="AbilitiesPanel/AbilitiesContainer/HexesContainer"]
layout_mode = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 1
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
text = "0"

[node name="PurchaseHex" type="Button" parent="AbilitiesPanel/AbilitiesContainer/HexesContainer"]
custom_minimum_size = Vector2(30, 30)
layout_mode = 2
size_flags_vertical = 4
theme_override_styles/focus = SubResource("StyleBoxEmpty_l1rqa")
flat = true

[node name="TextureRect" type="TextureRect" parent="AbilitiesPanel/AbilitiesContainer/HexesContainer/PurchaseHex"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("11_yu2im")
expand_mode = 1
stretch_mode = 4

[node name="BottomPanel" type="Control" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -100.0
grow_horizontal = 2
grow_vertical = 0

[node name="StartButton" parent="BottomPanel" instance=ExtResource("3_custom_button")]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -150.0
offset_top = -80.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(65, 30)

[node name="StartButtonBG" type="TextureRect" parent="BottomPanel/StartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("16_s3qn6")
expand_mode = 1
stretch_mode = 4

[node name="StartButtonLabel" type="Label" parent="BottomPanel/StartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "START"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HTTPRequest" type="HTTPRequest" parent="."]

[node name="WeaponSelectHTTPRequest" type="HTTPRequest" parent="."]

[node name="WeaponSelector" parent="." instance=ExtResource("9_weapon_selector")]
visible = false
layout_mode = 1

[node name="LoadingPanel" type="Control" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="LoadingBG" type="Panel" parent="LoadingPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_bg")

[node name="LoadingElement" parent="LoadingPanel" instance=ExtResource("8_loading")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0

[node name="LoadingLabel" type="Label" parent="LoadingPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = 30.0
offset_right = 100.0
offset_bottom = 80.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 24
text = "LOADING"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="gui_input" from="CharacterPreviewImage" to="CharacterPreviewImage" method="_on_full_screen_gui_input"]
[connection signal="pressed" from="TopPanel/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="WeaponPanel/WeaponSelectorButton" to="." method="_on_weapon_selector_button_pressed"]
[connection signal="pressed" from="AbilitiesPanel/AbilitiesContainer/GrenadesContainer/PurchaseGrenade" to="." method="_on_purchase_grenade_pressed"]
[connection signal="pressed" from="AbilitiesPanel/AbilitiesContainer/HexesContainer/PurchaseHex" to="." method="_on_purchase_hex_pressed"]
[connection signal="pressed" from="BottomPanel/StartButton" to="." method="_on_start_button_pressed"]
[connection signal="request_completed" from="HTTPRequest" to="." method="_on_http_request_completed"]
[connection signal="weapon_selected" from="WeaponSelector" to="." method="_on_weapon_selected"]
