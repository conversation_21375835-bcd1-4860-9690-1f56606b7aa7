[gd_scene load_steps=7 format=3 uid="uid://dfw3vd3t4jtva"]

[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="1_1fbju"]
[ext_resource type="Script" path="res://Scenes/ui/coin_show.gd" id="2_4bl4c"]
[ext_resource type="Texture2D" uid="uid://cl4cgb04g3uco" path="res://Scenes/ui/assets/show.png" id="2_ksons"]
[ext_resource type="Texture2D" uid="uid://8q8ki5ld0acy" path="res://Scenes/ui/assets/Coin.png" id="4_ocoyj"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="4_vk3ku"]
[ext_resource type="Texture2D" uid="uid://bvk463dv6y6w6" path="res://Scenes/ui/assets/Union.png" id="5_nf4h6"]

[node name="CoinShow" instance=ExtResource("1_1fbju")]
custom_minimum_size = Vector2(150, 50)
offset_right = -1050.0
offset_bottom = -670.0
pivot_offset = Vector2(75, 25)
script = ExtResource("2_4bl4c")
link_to_shop = true

[node name="BG" type="NinePatchRect" parent="." index="1"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_ksons")
patch_margin_left = 33
patch_margin_top = 17
patch_margin_right = 25
patch_margin_bottom = 21

[node name="coin" type="TextureRect" parent="." index="2"]
layout_mode = 0
offset_left = 1.0
offset_top = 2.0
offset_right = 54.0
offset_bottom = 47.0
mouse_filter = 2
texture = ExtResource("4_ocoyj")
expand_mode = 1
stretch_mode = 4

[node name="CoinLabel" type="Label" parent="." index="3"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 38.0
offset_right = -37.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_constants/shadow_outline_size = 2
theme_override_fonts/font = ExtResource("4_vk3ku")
theme_override_font_sizes/font_size = 25
horizontal_alignment = 1
vertical_alignment = 1

[node name="TextureRect" type="TextureRect" parent="." index="4"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -34.0
offset_top = -13.0
offset_right = -13.0
offset_bottom = 13.0
grow_horizontal = 0
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("5_nf4h6")
expand_mode = 1
stretch_mode = 4

[connection signal="pressed" from="Button" to="." method="_on__button_pressed"]
