[gd_scene load_steps=6 format=4 uid="uid://c11ejothsg05c"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_7fttw"]

[sub_resource type="ArrayMesh" id="ArrayMesh_t4k2y"]
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.4, 1, 4.4),
"format": 34359742465,
"index_count": 150,
"index_data": PackedByteArray("AAABAAsACgALAAEAAQADABAAEQAQAAMABAASAAMAEQADABIABwAPAAYADwAOAAYABgAOAAQAEgAEAA4ADgAMABIAEQASAAwAEAARAA0ADAANABEABgAEAAgABQAIAAQADQAPABAADwAHABAABwAKABAAAQAQAAoADwANAA4ADAAOAA0ACQAIAAIABQACAAgACQACAAsAAAALAAIACwAKAAkABwAJAAoACAAJAAcABgAIAAcABQAEAAIABAADAAIAAwABAAIAAAACAAEAFgAcABQAGwAUABwAHQAaABwAGwAcABoAGwAaABQAGgAZABQAFAAZABMAGAATABkAEwAYABUAFwAVABgAFgAUABUAEwAVABQAFwAeABUAFgAVAB4AHAAWAB4AHQAcAB4A"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 31,
"vertex_data": PackedByteArray("AAAAQAAAAAD//38/AAAAQAAAAACZmZk///9/PwAAAAD//38/AAAAQAAAAAAAAABAmZmZPwAAAAAAAABA//9/PwAAAAAAAABAmZmZP5qZmT4AAABAmZmZP5qZmT6ZmZk///9/P5qZmT4AAABA//9/P5qZmT7//38/AAAAQJqZmT6ZmZk/AAAAQJqZmT7//38/mZkZQAAAgD+ZmRlAmZkZQAAAgD+ZmZk/mZmZPwAAgD+ZmRlAmZmZPwAAgD+ZmZk/mZkZQM3MzD6ZmZk/mZkZQM3MzD6ZmRlAmZmZP83MzD6ZmRlAAAAAQAAAAAAAAADAAAAAwAAAAAAAAADAAAAAQM3MTD4AAADAAAAAwM3MTD4AAADAAAAAQM3MTD4AAIA/AAAAQAAAAAAAAIA/AACAPwAAAAAAAIA/AACAPwAAAAAAAABAAAAAwAAAAAAAAABAAAAAwM3MTD4AAABAAACAP83MTD4AAABAAACAP83MTD4AAIA/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_wjjrj"]
resource_name = "RoofFlatCornerInner_roof-flat-corner-inner"
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.4, 1, 4.4),
"attribute_data": PackedByteArray("W6ucPnjLcT9bq5w+eMtxP1urnD54y3E/W6ucPnjLcT9bq5w+eMtxP1urnD54y3E/W6ucPq9TcT9bq5w+r1NxP1urnD6vU3E/W6ucPq9TcT9bq5w+r1NxP1urnD6vU3E/W6ucPnjLcT9bq5w+eMtxP1urnD6vU3E/W6ucPq9TcT9bq5w+eMtxP1urnD6vU3E/W6ucPnjLcT9bq5w+r1NxP1urnD4wPHA/W6ucPjA8cD9bq5w+MDxwP1urnD4wPHA/W6ucPnjLcT9bq5w+r1NxP1urnD7BK3E/W6ucPq9TcT9bq5w+MDxwP1urnD4wPHA/W6ucPnjLcT9bq5w+eMtxP1urnD6vU3E/W6ucPq9TcT9bq5w+MDxwP1urnD7BK3E/W6ucPjA8cD9bq5w+wStxP1urnD7BK3E/W6ucPjA8cD9bq5w+wStxP1urnD4wPHA/W6ucPsErcT9bq5w+MDxwP1urnD54y3E/W6ucPq9TcT9bq5w+MDxwP1urnD6vU3E/W6ucPsErcT9bq5w+wStxP1urnD54y3E/W6ucPnjLcT9bq5w+wStxP1urnD54y3E/W6ucPsErcT9bq5w+eMtxP1urnD6vU3E/W6ucPnjLcT9bq5w+r1NxP1urnD54y3E/W6ucPqZncT9bq5w+pmdxP1urnD4CoHA/W6ucPgKgcD9bq5w+AqBwP1urnD6mZ3E/W6ucPgKgcD9bq5w+pmdxP1urnD6mZ3E/W6ucPqZncT9bq5w+pmdxP1urnD6mZ3E/W6ucPqZncT9bq5w+pmdxP1urnD6mZ3E/W6ucPqZncT9bq5w+AqBwP1urnD4CoHA/W6ucPqZncT9bq5w+AqBwP1urnD6mZ3E/W6ucPgKgcD8AQJ0+M5NtPwBAnT4zk20/AECdPjOTbT8AQJ0+M5NtPwBAnT4zk20/AECdPjOTbT8="),
"format": 34359742487,
"index_count": 150,
"index_data": PackedByteArray("OwA5ADoAOAA6ADkANwA1ADYANAA2ADUAMwAxADIAMAAyADEALwAuAC0ALgArAC0ALQArACwAKgAsACsAKQAnACgAJgAoACcAJQAjACQAIgAkACMAIQAfACAAHgAgAB8AHQAcABoAHAAbABoAGwAZABoAGAAaABkAFwAVABYAFAAWABUAEwARABIAEAASABEADwANAA4ADAAOAA0ACwAKAAkABwAJAAoACAAJAAcABgAIAAcABQAEAAIABAADAAIAAwABAAIAAAACAAEAUQBPAFAATgBQAE8ATQBLAEwASgBMAEsASQBIAEcASABFAEcARwBFAEYARABGAEUAQwBBAEIAQABCAEEAPwA9AD4APAA+AD0AVwBTAFYAVQBWAFMAVABVAFMAUgBUAFMA"),
"material": ExtResource("1_7fttw"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 88,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_t4k2y")

[sub_resource type="BoxShape3D" id="BoxShape3D_ipfyk"]
size = Vector3(4.03613, 0.222321, 4.0238)

[sub_resource type="BoxShape3D" id="BoxShape3D_is38m"]
size = Vector3(1.20349, 0.714798, 1.2114)

[node name="RoofFlatCornerInner" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_wjjrj")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.012207, 0.0954437, 0.0128784)
shape = SubResource("BoxShape3D_ipfyk")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.80323, 0.650651, 1.79143)
shape = SubResource("BoxShape3D_is38m")
