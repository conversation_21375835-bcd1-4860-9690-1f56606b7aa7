[gd_scene load_steps=5 format=4 uid="uid://ba1bu2s1nxvbc"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_kudfm"]

[sub_resource type="ArrayMesh" id="ArrayMesh_s1pfo"]
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.15, 4.8, 4.15),
"format": 34359742465,
"index_count": 96,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACQAIAAYABgAHAAkACQADAAEAAQAIAAkADAAKAAsACwANAAwADAANAA4ADgAPAAwAAAAQAAEACAABABAAEAAGAAgAEAAEAAYAAgADAAkACQARAAIACQAHABEABwAFABEAEgAKAAwADAATABIADAAPABMADwAUABMAFgAVABIAEgATABYAFAAXABYAFgATABQADgAXABQAFAAPAA4AFQALAAoACgASABUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("////P/Xc0CYyM9M/mZkJQPXc0CYyM9M/////P5qZmUAyM9M/mZkJQJqZmUAyM9M/MjPTP/Xc0Cb///8/MjPTP5qZmUD///8/MjPTP/Xc0CaZmQlAMjPTP5qZmUCZmQlAmZkJQPXc0CaZmQlAmZkJQJqZmUCZmQlAAAAAQJqZmUAAAADAAAAAQAAAAAAAAADAAAAAQJqZmUAAAABAAAAAQAAAAAAAAABAAAAAwAAAAAAAAABAAAAAwJqZmUAAAABA////P/Xc0Cb///8/////P5qZmUD///8/ZmbmP5qZmUAAAADAZmbmP5qZmUBmZuY/AAAAwJqZmUBmZuY/ZmbmP6kT0CMAAADAZmbmP6kT0CNmZuY/AAAAwKkT0CNmZuY/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_6p8sg"]
resource_name = "WallCornerColumn_wall-corner-column"
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.15, 4.8, 4.15),
"attribute_data": PackedByteArray("0F6kPjzEcT/QXqQ+PMRxP9BepD48xHE/0F6kPjzEcT/QXqQ+PMRxP9BepD48xHE/0F6kPjzEcT/QXqQ+PMRxP9BepD48xHE/0F6kPjzEcT/QXqQ+PMRxP9BepD48xHE/0F6kPjzEcT/QXqQ+PMRxP9BepD48xHE/0F6kPjzEcT/i36Q+36xsP+LfpD7frGw/4t+kPt+sbD/i36Q+36xsP+LfpD7frGw/4t+kPt+sbD/i36Q+36xsP+LfpD7frGw/eFukPktfcT94W6Q+S19xP3hbpD5LX3E/eFukPktfcT94W6Q+S19xP3hbpD5LX3E/LLmkPuu9cT8suaQ+671xPyy5pD7rvXE/LLmkPuu9cT8suaQ+671xPyy5pD7rvXE/5b2kPgXrcD/lvaQ+BetwP+W9pD4F63A/5b2kPgXrcD/lvaQ+BetwP+W9pD4F63A/4t+kPt+sbD/i36Q+36xsP+LfpD7frGw/4t+kPt+sbD/i36Q+36xsP+LfpD7frGw/4t+kPt+sbD/i36Q+36xsP8g7pT6E23E/yDulPq9XcD/IO6U+hNtxP8g7pT6vV3A/yDulPoTbcT/IO6U+r1dwP8g7pT6E23E/yDulPq9XcD8="),
"format": 34359742487,
"index_count": 96,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGwAZABgAGAAcABsAGAAdABwAIAAeAB8AHwAhACAAHwAiACEAIgAjACEAJgAkACUAJQAnACYAJQAoACcAKAApACcALAAqACsAKwAtACwAMAAuAC8ALwAxADAANAAyADMAMwA1ADQAOAA2ADcANwA5ADgA"),
"material": ExtResource("1_kudfm"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 58,
"vertex_data": PackedByteArray("////P/Xc0CYyM9M/mZkJQPXc0CYyM9M/////P5qZmUAyM9M/mZkJQJqZmUAyM9M/MjPTP/Xc0Cb///8/MjPTP5qZmUD///8/MjPTP/Xc0CaZmQlAMjPTP5qZmUCZmQlAmZkJQPXc0CaZmQlAMjPTP/Xc0CaZmQlAmZkJQJqZmUCZmQlAMjPTP5qZmUCZmQlAmZkJQJqZmUAyM9M/mZkJQPXc0CYyM9M/mZkJQJqZmUCZmQlAmZkJQPXc0CaZmQlAAAAAQJqZmUAAAADAAAAAQAAAAAAAAADAAAAAQJqZmUAAAABAAAAAQAAAAAAAAABAAAAAQAAAAAAAAABAAAAAwAAAAAAAAABAAAAAQJqZmUAAAABAAAAAwJqZmUAAAABA////P/Xc0Cb///8/mZkJQPXc0CYyM9M/////P/Xc0CYyM9M/mZkJQPXc0CaZmQlAMjPTP/Xc0CaZmQlAMjPTP/Xc0Cb///8/mZkJQJqZmUAyM9M/mZkJQJqZmUCZmQlA////P5qZmUAyM9M/////P5qZmUD///8/MjPTP5qZmUCZmQlAMjPTP5qZmUD///8/AAAAQJqZmUAAAADAAAAAQJqZmUAAAABAZmbmP5qZmUAAAADAZmbmP5qZmUBmZuY/AAAAwJqZmUAAAABAAAAAwJqZmUBmZuY/ZmbmP6kT0CMAAADAZmbmP5qZmUAAAADAZmbmP6kT0CNmZuY/ZmbmP5qZmUBmZuY/AAAAwKkT0CNmZuY/ZmbmP6kT0CNmZuY/AAAAwJqZmUBmZuY/ZmbmP5qZmUBmZuY/AAAAwKkT0CNmZuY/AAAAwJqZmUBmZuY/AAAAwAAAAAAAAABAAAAAwJqZmUAAAABAAAAAQAAAAAAAAADAAAAAQJqZmUAAAADAZmbmP6kT0CMAAADAZmbmP5qZmUAAAADA/////////7//////////v/////////+//////////78AAP9/////vwAA/3////+/AAD/f////78AAP9/////v/9//3////8//3//f////z//f/9/////P/9//3////8/////f////7////9/////v////3////+/////f////7////9/////v////3////+/////f////7////9/////v/9//3////8//3//f////z//f/9/////P/9//3////8//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9///////+/AAD/f////78AAP9/////vwAA/3////+/AAD/f////7//////////v/////////+//////////7//////////vwAA/3////+/AAD/f////78AAP9/////vwAA/3////+//////////7//////////v/////////+//////////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_s1pfo")

[sub_resource type="BoxShape3D" id="BoxShape3D_jaynl"]
size = Vector3(0.2, 4.8, 4)

[node name="wall-corner-column" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_6p8sg")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.9, 2.4, 0)
shape = SubResource("BoxShape3D_jaynl")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 9.68575e-08, 2.4, 1.9)
shape = SubResource("BoxShape3D_jaynl")
