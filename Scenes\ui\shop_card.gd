extends Control

signal pressed

@onready var image = $Image
@onready var coin_label = $CoinLabel
@onready var price = $Price
@onready var title_label = $TitleLabel

var data

func set_data(_data, google_play=false):
	data = _data
	
	image.texture = load(data["image_path"])
	coin_label.text = str(data["coins"])
	price.text = str(data["price_str"])
	if data.has("title"):
		title_label.text = data["title"]
		
	if google_play:
		price.text = str(data["price_str_google"])
		title_label.text = data["title_google"]


func _on_button_pressed():
	emit_signal("pressed", data)


func _on_button_button_down():
	scale = Vector2(0.95, 0.95)


func _on_button_button_up():
	scale = Vector2(1, 1)
