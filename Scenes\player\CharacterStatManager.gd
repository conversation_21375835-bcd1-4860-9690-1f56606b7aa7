class_name  CharacterStat<PERSON>anager
extends Node


const DEFAULT_SPEED = 4.0
const DEFAULT_JUMP = 4.0
const DEFAULT_SLIDE = 7.0
const DEFAULT_AIR = 3.0

const MAX_HUNGER_EXTRA = 150.0
const MAX_HUNGER = 100.0
const MIN_SPEED = 2.4
const MAX_SPEED = 6.0
const MAX_SPEED_EXTRA = 8.0
const MIN_JUMP = 4.0
const MAX_JUMP = 4.0
const MAX_JUMP_EXTRA = 7.0
const hunger_delta = MAX_HUNGER_EXTRA / 3600
const hunger_alarm_interval = 5 * 60

const MAX_WARMTH = 100.0
const warmth_cold_delta = MAX_WARMTH / 1200#20 minutes
const warmth_warm_delta = MAX_WARMTH / 500#30 minutes

const lava_damage_rate = 5

var player: Character
var last_hunger_alarm = 0


func init(p:Character):
	player = p


func run(delta):
	if Constants.is_server:
		if player.is_bot():
			set_defaults()
		return


	if player.is_me():
		#print(player.global_position)
		Selector.my_hunger -= delta * hunger_delta
		Selector.my_hunger = clamp(Selector.my_hunger, 0, MAX_HUNGER_EXTRA)
		
		if player.is_in_cold_area:
			Selector.my_warmth -= delta * warmth_cold_delta
			if Selector.my_warmth <= 0:
				if not Constants.im_dead():
					if VehicleManager.ami_in_vehicle():
						VehicleManager.on_unmount_vehicle_pressed()
					else:
						player.freezeDeathState.start_state()
		else:
			Selector.my_warmth += delta * warmth_cold_delta
		Selector.my_warmth = clamp(Selector.my_warmth, 0, MAX_WARMTH)
		
		if player.is_in_lava_area:
			Selector.my_hunger -= lava_damage_rate * delta
		
		player.SPEED = calc_speed()
		player.JUMP_VELOCITY = calc_jump()
		player.AIR_SPEED = player.SPEED - 1
		player.SLIDE_SPEED = player.SPEED + 2
		if Selector.selected_game_mode in [Constants.GameMode.Race, Constants.GameMode.Prophaunt]:
			set_defaults()
		else:
			check_for_hunger_alarm()
		
		if Constants.is_desktop():
			if Input.is_action_just_pressed("Map+"):
				Selector.my_hunger += 10
			if Input.is_action_just_pressed("Map-"):
				Selector.my_hunger -= 10
			if Input.is_action_just_pressed("Warmth+"):
				Selector.my_warmth += 10
			if Input.is_action_just_pressed("Warmth-"):
				Selector.my_warmth -= 10


func calc_speed():
	if Selector.my_hunger >= MAX_HUNGER:
		return MAX_SPEED + (Selector.my_hunger - MAX_HUNGER) / (MAX_HUNGER_EXTRA - MAX_HUNGER) * (MAX_SPEED_EXTRA - MAX_SPEED)
	
	return MIN_SPEED + (Selector.my_hunger / MAX_HUNGER) * (MAX_SPEED - MIN_SPEED)


func calc_jump():
	if Selector.my_hunger <= MAX_HUNGER:
		return DEFAULT_JUMP
	
	return MAX_JUMP + (Selector.my_hunger - MAX_HUNGER) / (MAX_HUNGER_EXTRA - MAX_HUNGER) * (MAX_JUMP_EXTRA - MAX_JUMP)


func set_defaults():
	player.SPEED = DEFAULT_SPEED
	player.JUMP_VELOCITY = DEFAULT_JUMP
	player.SLIDE_SPEED = DEFAULT_SLIDE
	player.AIR_SPEED = DEFAULT_AIR


func consume_food(food_data):
	#print("consume food: ", food_data["name"])
	if Selector.my_hunger > food_data["max_hunger"]:
		return

	Selector.my_hunger += food_data["hunger"]
	Selector.my_hunger = clamp(Selector.my_hunger, 0, food_data["max_hunger"])
	DataSaver.send_save_request()


func check_for_hunger_alarm():
	if Selector.my_hunger >= 50:
		last_hunger_alarm = 0
		return
	if not Constants.client.connected:
		return
	
	if Constants.client.game_scene.loading_map_parent.visible:
		return
	
	var current_time = Time.get_unix_time_from_system()
	if (current_time - last_hunger_alarm) >= hunger_alarm_interval:
		last_hunger_alarm = current_time
		Constants.server.show_hunger_alarm.rpc_id(1)
		DataSaver.send_save_request()


func reset_alarm():
	last_hunger_alarm = Time.get_unix_time_from_system()
