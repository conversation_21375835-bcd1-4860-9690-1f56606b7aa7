extends "res://Scenes/ui/shop_card.gd"

@onready var character_image = $CharacterImage

signal show_info(data)

var change_time = 2
var FADE_OUT_TIME = 0.9
var FADE_IN_TIME = 0.9
var timer = 0
var characters = []
var index = 0

func set_data(_data, is_google=false):
	super(_data, is_google)
	handle_characters()

func handle_characters():
	for c in data["unlock_characters"]:
		var character = Selector.find_character_by_id_in_characters(c)
		characters.append(load(character["card_path"]))
	
	index = randi() % len(characters)
	change_character_image()


func _process(delta):
	timer += delta
	if timer > change_time:
		timer = 0
		change_character_image()


func change_character_image():
	index += 1
	if index >= len(characters):
		index = 0
	
	if len(characters) > 1 and character_image.texture != null:
		var tween = get_tree().create_tween()
		tween.tween_property(character_image, "modulate", Color("FFFFFF00"), FADE_OUT_TIME).set_trans(Tween.TRANS_QUINT).set_ease(Tween.EASE_IN)
		await get_tree().create_timer(FADE_OUT_TIME).timeout
	character_image.texture = characters[index]
	if len(characters) > 1:
		var tween = get_tree().create_tween()
		tween.tween_property(character_image, "modulate", Color("FFFFFFFF"), FADE_IN_TIME).set_trans(Tween.TRANS_QUINT).set_ease(Tween.EASE_OUT)
	#await get_tree().create_timer(FADE_IN_TIME).timeout


func _on_info_button_pressed():
	emit_signal("show_info", characters)
