[gd_scene load_steps=13 format=3 uid="uid://q5hqgjqfs6db"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntGame.gd" id="1_kg30s"]
[ext_resource type="PackedScene" uid="uid://dmu6rpse3pie5" path="res://prophaunt/ui/ProphauntGameUI.tscn" id="2_hhd8a"]
[ext_resource type="Script" path="res://Scenes/player/CameraController.gd" id="2_y8yry"]
[ext_resource type="PackedScene" uid="uid://cnxwv222lmpvj" path="res://Scenes/player/ProphauntTouchController.tscn" id="4_1w8hp"]
[ext_resource type="Texture2D" uid="uid://r7n4eqwqtiue" path="res://Scenes/ui/assets/gradiant_bg.png" id="5_p2n2n"]
[ext_resource type="PackedScene" uid="uid://b3elqmgq54s52" path="res://Scenes/ui/loading_element.tscn" id="6_ipia3"]
[ext_resource type="FontFile" uid="uid://djnxbn02v2032" path="res://Scenes/ui/assets/Font/Lalezar-Regular.ttf" id="7_6y7fc"]
[ext_resource type="PackedScene" uid="uid://jqbc5awj1g5t" path="res://Scenes/ui/CustomButton.tscn" id="8_nsl0r"]
[ext_resource type="Texture2D" uid="uid://cfkrhs0n66fyu" path="res://Scenes/ui/assets/button_light.png" id="9_8ox55"]

[sub_resource type="SphereMesh" id="SphereMesh_l6eb2"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_qne4x"]
texture = ExtResource("5_p2n2n")

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_5dy8m"]
texture = ExtResource("5_p2n2n")

[node name="ProphauntGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_kg30s")

[node name="Player" type="Node3D" parent="."]

[node name="Remotes" type="Node3D" parent="."]

[node name="Pointer" type="Node3D" parent="." groups=["pointer"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 3)

[node name="ProphauntGameUI" parent="." instance=ExtResource("2_hhd8a")]
layout_mode = 1

[node name="CameraController" type="Node3D" parent="."]
script = ExtResource("2_y8yry")

[node name="SpringArm3D" type="SpringArm3D" parent="CameraController"]
transform = Transform3D(-1, 5.53401e-08, -1.40489e-07, 0, 0.930418, 0.366501, 1.50996e-07, 0.366501, -0.930418, 0, 2.706, 0)
collision_mask = 146
spring_length = 4.0
margin = 0.2

[node name="Camera3D" type="Camera3D" parent="CameraController/SpringArm3D"]
transform = Transform3D(1, 5.38973e-08, -1.66275e-07, -6.56449e-08, 0.997442, -0.0714804, 1.61997e-07, 0.0714804, 0.997442, 7.87108e-12, -0.454067, -0.178958)
current = true
fov = 90.0

[node name="LookAt" type="Node3D" parent="CameraController"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 4)

[node name="MeshInstance3D" type="MeshInstance3D" parent="CameraController/LookAt"]
visible = false
mesh = SubResource("SphereMesh_l6eb2")

[node name="ProphauntTouchController" parent="." instance=ExtResource("4_1w8hp")]
layout_mode = 1
max_rot_x = 1.1
min_rot_x = -1.1

[node name="MapHTTPRequest" type="HTTPRequest" parent="."]

[node name="ClientRequest" type="HTTPRequest" parent="."]

[node name="FindGameHUD" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Loading" type="Panel" parent="FindGameHUD"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_qne4x")

[node name="LoadingElement" parent="FindGameHUD/Loading" instance=ExtResource("6_ipia3")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -375.0
offset_top = -68.0
offset_right = -335.0
offset_bottom = -28.0
rotation = 4.15823
force_process = true

[node name="WaitingLabel" type="Label" parent="FindGameHUD/Loading"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -338.0
offset_top = -76.0
offset_right = -42.0
offset_bottom = -18.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("7_6y7fc")
theme_override_font_sizes/font_size = 30
text = "WAITING_PLAYERS"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ServerCheckLabel" type="Label" parent="FindGameHUD/Loading"]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -338.0
offset_top = -76.0
offset_right = -42.0
offset_bottom = -18.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("7_6y7fc")
theme_override_font_sizes/font_size = 30
horizontal_alignment = 1
vertical_alignment = 1

[node name="count" type="Label" parent="FindGameHUD/Loading"]
layout_direction = 2
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -338.0
offset_top = -25.0
offset_right = -42.0
offset_bottom = 73.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("7_6y7fc")
theme_override_font_sizes/font_size = 60
horizontal_alignment = 1
vertical_alignment = 1
text_direction = 1

[node name="time" type="Label" parent="FindGameHUD/Loading"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -33.5
offset_top = 30.0
offset_right = 33.5
offset_bottom = 88.0
grow_horizontal = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("7_6y7fc")
theme_override_font_sizes/font_size = 30
horizontal_alignment = 1
vertical_alignment = 1

[node name="FindGame" type="Panel" parent="FindGameHUD"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_5dy8m")

[node name="Label" type="Label" parent="FindGameHUD/FindGame"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -77.0
offset_right = -4.0
offset_bottom = -19.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("7_6y7fc")
theme_override_font_sizes/font_size = 30
text = "FIND_GAME"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LoadingElement" parent="FindGameHUD/FindGame" instance=ExtResource("6_ipia3")]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -305.0
offset_top = -68.0
offset_right = -265.0
offset_bottom = -28.0
rotation = 4.15823

[node name="ExitButton" parent="FindGameHUD" instance=ExtResource("8_nsl0r")]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -250.0
offset_top = -100.0
offset_right = -50.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(100, 40)

[node name="TextureRect" type="TextureRect" parent="FindGameHUD/ExitButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
texture = ExtResource("9_8ox55")
expand_mode = 1
stretch_mode = 4

[node name="Label" type="Label" parent="FindGameHUD/ExitButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 5
theme_override_constants/shadow_outline_size = 3
theme_override_fonts/font = ExtResource("7_6y7fc")
theme_override_font_sizes/font_size = 30
text = "CANCEL"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="exit_game" from="ProphauntGameUI" to="." method="_on_prophaunt_game_ui_exit_game"]
[connection signal="ability_used" from="ProphauntTouchController" to="." method="_on_ability_used"]
[connection signal="look_at" from="ProphauntTouchController" to="." method="_on_prophaunt_touch_controller_look_at"]
[connection signal="request_completed" from="MapHTTPRequest" to="." method="_on_select_map_request_completed"]
[connection signal="request_completed" from="ClientRequest" to="." method="on_game_server_find"]
[connection signal="pressed" from="FindGameHUD/ExitButton" to="." method="_on_find_cancel_button_pressed"]
