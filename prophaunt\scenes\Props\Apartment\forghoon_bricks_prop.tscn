[gd_scene load_steps=6 format=3 uid="uid://cquetkrbas8dd"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_ll0rh"]
[ext_resource type="PackedScene" uid="uid://nrbny0j80sqh" path="res://prophaunt/maps/Source/Apartment/Props/forghoon_bricks.tscn" id="2_aecip"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.383535
height = 1.71617

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_qg4oh"]
radius = 0.0984793
height = 1.04749

[sub_resource type="BoxShape3D" id="BoxShape3D_hw3aw"]
size = Vector3(1.41968, 0.677612, 1.73193)

[node name="ForghoonBricksProp" instance=ExtResource("1_ll0rh")]

[node name="ForghoonBricks" parent="Meshes" index="0" instance=ExtResource("2_aecip")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1.91069e-15, 4.37114e-08, 1, 1, -4.37114e-08, 0, 4.37114e-08, 1, -4.37114e-08, 0.00131172, 0.325572, -0.00911003)
shape = SubResource("CapsuleShape3D_5q4rx")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(1.91069e-15, 4.37114e-08, 1, 1, -4.37114e-08, 0, 4.37114e-08, 1, -4.37114e-08, -0.496606, 1.07722, -0.946279)
shape = SubResource("CapsuleShape3D_qg4oh")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="StaticBody3D" index="2"]
transform = Transform3D(1.91069e-15, 4.37114e-08, 1, 1, -4.37114e-08, 0, 4.37114e-08, 1, -4.37114e-08, 0.50564, 1.07722, -0.946279)
shape = SubResource("CapsuleShape3D_qg4oh")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="3"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00524902, 0.838806, 0.145752)
shape = SubResource("BoxShape3D_hw3aw")
