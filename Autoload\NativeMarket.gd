extends Node

enum MarketType {CafeBazaar, Myket, GooglePlay, Zarinpal}

var market = MarketType.Myket
signal print_signal(txt)
signal purchase_succeed(item)
signal purchase_start
signal purchase_finished(sku)
signal query_succeed(results)
signal query_failed
signal consume_success(sku, token)
signal consume_failed(sku, token)

var bazaar_plugin_name = "GodotBilling_bazaar"
var myket_plugin_name  = "GodotBilling_myket"
var google_plugin_name = "GodotGooglePlayBilling"

var bazaar_public_key = "MIHNMA0GCSqGSIb3DQEBAQUAA4G7ADCBtwKBrwCn3kfxFBB566V0wNNrvD/cu1rGgw2+fVLUfejg5UcZ5jKAjq0GZauAeeAxWXZeJmf4uVnzat3Dk1mjroIcRtswOzmyeDlzcA261AOJJX00oSFutOFuOhHws1DOjFrnd5A/mzJAG1Vrpkd+hGgZmw9tO90tcsSMd3nooN/UfeV8AnqXbDJCa75x9SXll0GGhDMWnas4UfQl7usa19i0fsTvEfKQ/wrBHDnqK0hPyAECAwEAAQ==" # کلید عمومی برنامه را از بازار و مایکت بگیرید
var myket_public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCqIuH5TDJzM/PUV4EuSPD4EjPxmkDM193ah/u5qujLfKHuJFx6MDHLCeZYIvfUmrpPilhqoLP/96WXoZnaSOSqmyPIHKxPxcrcykzemX76RIWb5bS6rE0LldrzWLx/IWJZ3FW6l0qR57IRsUoT9oz31eHnMoVlSxRrNuOn0iVdrQIDAQAB"

var billing = null
var is_purchasing = false
var is_init = false
var plugin_name
var app_public_key

#Bazaar: [{ "price": "ØµÙ�Ø± Ø±ÛŒØ§Ù„", "product_id": "test", "description": "", "type": "inapp", "title": "test" }]
#Myket:  [{ "price": "Û±Û°Û° ØªÙˆÙ…Ø§Ù†", "product_id": "test", "description": "", "type": "inapp", "title": "test" }]

func _ready() -> void:
	plugin_name = bazaar_plugin_name
	app_public_key = bazaar_public_key
	market = null
	if OS.has_feature("Google"):
		market = MarketType.GooglePlay

	if OS.has_feature("Myket"):
		print("market: Myket")
		market = MarketType.Myket
		app_public_key = myket_public_key
		plugin_name= myket_plugin_name
		
	if OS.has_feature("Bazaar"):
		print("market: Bazaar")
		market = MarketType.CafeBazaar
	if OS.has_feature("Zarinpal"):
		market = MarketType.Zarinpal
		print("market: Zarinpal")
	

func store_url():
	if market == MarketType.CafeBazaar:
		return "http://cafebazaar.ir/app/?id=ir.sizakgames.animalrushmmo&ref=share"
	if market == MarketType.Myket:
		return "https://myket.ir/app/ir.sizakgames.animalrushmmo"
	if market == MarketType.GooglePlay:
		return "https://play.google.com/store/apps/details?id=com.sizak.animalrp"
	if market == MarketType.Zarinpal:
		return "https://sizakgames.ir"
	return ""


func store_page():
	if market == MarketType.CafeBazaar:
		return "bazaar://details?id=" + "ir.sizakgames.animalrushmmo"
	if market == MarketType.Myket:
		return "myket://details?id=" + "ir.sizakgames.animalrushmmo"
	if market == MarketType.GooglePlay:
		return "https://play.google.com/store/apps/details?id=com.sizak.animalrp"
	if market == MarketType.Zarinpal:
		return "https://shop.sizakgames.ir"
	return ""


func rate_page():
	if market == MarketType.CafeBazaar:
		return "bazaar://details?id=" + "ir.sizakgames.animalrushmmo"
	if market == MarketType.Myket:
		return "myket://comment?id=" + "ir.sizakgames.animalrushmmo"
	if market == MarketType.GooglePlay:
		return "market://details?id=" + "ir.sizakgames.animalrushmmo"
	if market == MarketType.Zarinpal:
		return "https://shop.sizakgames.ir"
	return ""


func get_share_text():
	var _str = "Animal Rush رو نصب کن"
	return _str + "\n" + store_url()


func support_page():
	return "mailto:<EMAIL>"


func market_string():
	if Constants.is_desktop():
		return "Desktop"

	if market == MarketType.CafeBazaar:
		return "CafeBazaar"
	if market == MarketType.Myket:
		return "Myket"
	if market == MarketType.GooglePlay:
		return "GooglePlay"
	if market == MarketType.Zarinpal:
		return "Zarinpal"


func init():
	if is_init:
		return
	if market == MarketType.Zarinpal:
		return
	
	
	if market == MarketType.GooglePlay:
		init_google_play()
		return
	if Engine.has_singleton(plugin_name):
		billing = Engine.get_singleton(plugin_name)
		billing.setApplicationKey(app_public_key)
		billing.setDebugMode(true)
		billing.connection_succeed.connect(_on_Connected) # (message)
		billing.connection_failed.connect(_on_ConnectionFailed) # (message)
		billing.query_sku_details_succeed.connect(_on_QuerySucceed) # (dictionaryArray)
		billing.query_sku_details_failed.connect(_on_QueryFailed) # ()
		billing.purchase_succeed.connect(_on_PurchaseSucceed) # (sku)
		billing.purchase_failed.connect(_on_PurchaseFailed) # (errorCode, errorMessage)
		billing.consume_succeed.connect(_on_ConsumeSucceed) # (sku)
		billing.consume_failed.connect(_on_ConsumeFailed) # (sku)
		billing.startConnection()
		#emit_signal("print_signal", "has billing: " + plugin_name)
		is_init = true
	else:
		#emit_signal("print_signal", "no billing: " + plugin_name)
		if Constants.is_mobile():
			print("SHOP***billing plugin not found")


func init_google_play():
	if is_init:
		return
	is_init = true
	if Engine.has_singleton("GodotGooglePlayBilling"):
		billing = Engine.get_singleton("GodotGooglePlayBilling")

		# These are all signals supported by the API
		# You can drop some of these based on your needs
		billing.billing_resume.connect(_on_billing_resume) # No params
		billing.connected.connect(_on_connected) # No params
		billing.disconnected.connect(_on_disconnected) # No params
		billing.connect_error.connect(_on_connect_error) # Response ID (int), Debug message (string)
		billing.price_change_acknowledged.connect(_on_price_acknowledged) # Response ID (int)
		billing.purchases_updated.connect(_on_purchases_updated) # Purchases (Dictionary[])
		billing.purchase_error.connect(_on_purchase_error) # Response ID (int), Debug message (string)
		billing.sku_details_query_completed.connect(_on_product_details_query_completed) # Products (Dictionary[])
		billing.sku_details_query_error.connect(_on_product_details_query_error) # Response ID (int), Debug message (string), Queried SKUs (string[])
		billing.purchase_acknowledged.connect(_on_purchase_acknowledged) # Purchase token (string)
		billing.purchase_acknowledgement_error.connect(_on_purchase_acknowledgement_error) # Response ID (int), Debug message (string), Purchase token (string)
		billing.purchase_consumed.connect(_on_purchase_consumed) # Purchase token (string)
		billing.purchase_consumption_error.connect(_on_purchase_consumption_error) # Response ID (int), Debug message (string), Purchase token (string)
		billing.query_purchases_response.connect(_on_query_purchases_response) # Purchases (Dictionary[])

		billing.startConnection()
	else:
		print("Android IAP support is not enabled. Make sure you have enabled 'Gradle Build' and the GodotGooglePlayBilling plugin in your Android export settings! IAP will not work.")


func _reconnect():
	if billing == null:
		print("SHOP***InAppBilling plugin is not available for this device.")
	else:
		billing.startConnection()


func exit():
	if billing != null:
		billing.endConnection()


func _on_Connected(_msg: String):
	print("Android IAP: Successfully connected to billing service")
	billing.querySkuDetails()


func _on_ConnectionFailed(_msg: String):
	print("Android IAP: Can not connect to billing service")
	billing = null


func _on_QuerySucceed(results):
	if billing == null:
		return
		
	if is_purchasing:
		print("is purchasing: ", last_purchase_data)
		billing.consume(last_purchase_data["sku"])
	else:
		query_succeed.emit(results)



func _on_QueryFailed():
	if billing == null:
		return
	query_failed.emit()
	#await Constants.wait_timer(1)
	#billing.querySkuDetails()


func _on_PurchaseSucceed(sku: String):
	print("SHOP***Purchase Succeed SKU: ", sku)
	
	#await get_tree().create_timer(2).timeout
	billing.querySkuDetails()
	#if market == MarketType.CafeBazaar:
	#	billing.consume(sku)
	#else:
	#	billing.querySkuDetails()


func _on_PurchaseFailed(errorCode: int, errorMessage: String):
	print("SHOP***Purchase Failed error code: " + str(errorCode) + " error message: " + errorMessage)
	is_purchasing = false
	purchase_finished.emit("")


func _on_ConsumeSucceed(sku: String, token: String):
	print("SHOP***Consume Succeed: ", sku, " ", token)
	if not is_purchasing:
		consume_success.emit(sku, token)
		return
	last_purchase_data["token"] = token
	last_purchase_data["sku"] = sku
	purchase_succeed.emit(last_purchase_data)
	is_purchasing = false
	purchase_finished.emit(sku)


func _on_ConsumeFailed(sku: String, token: String):
	print("SHOP***Consume Failed: ", sku, " ", token)
	if not is_purchasing:
		consume_failed.emit(sku, token)
		return
	await Constants.wait_timer(1)
	billing.consume(sku)
	#is_purchasing = false
	#emit_signal("purchase_finished")


var last_purchase_data = null # Set in shopScene
func purchase_item(data):
	if billing == null:
		return
	
	is_purchasing = true
	emit_signal("purchase_start")
	last_purchase_data = data
	
	if market == MarketType.GooglePlay:
		billing.purchase(data["sku"])
		return
		

	purchase_sku(data["sku"])
	


func purchase_sku(sku):
	if Constants.is_desktop():
		_on_ConsumeSucceed(sku, "")
		return
	
	if billing == null:
		return

	if billing != null:
		emit_signal("print_signal", "purchasing: " + sku)
		billing.purchase(sku)
	else:
		emit_signal("print_signal", "billing is null")


func test_crash():
	if billing != null:
		billing.testCrash()



############################GOOGLE PLAY FUNCTIONS###############################
# Matches BillingClient.ConnectionState in the Play Billing Library
enum ConnectionState {
	DISCONNECTED, # not yet connected to billing service or was already closed
	CONNECTING, # currently in process of connecting to billing service
	CONNECTED, # currently connected to billing service
	CLOSED, # already closed and shouldn't be used again
}

# Matches Purchase.PurchaseState in the Play Billing Library
enum PurchaseState {
	UNSPECIFIED,
	PURCHASED,
	PENDING,
}

func _on_billing_resume():
	if billing.getConnectionState() == ConnectionState.CONNECTED:
		_query_purchases()


func _on_connected():
	billing.querySkuDetails(["item1", "item2", "basic", "coinbig1",
	 "coinbig2", "coinbig3", "deal1", "deal2", "police_license",
	 "yalda1", "yalda2", "jamjam", "progress"], "inapp")


func _on_disconnected():
	pass


 # Response ID (int), Debug message (string)


func _on_connect_error(response_id:int, error_message:String):
	print("on connect error ", response_id, " ", error_message)


func _on_price_acknowledged(_response_id:int):
	print("_on_price_acknowledged: ", _response_id)
	pass


func _on_purchases_updated(purchases):
	for purchase in purchases:
		_process_purchase(purchase)


func _on_purchase_error(response_id:int, error_message:String):
	print("purchase_error id:", response_id, " message: ", error_message)
	purchase_finished.emit("")


func _on_product_details_query_completed(products:Array):
	print("_on_product_details_query_completed: ", products)
	for available_product in products:
		print(available_product)


# Response ID (int), Debug message (string), Queried SKUs (string[])
func _on_product_details_query_error(response_id:int, message:String, skus:Array[String]):
	print("on_product_details_query_error id:", response_id, " message: ",
			message, " products: ", skus)


func _on_purchase_acknowledged(purchase_token:String):
	print_debug(purchase_token)


# Response ID (int), Debug message (string), Purchase token (string)
func _on_purchase_acknowledgement_error(response_id, debug_message, purchase_token):
	print_debug(response_id, " ", debug_message, " ", purchase_token)


func _on_purchase_consumed(token:String):
	print("Consume Succeed: ", token)
	last_purchase_data["token"] = token
	purchase_succeed.emit(last_purchase_data)
	is_purchasing = false
	purchase_finished.emit(last_purchase_data["sku"])


# Response ID (int), Debug message (string), Purchase token (string)
func _on_purchase_consumption_error(response_id:int, debug_message:String, purchase_token:String):
	print("Consume Failed: ", purchase_token, " ", debug_message, " ", response_id)
	is_purchasing = false
	purchase_finished.emit("")
	
	#await Constants.wait_timer(1)
	#billing.consume(sku)


func _on_query_purchases_response(query_result:Dictionary):
	if query_result.status == OK:
		for purchase in query_result.purchases:
			_process_purchase(purchase)
	else:
		print("queryPurchases failed, response code: ",
				query_result.response_code,
				" debug message: ", query_result.debug_message)


func _query_purchases():
	billing.queryPurchases("inapp")


func _process_purchase(purchase):
	if purchase.purchase_state == PurchaseState.PURCHASED:
		# Add code to store payment so we can reconcile the purchase token
		# in the completion callback against the original purchase
		billing.consumePurchase(purchase.purchase_token)
