extends "res://Scenes/FreeRide/Assets/Actionables/BaseActionable.gd"

@onready var panel = $Panel
@onready var container = $Panel/Tab1Scroll/HBoxContainer
@onready var http_request = $HTTPRequest
@onready var verify_http = $VerifyHttp
@onready var loading = $Panel/Loading
@onready var tab1_scroll = $Panel/Tab1Scroll
@onready var tab2_scroll = $Panel/Tab2Scroll
@onready var error = $Panel/Error
@onready var info_panel = $Panel/InfoPanel
@onready var info_container = $Panel/InfoPanel/InfoPanel/InfoScroll/HBoxContainer
@onready var info_scroll = $Panel/InfoPanel/InfoPanel/InfoScroll
@onready var coin_shop_card = preload("res://Scenes/ui/shop_card.tscn")
@onready var character_shop_card = preload("res://Scenes/ui/shop_card_character.tscn")
@onready var character_unlocker_popup = $Panel/CharacterUnlockerPopup
@onready var shop_item_prize_popup = $Panel/ShopItemPrizePopup
@onready var buy_from_menu_panel = $Panel/BuyFromMenuPanel
@onready var content_creators: Control = $Panel/ContentCreators

@onready var tab_1_button = $Panel/Tab1Button
@onready var tab_2_button = $Panel/Tab2Button
@onready var chest_popup = $Panel/ChestPopup
@onready var fox = $Fox
@onready var restore_button: Control = $Panel/RestoreButton
@onready var purchase_verifier_ui: Control = $Panel/PurchaseVerifierUi


var tabs = []
var selected_tab
var package_price = 0
var package_sku = ""
var purchase_start_time = 0
var purchase_success_list = []
var ami_active = false

func _ready():
	init_zarinpal_request()
	panel.process_mode = Node.PROCESS_MODE_DISABLED
	buy_from_menu_panel.visible = false
	fox.set_show()
	fox.set_dummy()
	panel.visible = false
	tabs = [tab1_scroll, tab2_scroll, content_creators]
	shop_item_prize_popup.visible = false
	info_panel.visible = false
	http_request.request_completed.connect(on_list_request_complete)
	verify_http.request_completed.connect(on_verify_request_complete)
	NativeMarket.purchase_succeed.connect(on_purchase_succeed)
	NativeMarket.purchase_start.connect(on_purchase_start)
	NativeMarket.purchase_finished.connect(on_purchase_finished)
	chest_popup.visible = false
	chest_popup.start_character_unlock.connect(start_character_unlock)
	var is_mob = NativeMarket.market == NativeMarket.MarketType.CafeBazaar or NativeMarket.market == NativeMarket.MarketType.Myket
	restore_button.visible = is_mob


func on_action(_player):
	ami_active = true
	panel.visible = true
	panel.process_mode = Node.PROCESS_MODE_INHERIT
	error.visible = false
	loading.visible = false
	send_request()
	if Constants.is_desktop() and Constants.is_client():
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)


func diff_time():
	return Time.get_unix_time_from_system() - purchase_start_time


func _process(_delta):
	if panel.visible:
		if Input.is_action_just_pressed("hide"):
			_on_exit_button_pressed()
	
	for purchase in purchase_success_list:
		handle_purchase_succeed(purchase)
	purchase_success_list = []


func _on_panel_gui_input(event):
	if event is InputEventScreenTouch:
		if event.pressed:
			if panel.visible:
				_on_exit_button_pressed()


#Shop Functions
func send_request():
	for child in container.get_children():
		child.queue_free()
	hide_all()
	loading.visible = true
	var url = Constants.BACKEND_URL + "/shop/list_shop/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	http_request.cancel_request()
	http_request.request(url, headers, HTTPClient.METHOD_POST, json)


var is_google_iap = false
func on_list_request_complete(_result, response_code, _headers, body):
	loading.visible = false
	is_google_iap = false
	if response_code == 200:
		#unlock_button.visible = true
		var json = JSON.parse_string(body.get_string_from_utf8())
		handle_items(json["items"])
		gateway = json["gateway_type"]
		_on_tab_2_button_pressed()
		
		if gateway == "IAP" and NativeMarket.market == NativeMarket.MarketType.GooglePlay:
			_on_tab_2_button_pressed()
			tab_2_button.visible = true
			is_google_iap = true
			#_on_tab_1_button_pressed()
			#tab_2_button.visible = false
		else:
			_on_tab_2_button_pressed()
			tab_2_button.visible = true
		#print(json["items"])
		return
	
	error.visible = true


func handle_items(items):
	for child in container.get_children():
		child.queue_free()

	for item in items:
		if DataSaver.has_purchased(item["id"]) and item["one_time"] == true:
			continue

		var card
		if len(item.get("unlock_characters", [])) > 0:
			card = character_shop_card.instantiate()
			card.show_info.connect(show_info)
		else:
			card = coin_shop_card.instantiate()
		container.add_child(card, is_google_iap)
		card.set_data(item)
		card.pressed.connect(purchase_item)


func hide_all():
	error.visible = false
	tab1_scroll.visible = false
	loading.visible = false


func _on_retry_button_pressed():
	send_request()


func _on_unlock_button_pressed():
	Firebase.logEvent("ShopCharacter", {})
	character_unlocker_popup.show_popup()


func purchase_item(item):
	if gateway == "IPG" or NativeMarket.market == NativeMarket.MarketType.Zarinpal:
		send_ipg_request(item)
		return
	#buy_from_menu_panel.visible = true
	Firebase.logEvent("PurchaseItemTry", {})
	package_price = int(item["price_str"].replace(",", "").replace("Tomans", "").replace(" ", ""))
	package_sku = item.get("sku")
	NativeMarket.purchase_item(item)


func handle_purchase_succeed(item):
	if diff_time() < 2:
		print("PURCHASE SUCCEED ignoring")
		return

	purchase_start_time = Time.get_unix_time_from_system()
	
	verify_purchase(item)


var last_item = null
func on_purchase_succeed(item):
	if ami_active:
		last_item = item
		purchase_success_list.append(item)


func verify_purchase(item):
	print("verifying purchase")
	hide_all()
	loading.visible = true
	var url = Constants.BACKEND_URL + "/shop/verify_purchase/"
	var data = {
		"item_id": item["id"],
		"market": NativeMarket.market_string(),
		"token": item["token"],
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	verify_http.cancel_request()
	verify_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_verify_request_complete(_result, response_code, _headers, body):
	if response_code == 200:
		loading.visible = false
		tab1_scroll.visible = true
		var json = JSON.parse_string(body.get_string_from_utf8())
		var added_coin = json["coin"] - Selector.my_coin
		Selector.my_coin = json["coin"]
		print("verify success")
		@warning_ignore("integer_division")
		#Tenjin.purchase_event(package_sku, package_price / 10, "IRR")
		Tenjin.purchase_event(package_sku, package_price / 1000000, "USD")
		@warning_ignore("integer_division")
		GameAnalyitics.addBussinessEvent(package_price / 10)
		GameAnalyitics.addResourceEvent("Source", added_coin, "IAP", "IAP")
		for character in json["characters"]:
			Selector.my_characters.append(character)

		shop_item_prize_popup.show_popup(added_coin, json["characters"])
		DataSaver.add_purchase_id(json["id"])
		return
	else:
		await Constants.wait_timer(1)
		verify_purchase(last_item)


func on_purchase_start():
	loading.visible = true


func on_purchase_finished(_sku):
	loading.visible = false


func show_info(characters):
	info_panel.visible = true
	for child in info_container.get_children():
		child.queue_free()
	
	for chara in characters:
		var image = TextureRect.new()
		image.expand_mode = TextureRect.EXPAND_IGNORE_SIZE
		image.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		image.texture = chara
		info_container.add_child(image)
		image.custom_minimum_size.x = 157
		image.custom_minimum_size.y = 249
	
	await get_tree().process_frame
	await get_tree().process_frame
	info_scroll.scroll_horizontal = 0


func _on_info_exit_button_pressed():
	info_panel.visible = false


func _on_chest_card_chest_open(tier):
	chest_popup.show_popup(tier)


func _on_chest_card_2_chest_open(tier):
	chest_popup.show_popup(tier)


func _on_chest_card_3_chest_open(tier):
	chest_popup.show_popup(tier)


func _on_chest_card_4_chest_open(tier):
	chest_popup.show_popup(tier)


func select_tab(index):
	for tab in tabs:
		tab.visible = false
	selected_tab = tabs[index]
	selected_tab.visible = true


func _on_tab_1_button_pressed():
	$Panel/Tab1Button/Selected.visible = true
	$Panel/Tab2Button/Selected.visible = false
	$Panel/Tab3Button/Selected.visible = false
	select_tab(1)


func _on_tab_2_button_pressed():
	$Panel/Tab1Button/Selected.visible = false
	$Panel/Tab2Button/Selected.visible = true
	$Panel/Tab3Button/Selected.visible = false
	select_tab(0)


func start_character_unlock(tier):
	character_unlocker_popup.show_popup(tier)


func _on_body_exited(player):
	if not player is Character:
		return
	super(player)
	if not is_instance_valid(Constants.client):
		return
	if Constants.is_client() and is_instance_valid(player):
		if "controls" in player:
			if player.controls == Constants.Controls.Player:
				_on_exit_button_pressed()


func _on_exit_button_pressed():
	if Constants.LOCAL_MODE:
		return
	ami_active = false
	if not is_instance_valid(Constants.client):
		return

	if Constants.client.game_scene.quiting:
		return

	panel.visible = false
	panel.process_mode = Node.PROCESS_MODE_DISABLED
	if Constants.is_desktop() and Constants.is_client():
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)


func exit_buy_from_menu_panel():
	buy_from_menu_panel.visible = false


func _on_rubika_button_pressed() -> void:
	OS.shell_open("https://rubika.ir/animalrush")


func _on_tab_3_button_pressed() -> void:
	$Panel/Tab1Button/Selected.visible = false
	$Panel/Tab2Button/Selected.visible = false
	$Panel/Tab3Button/Selected.visible = true
	select_tab(2)
	content_creators.start()


var zarinpal_http_request: HTTPRequest
var gateway = "IAP"
func init_zarinpal_request():
	zarinpal_http_request = HTTPRequest.new()
	add_child(zarinpal_http_request)
	zarinpal_http_request.request_completed.connect(on_zarinpal_request)

func send_ipg_request(item):
	loading.visible = true
	var url = Constants.BACKEND_URL + "/shop/ipg/"
	var data = {
		"item_id": item.id
		}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	zarinpal_http_request.cancel_request()
	zarinpal_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func on_zarinpal_request(_result, response_code, _headers, body):
	loading.visible = false
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		OS.shell_open(json["url"])
		return
	
	Constants.show_toast(str(response_code))


func _notification(what: int) -> void:
	if Constants.is_server:
		return
	if not ami_active:
		return

	if what == NOTIFICATION_APPLICATION_RESUMED:
		ClientBackendManager.send_sync_request()


func _on_restore_button_pressed() -> void:
	purchase_verifier_ui.start()
