[gd_scene load_steps=84 format=3 uid="uid://qygrpw6g8etp"]

[ext_resource type="Script" path="res://prophaunt/maps/Office/PropHauntMap.gd" id="1_vpaf3"]
[ext_resource type="Texture2D" uid="uid://bj4c4guqfb5o4" path="res://HDRI/FS002_Day.png" id="2_sq7gv"]
[ext_resource type="PackedScene" uid="uid://bjvuvi1pgvg60" path="res://prophaunt/maps/Source/ElectricalItem/computer_keyboard.tscn" id="4_hwm02"]
[ext_resource type="PackedScene" uid="uid://qhwdvnixc7yx" path="res://prophaunt/scenes/Props/ElectricalItem/computer_mouse_prop.tscn" id="5_2s3sl"]
[ext_resource type="PackedScene" uid="uid://bstac2w45gnj" path="res://prophaunt/scenes/Props/ElectricalItem/computer_screen_prop.tscn" id="6_05txj"]
[ext_resource type="PackedScene" uid="uid://oif8x8k0d7lf" path="res://prophaunt/scenes/Props/Chairs/chair_desk_prop.tscn" id="7_5wnvk"]
[ext_resource type="PackedScene" uid="uid://dj4ggj2t2a3qv" path="res://prophaunt/scenes/Props/ElectricalItem/laptop_prop.tscn" id="8_r3jwd"]
[ext_resource type="PackedScene" uid="uid://dnce4onj55k6w" path="res://prophaunt/scenes/Props/Bathroom/toilet_open_prop.tscn" id="11_5iwue"]
[ext_resource type="PackedScene" uid="uid://e3s6ndkkeix0" path="res://prophaunt/maps/Source/Wall/wall_cornice.tscn" id="12_3u4kp"]
[ext_resource type="PackedScene" uid="uid://4nagexvpgy6i" path="res://prophaunt/scenes/Props/Tables/desk_corner_prop.tscn" id="12_oxr8q"]
[ext_resource type="PackedScene" uid="uid://bbjhgvnaphthm" path="res://prophaunt/maps/Source/Wall/wall_corner_cornice.tscn" id="13_ba1l5"]
[ext_resource type="PackedScene" uid="uid://bpxsd5h1f31h4" path="res://prophaunt/scenes/Props/Tables/desk_prop.tscn" id="13_iipbe"]
[ext_resource type="PackedScene" uid="uid://dqaugbiclfr56" path="res://prophaunt/scenes/Props/Item/box_closed_prop.tscn" id="13_tygdv"]
[ext_resource type="PackedScene" uid="uid://778co83upaj1" path="res://prophaunt/scenes/Props/Furniture/bookcase_closed_prop.tscn" id="14_17xay"]
[ext_resource type="Material" path="res://prophaunt/Mat/LightBrown.tres" id="14_dwcpb"]
[ext_resource type="PackedScene" uid="uid://lu81xqf3o6wu" path="res://prophaunt/maps/Source/Wall/wall.tscn" id="14_j712o"]
[ext_resource type="PackedScene" uid="uid://dk4c2lsskuhtw" path="res://prophaunt/scenes/Props/Item/box_open_prop.tscn" id="14_y66er"]
[ext_resource type="PackedScene" uid="uid://bfmninyo2r632" path="res://prophaunt/maps/Source/Wall/wall_doorway_wide.tscn" id="15_bo021"]
[ext_resource type="PackedScene" uid="uid://c1vesst43k6ng" path="res://prophaunt/scenes/Props/Furniture/bookcase_closed_wide_prop.tscn" id="15_qaf2w"]
[ext_resource type="Material" path="res://prophaunt/Mat/LightDarkBrown.tres" id="16_5fxuv"]
[ext_resource type="PackedScene" uid="uid://h3vsguwa86cp" path="res://prophaunt/maps/Source/Office/rug_rounded.tscn" id="16_a27ce"]
[ext_resource type="PackedScene" uid="uid://c86t22rcdus6y" path="res://prophaunt/maps/Source/Item/bear.tscn" id="17_6khit"]
[ext_resource type="PackedScene" uid="uid://d3nmpbno7f53a" path="res://prophaunt/maps/Source/Wall/wall_corner.tscn" id="17_tot3q"]
[ext_resource type="PackedScene" uid="uid://cybsgwgxlff46" path="res://prophaunt/scenes/Props/Item/plant_small_1_prop.tscn" id="19_i6gbk"]
[ext_resource type="PackedScene" uid="uid://cf7j12u33go1h" path="res://prophaunt/maps/Source/Wall/floor_full.tscn" id="20_g2x28"]
[ext_resource type="PackedScene" uid="uid://bhmi10j2gfo0p" path="res://prophaunt/scenes/Props/Item/plant_small_2_prop.tscn" id="20_levdh"]
[ext_resource type="PackedScene" uid="uid://jkoxibubs50i" path="res://prophaunt/scenes/Props/Item/plant_small_3_prop.tscn" id="21_qjbph"]
[ext_resource type="Material" path="res://prophaunt/Mat/FloorWood.tres" id="21_sb1nk"]
[ext_resource type="PackedScene" uid="uid://dfe3vm5rbvmqb" path="res://prophaunt/scenes/Props/Item/potted_plant_prop.tscn" id="22_5uxf6"]
[ext_resource type="PackedScene" uid="uid://dpyvkpmj7v04v" path="res://prophaunt/maps/Source/Wall/wall_doorway.tscn" id="22_7vdht"]
[ext_resource type="Material" path="res://prophaunt/Mat/DarkLightBrown.tres" id="22_uocqr"]
[ext_resource type="Material" path="res://prophaunt/Mat/DarkBrown.tres" id="23_cqyyt"]
[ext_resource type="PackedScene" uid="uid://dksat3ng0il8v" path="res://prophaunt/scenes/Props/Chairs/chair_boss_prop.tscn" id="23_jcohf"]
[ext_resource type="PackedScene" uid="uid://do8juxatiy1gl" path="res://prophaunt/scenes/Props/ElectricalItem/lamp_round_table_prop.tscn" id="23_y32li"]
[ext_resource type="PackedScene" uid="uid://dpyqqu8as07hk" path="res://prophaunt/scenes/Props/Item/books_prop.tscn" id="25_aaags"]
[ext_resource type="PackedScene" uid="uid://gr2gg76rbykx" path="res://prophaunt/maps/Source/Wall/wall_doorway_sign.tscn" id="25_pq8m1"]
[ext_resource type="PackedScene" uid="uid://brb86o57alhkm" path="res://prophaunt/scenes/Props/Item/book_prop.tscn" id="26_blxbc"]
[ext_resource type="PackedScene" uid="uid://bk2s1hduf81qw" path="res://prophaunt/scenes/Props/Kitchen/stove_multicounter_prop.tscn" id="27_x2ftt"]
[ext_resource type="Texture2D" uid="uid://b1e3bebxqq7yy" path="res://prophaunt/maps/Office/WCSign.png" id="28_d7qek"]
[ext_resource type="PackedScene" uid="uid://qgayq3vcobtx" path="res://prophaunt/scenes/Props/Kitchen/tomatoe_basket_prop.tscn" id="28_phrs6"]
[ext_resource type="Material" path="res://prophaunt/Mat/FloorBlackGray.tres" id="29_8k3ct"]
[ext_resource type="PackedScene" uid="uid://bp8q3naiag7vd" path="res://prophaunt/scenes/Props/Kitchen/bun_basket_prop.tscn" id="29_14ru0"]
[ext_resource type="PackedScene" path="res://prophaunt/scenes/Props/Bathroom/toilet_paper_prop.tscn" id="29_aowbf"]
[ext_resource type="PackedScene" uid="uid://cn1c31s7u8y15" path="res://prophaunt/scenes/Props/Kitchen/carrot_basket_prop.tscn" id="30_opbe2"]
[ext_resource type="PackedScene" uid="uid://dvgp1wmt4d53y" path="res://prophaunt/scenes/Props/Kitchen/cheese_basket_prop.tscn" id="31_k6m0g"]
[ext_resource type="PackedScene" uid="uid://bk36numwn518f" path="res://prophaunt/scenes/Props/Kitchen/lettuce_basket_prop.tscn" id="32_bwkaj"]
[ext_resource type="PackedScene" uid="uid://cgbyscokd2lua" path="res://prophaunt/scenes/Props/Kitchen/onions_basket_prop.tscn" id="33_51aou"]
[ext_resource type="PackedScene" uid="uid://by01s6pquawf6" path="res://prophaunt/scenes/Props/Furniture/boxcase_prop.tscn" id="33_koq8u"]
[ext_resource type="PackedScene" uid="uid://6awrh6yk77f1" path="res://prophaunt/scenes/Props/Kitchen/potatoes_basket_prop.tscn" id="34_8ji03"]
[ext_resource type="PackedScene" uid="uid://6t50sexrfrl4" path="res://prophaunt/scenes/Props/Kitchen/Sink_prop.tscn" id="36_626kn"]
[ext_resource type="PackedScene" uid="uid://cxojsp6gyo8rm" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/Kitchencounter.tscn" id="36_iibwf"]
[ext_resource type="PackedScene" uid="uid://diw7x314mo1ob" path="res://prophaunt/scenes/Props/Furniture/boxcase_wide_prop.tscn" id="36_ogog8"]
[ext_resource type="PackedScene" uid="uid://bnubck2v2s7ab" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/KitchenCabinet.tscn" id="37_7igkm"]
[ext_resource type="PackedScene" uid="uid://dgs2gkxbaqg7u" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/KitchencabinetCorner.tscn" id="38_1lp2e"]
[ext_resource type="Material" path="res://prophaunt/Mat/Hologram.tres" id="38_76emc"]
[ext_resource type="PackedScene" uid="uid://b6ast582o83jc" path="res://prophaunt/scenes/Props/Tables/table_round_prop.tscn" id="38_y1q72"]
[ext_resource type="PackedScene" uid="uid://bf65dphgrng6f" path="res://prophaunt/scenes/Props/Chairs/chair_modern_cushion_prop.tscn" id="39_5jv04"]
[ext_resource type="PackedScene" uid="uid://mxley74wyhax" path="res://prophaunt/maps/Source/Wall/mosaic.tscn" id="39_c2eli"]
[ext_resource type="PackedScene" uid="uid://htmsu6hast14" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/KitchencabinetHalf.tscn" id="39_eu1pn"]
[ext_resource type="PackedScene" uid="uid://fs8oixfsuwu0" path="res://prophaunt/scenes/Props/Tables/table_glass_boss_prop.tscn" id="40_m5vs2"]
[ext_resource type="PackedScene" uid="uid://bebbenbf5vuiw" path="res://prophaunt/scenes/Props/Kitchen/macrowave_prop.tscn" id="40_t4jyg"]
[ext_resource type="PackedScene" uid="uid://d1ah6jboy0bae" path="res://Scenes/FreeRide/Assets/Buildings/Restaurant/Environment/Hood.tscn" id="40_xvs3c"]
[ext_resource type="PackedScene" uid="uid://dxtnwmdp7gkd8" path="res://prophaunt/scenes/Props/Chairs/lounge_design_sofa_prop.tscn" id="41_b2kem"]
[ext_resource type="PackedScene" uid="uid://dcjmtk7n6nv6v" path="res://prophaunt/scenes/Props/Kitchen/cuttingboard_prop.tscn" id="42_wtkkc"]
[ext_resource type="PackedScene" uid="uid://dq0y18vr4klri" path="res://prophaunt/scenes/Props/Chairs/lounge_design_chair_prop.tscn" id="42_yg6dn"]
[ext_resource type="PackedScene" uid="uid://d7ihbyu7nmgd" path="res://prophaunt/scenes/Props/Kitchen/fridge_prop.tscn" id="47_mbd4h"]
[ext_resource type="PackedScene" uid="uid://770i1g0h44xe" path="res://prophaunt/scenes/Props/ElectricalItem/radio_prop.tscn" id="48_tvxv1"]
[ext_resource type="PackedScene" uid="uid://74cv3v1ck04n" path="res://prophaunt/scenes/Props/Furniture/bed_bunk_prop.tscn" id="49_vb48k"]
[ext_resource type="PackedScene" uid="uid://ldeb5ncnj6c4" path="res://prophaunt/scenes/Props/ElectricalItem/speaker_prop.tscn" id="52_vpwwf"]
[ext_resource type="PackedScene" uid="uid://blsl7vrgxp0ca" path="res://prophaunt/scenes/Props/Tables/table_coffee_prop.tscn" id="53_ub11u"]
[ext_resource type="PackedScene" uid="uid://ciheqrgmbfyxu" path="res://prophaunt/scenes/Props/ElectricalItem/television_modern_prop.tscn" id="54_rjbba"]
[ext_resource type="PackedScene" uid="uid://d22pnldrxcwcm" path="res://prophaunt/scenes/Props/ElectricalItem/lamp_square_floor_prop.tscn" id="55_52jm4"]
[ext_resource type="PackedScene" uid="uid://c1dbf426l5k4r" path="res://prophaunt/maps/Source/Wall/floor_full_quarter.tscn" id="59_j7vue"]
[ext_resource type="PackedScene" uid="uid://1jbdp7qvpdgj" path="res://prophaunt/maps/Source/Wall/wall_window.tscn" id="65_u47kt"]
[ext_resource type="PackedScene" uid="uid://bmm7khjgjwuv8" path="res://prophaunt/scenes/Props/Chairs/lounge_chair_prop.tscn" id="68_jjhsn"]
[ext_resource type="PackedScene" uid="uid://copnl1facw0an" path="res://prophaunt/scenes/Props/Chairs/lounge_sofa_prop.tscn" id="69_nted7"]
[ext_resource type="PackedScene" uid="uid://c7et302mptk4f" path="res://prophaunt/maps/Source/Wall/floor_corner.tscn" id="69_y07f4"]
[ext_resource type="PackedScene" uid="uid://kbu3bp05bof0" path="res://prophaunt/scenes/Props/Tables/table_coffee_glass_square_prop.tscn" id="71_epmsg"]
[ext_resource type="PackedScene" uid="uid://8utkviyakrhi" path="res://prophaunt/scenes/Props/ElectricalItem/lamp_square_ceiling_prop.tscn" id="74_48jwh"]

[sub_resource type="PanoramaSkyMaterial" id="PanoramaSkyMaterial_cyubb"]
panorama = ExtResource("2_sq7gv")

[sub_resource type="Sky" id="Sky_r37ka"]
sky_material = SubResource("PanoramaSkyMaterial_cyubb")

[sub_resource type="Environment" id="Environment_elq66"]
background_mode = 2
background_energy_multiplier = 1.2
sky = SubResource("Sky_r37ka")
ambient_light_source = 1
ambient_light_color = Color(0.753907, 0.448284, 0.283835, 1)
ambient_light_energy = 11.65
glow_intensity = 2.55

[sub_resource type="QuadMesh" id="QuadMesh_3mcft"]
material = ExtResource("38_76emc")
size = Vector2(3, 3)

[node name="OfficeMap" type="Node3D"]
script = ExtResource("1_vpaf3")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_elq66")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.768734, -0.540993, 0.341137, 0.0451442, 0.486158, 0.872704, -0.637973, 0.686278, -0.349304, 1.97245, 14.5241, -0.396627)
shadow_enabled = true

[node name="Office1" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26.18, 0, 10.5)

[node name="PropNode1" type="Node3D" parent="Office1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 4.91312)

[node name="BookcaseNode8" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11.0448, 0, 1.97518)

[node name="BookcaseClosedWideProp" parent="Office1/PropNode1/BookcaseNode8" instance=ExtResource("15_qaf2w")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp6" parent="Office1/PropNode1/BookcaseNode8/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -8.74224e-08, 0, 1, 0, 8.74224e-08, 0, -1, -0.782497, 0.260172, 0.00842667)

[node name="BookProp2" parent="Office1/PropNode1/BookcaseNode8/BookcaseClosedWideProp" instance=ExtResource("26_blxbc")]
transform = Transform3D(-3.05221e-08, -0.698265, 0.715839, 1, -4.37114e-08, -5.32907e-15, 3.12903e-08, 0.715839, 0.698265, 0.505262, 2.42122, -0.0181313)

[node name="BookcaseNode9" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 11.0448, 0, -1.71291)

[node name="BookcaseClosedWideProp" parent="Office1/PropNode1/BookcaseNode9" instance=ExtResource("15_qaf2w")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp6" parent="Office1/PropNode1/BookcaseNode9/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -0.782497, 0.958404, 0.00842667)

[node name="BooksProp7" parent="Office1/PropNode1/BookcaseNode9/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, 0.726026, 1.67189, 0.00842667)

[node name="DeskPcNode" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.7, 0, -6.7)

[node name="DeskProp" parent="Office1/PropNode1/DeskPcNode" instance=ExtResource("13_iipbe")]
enabled = false

[node name="ChairDeskProp" parent="Office1/PropNode1/DeskPcNode" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office1/PropNode1/DeskPcNode" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office1/PropNode1/DeskPcNode" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office1/PropNode1/DeskPcNode" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskLaptopNode" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, -6.7)

[node name="DeskProp" parent="Office1/PropNode1/DeskLaptopNode" instance=ExtResource("13_iipbe")]
enabled = false

[node name="ComputerMouseProp" parent="Office1/PropNode1/DeskLaptopNode" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, 0.4, 1.15256, 0.3)
enabled = false

[node name="laptopProp" parent="Office1/PropNode1/DeskLaptopNode" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 1.15256, 0)

[node name="ChairDeskProp" parent="Office1/PropNode1/DeskLaptopNode" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.299999, 0, 1)

[node name="DeskPcNode2" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -5, 0, 3)

[node name="DeskProp" parent="Office1/PropNode1/DeskPcNode2" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -1.42109e-14, 0, 1, 0, 1.42109e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office1/PropNode1/DeskPcNode2" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office1/PropNode1/DeskPcNode2" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office1/PropNode1/DeskPcNode2" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office1/PropNode1/DeskPcNode2" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskLaptopNode2" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.3, 0, 3)

[node name="DeskProp" parent="Office1/PropNode1/DeskLaptopNode2" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -1.42109e-14, 0, 1, 0, 1.42109e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ComputerMouseProp" parent="Office1/PropNode1/DeskLaptopNode2" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, 0.4, 1.15578, 0.299999)
enabled = false

[node name="laptopProp" parent="Office1/PropNode1/DeskLaptopNode2" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 1.15578, 0)

[node name="ChairDeskProp" parent="Office1/PropNode1/DeskLaptopNode2" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.299999, 0, 1)

[node name="DeskPcNode3" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 2.5, 0, 3)

[node name="DeskProp" parent="Office1/PropNode1/DeskPcNode3" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -1.42109e-14, 0, 1, 0, 1.42109e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office1/PropNode1/DeskPcNode3" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office1/PropNode1/DeskPcNode3" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office1/PropNode1/DeskPcNode3" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office1/PropNode1/DeskPcNode3" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskLaptopNode3" type="Node3D" parent="Office1/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 6.7, 0, 3)

[node name="DeskProp" parent="Office1/PropNode1/DeskLaptopNode3" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -1.42109e-14, 0, 1, 0, 1.42109e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ComputerMouseProp" parent="Office1/PropNode1/DeskLaptopNode3" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, 0.400002, 1.15646, 0.299999)
enabled = false

[node name="laptopProp" parent="Office1/PropNode1/DeskLaptopNode3" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 1.15646, 0)

[node name="ChairDeskProp" parent="Office1/PropNode1/DeskLaptopNode3" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.299999, 0, 1)

[node name="PropNode2" type="Node3D" parent="Office1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -8.21128)

[node name="DeskCornerNode" type="Node3D" parent="Office1/PropNode2"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 9.9, 0, 7)

[node name="DeskCornerProp" parent="Office1/PropNode2/DeskCornerNode" instance=ExtResource("12_oxr8q")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office1/PropNode2/DeskCornerNode" instance=ExtResource("7_5wnvk")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0, 0, 0.5)

[node name="ComputerMouseProp" parent="Office1/PropNode2/DeskCornerNode" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.5, 0, 0.866025, 0, 1, 0, -0.866025, 0, -0.5, 0.6, 1.15405, 0.799999)
enabled = false

[node name="ComputerScreenProp" parent="Office1/PropNode2/DeskCornerNode" instance=ExtResource("6_05txj")]
transform = Transform3D(0.707107, 0, -0.707107, 0, 1, 0, 0.707107, 0, 0.707107, 0.799999, 1.2, -0.5)

[node name="laptopProp" parent="Office1/PropNode2/DeskCornerNode" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0.9, 1.15405, 0.299999)

[node name="DeskPcNode4" type="Node3D" parent="Office1/PropNode2"]
transform = Transform3D(1, 0, 2.13163e-14, 0, 1, 0, -2.13163e-14, 0, 1, 6.7, 0, -1.6756)

[node name="DeskProp" parent="Office1/PropNode2/DeskPcNode4" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -2.4869e-14, 0, 1, 0, 2.4869e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office1/PropNode2/DeskPcNode4" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office1/PropNode2/DeskPcNode4" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office1/PropNode2/DeskPcNode4" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office1/PropNode2/DeskPcNode4" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskPcNode5" type="Node3D" parent="Office1/PropNode2"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 10.8, 0, 1.2244)

[node name="DeskProp" parent="Office1/PropNode2/DeskPcNode5" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office1/PropNode2/DeskPcNode5" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office1/PropNode2/DeskPcNode5" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office1/PropNode2/DeskPcNode5" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office1/PropNode2/DeskPcNode5" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskLaptopNode4" type="Node3D" parent="Office1/PropNode2"]
transform = Transform3D(1, 0, 2.13163e-14, 0, 1, 0, -2.13163e-14, 0, 1, 1.6, 0, -1.6756)

[node name="DeskProp" parent="Office1/PropNode2/DeskLaptopNode4" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -2.13163e-14, 0, 1, 0, 2.13163e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ComputerMouseProp" parent="Office1/PropNode2/DeskLaptopNode4" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, 0.4, 1.15462, 0.3)
enabled = false

[node name="laptopProp" parent="Office1/PropNode2/DeskLaptopNode4" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 1.15462, 0)

[node name="ChairDeskProp" parent="Office1/PropNode2/DeskLaptopNode4" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.299999, 0, 1)

[node name="BookcaseNode6" type="Node3D" parent="Office1/PropNode2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.59566, 0, -1.89759)

[node name="BookcaseClosedProp" parent="Office1/PropNode2/BookcaseNode6" instance=ExtResource("14_17xay")]

[node name="BooksProp4" parent="Office1/PropNode2/BookcaseNode6/BookcaseClosedProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, 0.0892048, 1.85771, 0.00544512)

[node name="BooksProp5" parent="Office1/PropNode2/BookcaseNode6/BookcaseClosedProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -0.169998, 0.428707, 0.00544512)

[node name="BookcaseNode7" type="Node3D" parent="Office1/PropNode2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2.04869, 0, -1.90537)

[node name="BookcaseClosedWideProp" parent="Office1/PropNode2/BookcaseNode7" instance=ExtResource("15_qaf2w")]
enabled = false

[node name="BooksProp6" parent="Office1/PropNode2/BookcaseNode7/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -0.782497, 1.62635, 0.00842535)

[node name="BookProp2" parent="Office1/PropNode2/BookcaseNode7/BookcaseClosedWideProp" instance=ExtResource("26_blxbc")]
transform = Transform3D(-3.05221e-08, -0.698265, 0.715839, 1, -4.37114e-08, -5.32907e-15, 3.12903e-08, 0.715839, 0.698265, 0.505262, 0.976862, -0.0181298)

[node name="Trash" type="Node3D" parent="Office1"]

[node name="RugRounded6" parent="Office1/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -0.236, 0, 3.361)

[node name="RugRounded7" parent="Office1/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 7.387, 0, -4.561)

[node name="RugRounded8" parent="Office1/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -1.892, 0, -6.51)

[node name="WallOffice" type="Node3D" parent="Office1"]

[node name="WallCornice" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 2, 0, 11)
material_override = ExtResource("22_uocqr")

[node name="WallCornice2" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 6, 0, 11)
material_override = ExtResource("22_uocqr")

[node name="WallCornice3" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, -2, 0, 11)
material_override = ExtResource("22_uocqr")

[node name="WallCornice4" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 14, 0, -5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice5" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 14, 0, 3)
material_override = ExtResource("22_uocqr")

[node name="WallCornice6" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 14, 0, -1)
material_override = ExtResource("22_uocqr")

[node name="WallCornice7" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -2, 0, -13)
material_override = ExtResource("22_uocqr")

[node name="WallCornice8" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 2, 0, -13)
material_override = ExtResource("22_uocqr")

[node name="WallCornice9" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 6, 0, -13)
material_override = ExtResource("22_uocqr")

[node name="WallCornice10" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -10.181, 0, -5)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice" parent="Office1/WallOffice" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -1.50996e-07, 0, 1, 0, 1.50996e-07, 0, -1, 10, 0, 7)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice3" parent="Office1/WallOffice" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 10, 0, -9)
material_override = ExtResource("16_5fxuv")

[node name="Wall2" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -2, 4, -13)
material_override = ExtResource("22_uocqr")

[node name="WallCornice12" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -6, 0, -13)
material_override = ExtResource("22_uocqr")

[node name="Wall20" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -6, 4, -13)
material_override = ExtResource("22_uocqr")

[node name="Wall3" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 2, 4, -13)
material_override = ExtResource("22_uocqr")

[node name="Wall4" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 6, 4, -13)
material_override = ExtResource("22_uocqr")

[node name="Wall5" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, -2, 4, 11)
material_override = ExtResource("22_uocqr")

[node name="WallCornice11" parent="Office1/WallOffice" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, -6, 0, 11)
material_override = ExtResource("22_uocqr")

[node name="Wall19" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, -6, 4, 11)
material_override = ExtResource("22_uocqr")

[node name="Wall6" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 2, 4, 11)
material_override = ExtResource("22_uocqr")

[node name="Wall7" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 6, 4, 11)
material_override = ExtResource("22_uocqr")

[node name="Wall8" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 14, 4, 3)
material_override = ExtResource("22_uocqr")

[node name="Wall9" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 14, 4, -5)
material_override = ExtResource("22_uocqr")

[node name="Wall10" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 14, 4, -1)
material_override = ExtResource("22_uocqr")

[node name="Wall11" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 10, 4, 3)
material_override = ExtResource("14_dwcpb")

[node name="Wall12" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10.181, 4, 3)
material_override = ExtResource("22_uocqr")

[node name="Wall13" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -10.181, 4, -5)
material_override = ExtResource("22_uocqr")

[node name="Wall14" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 6.3573e-08, 0, 1, 0, -6.3573e-08, 0, 1, -5.99716, 4, -0.9969)
material_override = ExtResource("22_uocqr")

[node name="Wall15" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 6, 4, 3)
material_override = ExtResource("14_dwcpb")

[node name="Wall16" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -2, 4, -1)
material_override = ExtResource("14_dwcpb")

[node name="Wall17" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 10, 0, 3)
material_override = ExtResource("14_dwcpb")

[node name="Wall18" parent="Office1/WallOffice" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -2, 0, -1)
material_override = ExtResource("14_dwcpb")

[node name="WallDoorwayWide" parent="Office1/WallOffice" instance=ExtResource("15_bo021")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10.181, 0, 3.0031)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide2" parent="Office1/WallOffice" instance=ExtResource("15_bo021")]
transform = Transform3D(1, 0, 6.3573e-08, 0, 1, 0, -6.3573e-08, 0, 1, -5.99716, 0, -1)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide3" parent="Office1/WallOffice" instance=ExtResource("15_bo021")]
transform = Transform3D(1, 0, 6.3573e-08, 0, 1, 0, -6.3573e-08, 0, 1, 6, 0, 3)
material_override = ExtResource("14_dwcpb")

[node name="WallCorner" parent="Office1/WallOffice" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, -8.9407e-08, 0, 1, 0, 8.9407e-08, 0, -1, -6, 0, -1)
surface_material_override/0 = ExtResource("22_uocqr")

[node name="WallCorner3" parent="Office1/WallOffice" instance=ExtResource("17_tot3q")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 9.9816, 4, -8.9969)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner4" parent="Office1/WallOffice" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 1.50996e-07, 0, 1, 0, -1.50996e-07, 0, -1, 9.9816, 4, 7.0031)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner6" parent="Office1/WallOffice" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, -8.9407e-08, 0, 1, 0, 8.9407e-08, 0, -1, -6, 4, -1)
surface_material_override/0 = ExtResource("22_uocqr")

[node name="WallCorner7" parent="Office1/WallOffice" instance=ExtResource("17_tot3q")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 1.9816, 4, -0.9969)
material_override = ExtResource("14_dwcpb")

[node name="WallCorner8" parent="Office1/WallOffice" instance=ExtResource("17_tot3q")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 1.9816, 0, -0.9969)
material_override = ExtResource("14_dwcpb")

[node name="WallWindow" parent="Office1/WallOffice" instance=ExtResource("65_u47kt")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -6.2, 0, -9)
surface_material_override/0 = ExtResource("16_5fxuv")

[node name="Floor" type="Node3D" parent="Office1"]

[node name="FloorFull34" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 9.81493, 0, -0.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull39" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 3.01992e-07, 0, 2, 0, -3.01992e-07, 0, -2, 9.81493, 0, -4.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull45" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 9.81493, 0, -8.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull55" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-3.25841e-07, 0, 2, 0, 2, 0, -2, 0, -3.25841e-07, -2.18507, 0, -4.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull56" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2.18507, 0, -0.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull58" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 3.01992e-07, 0, 2, 0, -3.01992e-07, 0, -2, -6.18507, 0, -4.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull60" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2.18507, 0, -8.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull61" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -6.18507, 0, -8.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull64" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5.81493, 0, -8.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull65" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 1.81493, 0, -8.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull67" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-3.25841e-07, 0, 2, 0, 2, 0, -2, 0, -3.25841e-07, 5.81493, 0, -4.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull68" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5.81493, 0, -0.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull69" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 1.81493, 0, -0.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull70" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 3.01992e-07, 0, 2, 0, -3.01992e-07, 0, -2, 1.81493, 0, -4.9969)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull75" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-3.25841e-07, 0, 2, 0, 2, 0, -2, 0, -3.25841e-07, -2.18507, 0, 3.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull76" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2.18507, 0, 7.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull77" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -6.18507, 0, 7.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull78" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 3.01992e-07, 0, 2, 0, -3.01992e-07, 0, -2, -6.18507, 0, 3.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull79" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-3.25841e-07, 0, 2, 0, 2, 0, -2, 0, -3.25841e-07, 5.81493, 0, 3.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull80" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5.81493, 0, 7.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull81" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 1.81493, 0, 7.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull82" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 3.01992e-07, 0, 2, 0, -3.01992e-07, 0, -2, 1.81493, 0, 3.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull93" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 9.81493, 0, 7.0031)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull94" parent="Office1/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 3.01992e-07, 0, 2, 0, -3.01992e-07, 0, -2, 9.81493, 0, 3.0031)
material_override = ExtResource("21_sb1nk")

[node name="Light" type="Node3D" parent="Office1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.381599, 7.8, -6.5969)

[node name="LampSquareCeilingProp" parent="Office1/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Office1/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 15.203
light_indirect_energy = 11.781
light_volumetric_fog_energy = 1.68
light_size = 0.642
light_bake_mode = 1
spot_range = 35.259
spot_attenuation = 0.4
spot_angle = 89.99

[node name="Light2" type="Node3D" parent="Office1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0816002, 7.8, 5.0031)

[node name="LampSquareCeilingProp" parent="Office1/Light2" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Office1/Light2/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 17.758
light_indirect_energy = 12.291
light_volumetric_fog_energy = 10.578
light_size = 0.77
light_bake_mode = 1
spot_range = 18.722
spot_attenuation = 0.4
spot_angle = 89.99

[node name="WallDoorwaySign" parent="Office1" instance=ExtResource("25_pq8m1")]
transform = Transform3D(3.56901e-08, 0, -0.272165, 0, 0.276767, 0, 0.272165, 0, 3.56901e-08, -8.23387, 3.59304, 3.00242)
material_override = ExtResource("14_dwcpb")

[node name="Label3D" type="Label3D" parent="Office1/WallDoorwaySign"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.188495, 0.0297623)
text = "دفتر"
font_size = 208
outline_size = 32

[node name="WallDoorwaySign2" parent="Office1" instance=ExtResource("25_pq8m1")]
transform = Transform3D(0.272165, 0, 4.75868e-08, 0, 0.276767, 0, -4.75868e-08, 0, 0.272165, -6.01539, 3.59304, -2.75462)
material_override = ExtResource("14_dwcpb")

[node name="Label3D2" type="Label3D" parent="Office1/WallDoorwaySign2"]
transform = Transform3D(1, 0, 4.26326e-14, 0, 0.999999, 0, -4.26326e-14, 0, 1, 0, 0.188784, 0.0174103)
text = "دفتر"
font_size = 208
outline_size = 32

[node name="Office2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -18.51)

[node name="PropsNode1" type="Node3D" parent="Office2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.10214, -4.76837e-07, -0.0547676)

[node name="DeskCornerNode2" type="Node3D" parent="Office2/PropsNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.62956, 4.76837e-07, -7.77821)

[node name="ChairDeskProp" parent="Office2/PropsNode1/DeskCornerNode2" instance=ExtResource("7_5wnvk")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0, 0, 0.5)

[node name="ComputerMouseProp" parent="Office2/PropsNode1/DeskCornerNode2" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.5, 0, 0.866025, 0, 1, 0, -0.866025, 0, -0.5, 0.6, 1.1571, 0.799999)
enabled = false

[node name="ComputerScreenProp" parent="Office2/PropsNode1/DeskCornerNode2" instance=ExtResource("6_05txj")]
transform = Transform3D(0.707107, 0, -0.707107, 0, 1, 0, 0.707107, 0, 0.707107, 0.799999, 1.2, -0.5)

[node name="laptopProp" parent="Office2/PropsNode1/DeskCornerNode2" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0.9, 1.1571, 0.299999)

[node name="DeskCornerProp" parent="Office2/PropsNode1/DeskCornerNode2" instance=ExtResource("12_oxr8q")]
enabled = false

[node name="DeskPcNode7" type="Node3D" parent="Office2/PropsNode1"]
transform = Transform3D(1, 0, 2.13163e-14, 0, 1, 0, -2.13163e-14, 0, 1, -1.14604, 4.76837e-07, -8.67821)

[node name="DeskProp" parent="Office2/PropsNode1/DeskPcNode7" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -2.13163e-14, 0, 1, 0, 2.13163e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office2/PropsNode1/DeskPcNode7" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office2/PropsNode1/DeskPcNode7" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office2/PropsNode1/DeskPcNode7" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office2/PropsNode1/DeskPcNode7" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskPcNode6" type="Node3D" parent="Office2/PropsNode1"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.0460386, 4.76837e-07, 0.52179)

[node name="ChairDeskProp" parent="Office2/PropsNode1/DeskPcNode6" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office2/PropsNode1/DeskPcNode6" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office2/PropsNode1/DeskPcNode6" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office2/PropsNode1/DeskPcNode6" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="DeskProp" parent="Office2/PropsNode1/DeskPcNode6" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="DeskLaptopNode5" type="Node3D" parent="Office2/PropsNode1"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.0460405, 4.76837e-07, 4.52179)

[node name="ComputerMouseProp" parent="Office2/PropsNode1/DeskLaptopNode5" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, 0.4, 1.15492, 0.3)
enabled = false

[node name="laptopProp" parent="Office2/PropsNode1/DeskLaptopNode5" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 1.15492, 0)

[node name="ChairDeskProp" parent="Office2/PropsNode1/DeskLaptopNode5" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.299999, 0, 1)

[node name="DeskProp2" parent="Office2/PropsNode1/DeskLaptopNode5" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, 0, 0, 0)
enabled = false

[node name="BookcaseNode6" type="Node3D" parent="Office2/PropsNode1"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.16618, 4.76837e-07, 7.77993)

[node name="BookcaseClosedWideProp" parent="Office2/PropsNode1/BookcaseNode6" instance=ExtResource("15_qaf2w")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp6" parent="Office2/PropsNode1/BookcaseNode6/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -0.782497, 0.958404, 0.00842547)

[node name="BookProp2" parent="Office2/PropsNode1/BookcaseNode6/BookcaseClosedWideProp" instance=ExtResource("26_blxbc")]
transform = Transform3D(-3.05221e-08, -0.698265, 0.715839, 1, -4.37114e-08, -5.32907e-15, 3.12903e-08, 0.715839, 0.698265, 0.505262, 0.252963, -0.0181298)

[node name="PropsNode2" type="Node3D" parent="Office2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6.68957, 0, -3.77703)

[node name="BookcaseNode4" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7.844, 0, 3.03145)

[node name="BookcaseClosedProp" parent="Office2/PropsNode2/BookcaseNode4" instance=ExtResource("14_17xay")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp4" parent="Office2/PropsNode2/BookcaseNode4/BookcaseClosedProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, 0.0892048, 1.85771, 0.00544512)

[node name="BooksProp5" parent="Office2/PropsNode2/BookcaseNode4/BookcaseClosedProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -0.169998, 1.14815, 0.00544512)

[node name="BookcaseNode5" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7.84698, 0, 5.98468)

[node name="BookcaseClosedWideProp" parent="Office2/PropsNode2/BookcaseNode5" instance=ExtResource("15_qaf2w")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp6" parent="Office2/PropsNode2/BookcaseNode5/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -2.62268e-07, 0, 1, 0, 2.62268e-07, 0, -1, -0.782497, 0.958404, 0.00842535)

[node name="BookProp2" parent="Office2/PropsNode2/BookcaseNode5/BookcaseClosedWideProp" instance=ExtResource("26_blxbc")]
transform = Transform3D(-3.05221e-08, -0.698265, 0.715839, 1, -4.37114e-08, -5.32907e-15, 3.12903e-08, 0.715839, 0.698265, 0.505262, 0.252963, -0.0181298)

[node name="BookcaseNode2" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, -2.30658, 0, -7.52391)

[node name="BookcaseClosedWideProp" parent="Office2/PropsNode2/BookcaseNode2" instance=ExtResource("15_qaf2w")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp3" parent="Office2/PropsNode2/BookcaseNode2/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.832207, 0.208476, -0.144994)

[node name="BookcaseNode3" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -2.30658, 0, -3.12869)

[node name="BookcaseClosedWideProp" parent="Office2/PropsNode2/BookcaseNode3" instance=ExtResource("15_qaf2w")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="BooksProp" parent="Office2/PropsNode2/BookcaseNode3/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, 1.50996e-07, 0, 1, 0, -1.50996e-07, 0, -1, -0.842951, 1.64929, -0.144994)

[node name="BooksProp2" parent="Office2/PropsNode2/BookcaseNode3/BookcaseClosedWideProp" instance=ExtResource("25_aaags")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.75919, 0.93107, -0.144994)

[node name="DeskCornerNode3" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -1.07873, 0, -11.8559)

[node name="DeskCornerProp" parent="Office2/PropsNode2/DeskCornerNode3" instance=ExtResource("12_oxr8q")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office2/PropsNode2/DeskCornerNode3" instance=ExtResource("7_5wnvk")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0, 0, 0.5)

[node name="ComputerMouseProp" parent="Office2/PropsNode2/DeskCornerNode3" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.5, 0, 0.866025, 0, 1, 0, -0.866025, 0, -0.5, 0.599998, 1.15822, 0.8)
enabled = false

[node name="ComputerScreenProp" parent="Office2/PropsNode2/DeskCornerNode3" instance=ExtResource("6_05txj")]
transform = Transform3D(0.707107, 0, -0.707107, 0, 1, 0, 0.707107, 0, 0.707107, 0.799999, 1.2, -0.5)

[node name="laptopProp" parent="Office2/PropsNode2/DeskCornerNode3" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0.900002, 1.15822, 0.3)

[node name="DeskPcNode8" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -2.25433, 0, 0.744051)

[node name="DeskProp" parent="Office2/PropsNode2/DeskPcNode8" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="ChairDeskProp" parent="Office2/PropsNode2/DeskPcNode8" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.280828, 0, 0.839127)

[node name="ComputerKeyboardProp" parent="Office2/PropsNode2/DeskPcNode8" instance=ExtResource("4_hwm02")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, 0.285265)

[node name="ComputerMouseProp" parent="Office2/PropsNode2/DeskPcNode8" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.922181, 0, -0.386759, 0, 1, 0, 0.386759, 0, -0.922181, 0.779282, 1.15678, 0.361938)
enabled = false

[node name="ComputerScreenProp" parent="Office2/PropsNode2/DeskPcNode8" instance=ExtResource("6_05txj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.289186, 1.15678, -0.10409)

[node name="BookProp" parent="Office2/PropsNode2/DeskPcNode8" instance=ExtResource("26_blxbc")]
transform = Transform3D(3.05221e-08, 0.698265, -0.715839, 1, -4.37114e-08, -5.32907e-15, -3.12903e-08, -0.715839, -0.698265, -0.809757, 1.19226, 0.473269)

[node name="DeskLaptopNode6" type="Node3D" parent="Office2/PropsNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -2.25433, 0, 5.24405)

[node name="DeskProp" parent="Office2/PropsNode2/DeskLaptopNode6" instance=ExtResource("13_iipbe")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="ComputerMouseProp" parent="Office2/PropsNode2/DeskLaptopNode6" instance=ExtResource("5_2s3sl")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, 0.4, 1.15726, 0.3)
enabled = false

[node name="laptopProp" parent="Office2/PropsNode2/DeskLaptopNode6" instance=ExtResource("8_r3jwd")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 1.15822, 0)

[node name="ChairDeskProp" parent="Office2/PropsNode2/DeskLaptopNode6" instance=ExtResource("7_5wnvk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.299999, 0, 1)

[node name="Trash" type="Node3D" parent="Office2"]

[node name="RugRounded2" parent="Office2/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 6.59023, 0, 4.45716)

[node name="RugRounded5" parent="Office2/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -5.40446, 0, -11.0629)

[node name="RugRounded3" parent="Office2/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3.7621, 0, 1.9503)

[node name="RugRounded4" parent="Office2/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.12526, 0, -4.98315)

[node name="WallOffice2" type="Node3D" parent="Office2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0.0609782, 0, 0.00689888)

[node name="WallCornice2" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 9.53674e-07, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice6" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 12, 0, 4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice8" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice9" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 8, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice" parent="Office2/WallOffice2" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -1.50996e-07, 0, 1, 0, 1.50996e-07, 0, -1, 8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice4" parent="Office2/WallOffice2" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice2" parent="Office2/WallOffice2" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice8" parent="Office2/WallOffice2" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, -8, 0, 4)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice3" parent="Office2/WallOffice2" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall2" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 2.86102e-06, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall8" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -8, 4, -4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice15" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -8, 0, -4.76837e-07)
material_override = ExtResource("22_uocqr")

[node name="Wall9" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -8, 4, -5.96046e-07)
material_override = ExtResource("22_uocqr")

[node name="Wall3" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall4" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 8, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice13" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 12, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall17" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 12, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall5" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, -4, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall6" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 9.53674e-07, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall7" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.38419e-07, 0, 1, 0, -2.38419e-07, 0, 1, 4, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall11" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -4, 4, 4)
material_override = ExtResource("14_dwcpb")

[node name="Wall12" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 0, 4, 4)
material_override = ExtResource("14_dwcpb")

[node name="WallCornice16" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 12, 0, 4.76837e-07)
material_override = ExtResource("22_uocqr")

[node name="Wall18" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 12, 4, 4.76837e-07)
material_override = ExtResource("22_uocqr")

[node name="WallCornice17" parent="Office2/WallOffice2" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 12, 0, 4.76837e-07)
material_override = ExtResource("22_uocqr")

[node name="Wall19" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 12, 4, 4.76837e-07)
material_override = ExtResource("22_uocqr")

[node name="Wall15" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 8, 4, 2.38419e-07)
material_override = ExtResource("14_dwcpb")

[node name="Wall10" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 12, 4, 4)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide4" parent="Office2/WallOffice2" instance=ExtResource("15_bo021")]
transform = Transform3D(1, 0, 6.3573e-08, 0, 1, 0, -6.3573e-08, 0, 1, -4, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide5" parent="Office2/WallOffice2" instance=ExtResource("15_bo021")]
transform = Transform3D(1.98616e-08, 0, -1, 0, 1, 0, 1, 0, 1.98616e-08, -8, 0, -4)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide3" parent="Office2/WallOffice2" instance=ExtResource("15_bo021")]
transform = Transform3D(1, 0, 6.3573e-08, 0, 1, 0, -6.3573e-08, 0, 1, 8, 0, 2.38419e-07)
material_override = ExtResource("14_dwcpb")

[node name="WallCorner2" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -4, 4, -7.997)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner3" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 15.9816, 4, -7.9969)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner7" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 4, -1.19209e-07)
material_override = ExtResource("14_dwcpb")

[node name="Wall13" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -4, 0, 4)
material_override = ExtResource("14_dwcpb")

[node name="Wall14" parent="Office2/WallOffice2" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 0, 0, 4)
material_override = ExtResource("14_dwcpb")

[node name="WallCorner9" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -1.19209e-07)
material_override = ExtResource("14_dwcpb")

[node name="WallCorner4" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 1.50996e-07, 0, 1, 0, -1.50996e-07, 0, -1, 7.9816, 4, 8.0031)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice6" parent="Office2/WallOffice2" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -1.50996e-07, 0, 1, 0, 1.50996e-07, 0, -1, 16, 0, -4)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner8" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 1.50996e-07, 0, 1, 0, -1.50996e-07, 0, -1, 15.9816, 4, -3.9969)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner5" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(7.54979e-08, 0, 1, 0, 1, 0, -1, 0, 7.54979e-08, -8, 4, 8.0031)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner6" parent="Office2/WallOffice2" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 1.7683e-07, 0, 1, 0, -1.7683e-07, 0, 1, -8, 4, 4)
material_override = ExtResource("16_5fxuv")

[node name="WallWindow" parent="Office2/WallOffice2" instance=ExtResource("65_u47kt")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 12)
surface_material_override/0 = ExtResource("22_uocqr")

[node name="WallWindow2" parent="Office2/WallOffice2" instance=ExtResource("65_u47kt")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, -12)
surface_material_override/0 = ExtResource("22_uocqr")

[node name="Floor2" type="Node3D" parent="Office2"]
transform = Transform3D(2, 0, 0, 0, 2.1, 0, 0, 0, 2, 0, 0, 0)

[node name="FloorFull29" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, 4, 0, -3.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull32" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, 0, 0, -3.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull35" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 2, 0, -3.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull105" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -2, 0, -7.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull107" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -2, 0, -5.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull109" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, -4, 0, -3.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull111" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -2, 0, -3.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull118" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, -4, 0, -7.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull119" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -5.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull122" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -1.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull123" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -2, 0, -1.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull124" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -2, 0, 0.0100002)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull129" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -2, 0, 2.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull137" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, -4, 0, 0.0100002)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull138" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 2.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull141" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -1.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull147" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, 4, 0, 4.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull151" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 2.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull152" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 2, 0, 4.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull153" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, 0, 0, 0.0100002)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull154" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -1.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull155" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 2, 0, -1.99)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull156" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2.18557e-07, 0, 1, 0, 1, 0, -1, 0, -2.18557e-07, 4, 0, 0.0100002)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull157" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 2.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull158" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 2, 0, 2.01)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull159" parent="Office2/Floor2" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 2, 0, 0.0100002)
material_override = ExtResource("21_sb1nk")

[node name="Light" type="Node3D" parent="Office2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.04258, 7.8, 0.210001)

[node name="LampSquareCeilingProp" parent="Office2/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Office2/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 25.0
spot_attenuation = 0.4
spot_angle = 89.99

[node name="Light2" type="Node3D" parent="Office2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.95742, 7.8, -5.99)

[node name="LampSquareCeilingProp" parent="Office2/Light2" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Office2/Light2/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 25.0
spot_attenuation = 0.4
spot_angle = 89.99

[node name="WallDoorwaySign2" parent="Office2" instance=ExtResource("25_pq8m1")]
transform = Transform3D(0.272165, 0, 4.75868e-08, 0, 0.276767, 0, -4.75868e-08, 0, 0.272165, -3.94286, 3.59304, 6.23634)
material_override = ExtResource("14_dwcpb")

[node name="Label3D3" type="Label3D" parent="Office2/WallDoorwaySign2"]
transform = Transform3D(1, 0, 4.26326e-14, 0, 0.999999, 0, -4.26326e-14, 0, 1, 0, 0.156831, 0.0179672)
text = "دفتر"
font_size = 208
outline_size = 32

[node name="WallDoorwaySign3" parent="Office2" instance=ExtResource("25_pq8m1")]
transform = Transform3D(-5.94835e-08, 0, 0.272165, 0, 0.276767, 0, -0.272165, 0, -5.94835e-08, 10.3081, 3.59304, 4.00577)
material_override = ExtResource("14_dwcpb")

[node name="Label3D4" type="Label3D" parent="Office2/WallDoorwaySign3"]
transform = Transform3D(1, 0, 8.52651e-14, 0, 0.999998, 0, -8.52651e-14, 0, 1, 0, 0.188115, 0.0161781)
text = "دفتر"
font_size = 208
outline_size = 32

[node name="Bathroom" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -56.0003, 0, -36.5032)

[node name="PropNode1" type="Node3D" parent="Bathroom"]

[node name="ToiletNode" type="Node3D" parent="Bathroom/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 6)

[node name="ToiletOpenProp" parent="Bathroom/PropNode1/ToiletNode" instance=ExtResource("11_5iwue")]

[node name="ToiletPaperProp" parent="Bathroom/PropNode1/ToiletNode" instance=ExtResource("29_aowbf")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.80528, 1.40586, 0)
enabled = false

[node name="ToiletNode2" type="Node3D" parent="Bathroom/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 6)

[node name="ToiletOpenProp" parent="Bathroom/PropNode1/ToiletNode2" instance=ExtResource("11_5iwue")]

[node name="ToiletPaperProp" parent="Bathroom/PropNode1/ToiletNode2" instance=ExtResource("29_aowbf")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.80528, 1.40586, 0)
enabled = false

[node name="ToiletNode3" type="Node3D" parent="Bathroom/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 6)

[node name="ToiletOpenProp" parent="Bathroom/PropNode1/ToiletNode3" instance=ExtResource("11_5iwue")]

[node name="ToiletPaperProp" parent="Bathroom/PropNode1/ToiletNode3" instance=ExtResource("29_aowbf")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.80528, 1.40586, 0)
enabled = false

[node name="ToiletNode4" type="Node3D" parent="Bathroom/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 0, -6)

[node name="ToiletOpenProp" parent="Bathroom/PropNode1/ToiletNode4" instance=ExtResource("11_5iwue")]

[node name="ToiletPaperProp" parent="Bathroom/PropNode1/ToiletNode4" instance=ExtResource("29_aowbf")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.80528, 1.40586, 0)
enabled = false

[node name="ToiletNode5" type="Node3D" parent="Bathroom/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0, -6)

[node name="ToiletOpenProp" parent="Bathroom/PropNode1/ToiletNode5" instance=ExtResource("11_5iwue")]

[node name="ToiletPaperProp" parent="Bathroom/PropNode1/ToiletNode5" instance=ExtResource("29_aowbf")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.80528, 1.40586, 0)
enabled = false

[node name="ToiletNode6" type="Node3D" parent="Bathroom/PropNode1"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 0, -6)

[node name="ToiletOpenProp" parent="Bathroom/PropNode1/ToiletNode6" instance=ExtResource("11_5iwue")]

[node name="ToiletPaperProp" parent="Bathroom/PropNode1/ToiletNode6" instance=ExtResource("29_aowbf")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.80528, 1.40586, 0)
enabled = false

[node name="Trash" type="Node3D" parent="Bathroom"]

[node name="WallBathroom" type="Node3D" parent="Bathroom"]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, 0.000299454, 0, 0.00320053)

[node name="WallDoorway" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="WallDoorway2" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 12, 0, 2)
material_override = ExtResource("22_uocqr")

[node name="WallDoorway3" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, -4, 0, -2)
material_override = ExtResource("23_cqyyt")

[node name="WallDoorway5" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, 0, 0, -2)
material_override = ExtResource("23_cqyyt")

[node name="WallDoorway6" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, 4, 0, -2)
material_override = ExtResource("23_cqyyt")

[node name="WallDoorway4" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, -4, 0, 6.1)
material_override = ExtResource("14_dwcpb")

[node name="WallDoorway7" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, 0, 0, 6.1)
material_override = ExtResource("14_dwcpb")

[node name="WallDoorway8" parent="Bathroom/WallBathroom" instance=ExtResource("22_7vdht")]
transform = Transform3D(1, 0, -1.06581e-14, 0, 1, 0, 1.06581e-14, 0, 1, 4, 0, 6.1)
material_override = ExtResource("14_dwcpb")

[node name="Wall2" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 4, 4, -6)
material_override = ExtResource("22_uocqr")

[node name="Wall3" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 0, 4, -6)
material_override = ExtResource("22_uocqr")

[node name="Wall4" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -4, 4, -6)
material_override = ExtResource("22_uocqr")

[node name="Wall5" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 4, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall6" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -8, 4, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall7" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 4, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall14" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, -8, 4, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall15" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, 2)
material_override = ExtResource("22_uocqr")

[node name="Wall20" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -4, 0, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall21" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall22" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall23" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 4, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall24" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 4, 0, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall25" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -4, 4, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall26" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 4, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall27" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -8, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall28" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 8, 4, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall29" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 4, 4, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall8" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 7.99387, 4, -1.99897)
material_override = ExtResource("22_uocqr")

[node name="Wall9" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 11.9939, 4, 2.00103)
material_override = ExtResource("22_uocqr")

[node name="WallCorner2" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -8, 4, -5.997)
material_override = ExtResource("22_uocqr")

[node name="WallCorner3" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39091e-08, 0, -1, 0, 1, 0, 1, 0, -1.39091e-08, 7.9816, 4, -6)
material_override = ExtResource("22_uocqr")

[node name="WallCorner4" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 5.76205e-08, 0, 1, 0, -5.76205e-08, 0, -1, 8, 4, 6)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner5" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.01332e-07, 0, 1, 0, 1, 0, -1, 0, 1.01332e-07, -8, 4, 6)
material_override = ExtResource("16_5fxuv")

[node name="Wall10" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 4, 0, -6)
material_override = ExtResource("22_uocqr")

[node name="Wall11" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 0, 0, -6)
material_override = ExtResource("22_uocqr")

[node name="Wall12" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -4, 0, -6)
material_override = ExtResource("22_uocqr")

[node name="Wall13" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall16" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 4, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall17" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 8, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall30" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -8, 0, 6)
material_override = ExtResource("14_dwcpb")

[node name="Wall31" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -4, 0, 6)
material_override = ExtResource("14_dwcpb")

[node name="Wall32" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 0, 0, 6)
material_override = ExtResource("14_dwcpb")

[node name="Wall33" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 4, 0, 6)
material_override = ExtResource("14_dwcpb")

[node name="Wall34" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -8, 0, -5.9)
material_override = ExtResource("23_cqyyt")

[node name="Wall35" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -4, 0, -5.9)
material_override = ExtResource("23_cqyyt")

[node name="Wall36" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 0, 0, -5.9)
material_override = ExtResource("23_cqyyt")

[node name="Wall37" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 4, 0, -5.9)
material_override = ExtResource("23_cqyyt")

[node name="Wall18" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, -8, 0, -2)
material_override = ExtResource("22_uocqr")

[node name="Wall19" parent="Bathroom/WallBathroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, 2)
material_override = ExtResource("22_uocqr")

[node name="WallCorner6" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -8, 0, -5.997)
material_override = ExtResource("22_uocqr")

[node name="WallCorner7" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39091e-08, 0, -1, 0, 1, 0, 1, 0, -1.39091e-08, 7.9816, 0, -6)
material_override = ExtResource("22_uocqr")

[node name="WallCorner8" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 5.76205e-08, 0, 1, 0, -5.76205e-08, 0, -1, 8, 0, 6)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner9" parent="Bathroom/WallBathroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.01332e-07, 0, 1, 0, 1, 0, -1, 0, 1.01332e-07, -8, 0, 6)
material_override = ExtResource("16_5fxuv")

[node name="Floor" type="Node3D" parent="Bathroom"]

[node name="FloorFull" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7.9997, 0, 6.0032)

[node name="FloorFull3" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7.9997, 0, -1.9968)

[node name="FloorFull4" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7.9997, 0, -5.9968)

[node name="FloorFull5" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3.9997, 0, 6.0032)

[node name="FloorFull6" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3.9997, 0, 2.0032)

[node name="FloorFull21" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7.9997, 0, 2.0032)

[node name="FloorFull7" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3.9997, 0, -1.9968)

[node name="FloorFull8" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3.9997, 0, -5.9968)

[node name="FloorFull9" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0.000299454, 0, 6.0032)

[node name="FloorFull10" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0.000299454, 0, 2.0032)

[node name="FloorFull11" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0.000299454, 0, -1.9968)

[node name="FloorFull12" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0.000299454, 0, -5.9968)

[node name="FloorFull13" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.0003, 0, 6.0032)

[node name="FloorFull14" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.0003, 0, 2.0032)

[node name="FloorFull15" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.0003, 0, -1.9968)

[node name="FloorFull16" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.0003, 0, -5.9968)

[node name="FloorFull17" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8.0003, 0, 6.0032)

[node name="FloorFull18" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8.0003, 0, 2.0032)

[node name="FloorFull19" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8.0003, 0, -1.9968)

[node name="FloorFull20" parent="Bathroom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8.0003, 0, -5.9968)

[node name="Light" type="Node3D" parent="Bathroom"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000301361, 7.8, 4.0032)

[node name="LampSquareCeilingProp" parent="Bathroom/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Bathroom/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 30.0
spot_attenuation = 0.4
spot_angle = 89.99

[node name="Light2" type="Node3D" parent="Bathroom"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.000301361, 7.8, -3.9968)

[node name="LampSquareCeilingProp" parent="Bathroom/Light2" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Bathroom/Light2/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 30.0
spot_attenuation = 0.4
spot_angle = 89.99

[node name="WallDoorwaySign2" parent="Bathroom" instance=ExtResource("25_pq8m1")]
transform = Transform3D(-5.55135e-09, 0, 0.422, 0, 0.428, 0, -0.127, 0, -1.84462e-08, 10.2488, 3.98962, -0.00452805)
material_override = ExtResource("14_dwcpb")

[node name="Sprite3D" type="Sprite3D" parent="Bathroom/WallDoorwaySign2"]
transform = Transform3D(3.31908, 0, -2.84217e-14, 0, 0.999159, 0, -7.10543e-15, 0, 1.00061, 0.000518799, 0, 0.053833)
pixel_size = 0.0045
texture = ExtResource("28_d7qek")

[node name="Storage" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, 1.5)

[node name="PropsNode1" type="Node3D" parent="Storage"]

[node name="CaseLineNode" type="Node3D" parent="Storage/PropsNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -2.55337)

[node name="BoxcaseNode1" type="Node3D" parent="Storage/PropsNode1/CaseLineNode"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0, 0, 0.499971)

[node name="BoxcaseProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode1" instance=ExtResource("33_koq8u")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 0)
enabled = false

[node name="BoxCloseProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode1/BoxcaseProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.293848, 2.84031, 0)

[node name="BoxCloseProp2" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode1/BoxcaseProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.293848, 2.03494, 0)

[node name="BoxOpenProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode1/BoxcaseProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.262561, 0.415746, 0)

[node name="BoxcaseNode2" type="Node3D" parent="Storage/PropsNode1/CaseLineNode"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 6.10122, 0, 0)

[node name="BoxcaseWideProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode2" instance=ExtResource("36_ogog8")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 0)
enabled = false

[node name="BoxCloseProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode2/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 1.02911, 1.23022, 0)

[node name="BoxCloseProp2" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode2/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.438646, 1.23022, 0)

[node name="BoxCloseProp3" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode2/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -1.01354, 2.0348, 0)

[node name="BoxCloseProp4" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode2/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.973707, 2.83217, 0)

[node name="BoxOpenProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode2/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.274758, 0.422043, 0)

[node name="BoxcaseNode3" type="Node3D" parent="Storage/PropsNode1/CaseLineNode"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -6, 0, 0)

[node name="BoxcaseWideProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode3" instance=ExtResource("36_ogog8")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0, 0)
enabled = false

[node name="BoxCloseProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode3/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, -0.95524, 0.418916, 0)

[node name="BoxOpenProp" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode3/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, -0.0797541, 0.418916, 0)

[node name="BoxCloseProp2" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode3/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0.357317, 2.03536, 0)

[node name="BoxOpenProp2" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode3/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, 0.951614, 1.21474, 0)

[node name="BoxOpenProp3" parent="Storage/PropsNode1/CaseLineNode/BoxcaseNode3/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(1, 0, -7.10543e-15, 0, 1, 0, 7.10543e-15, 0, 1, -0.977241, 2.85449, 0)

[node name="CaseLineNode2" type="Node3D" parent="Storage/PropsNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 2.71643)

[node name="BoxcaseNode4" type="Node3D" parent="Storage/PropsNode1/CaseLineNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 5.99932, 0, 0.441483)

[node name="BoxcaseProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode4" instance=ExtResource("33_koq8u")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 0)
enabled = false

[node name="BoxCloseProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode4/BoxcaseProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.303353, 2.85392, 0)

[node name="BoxOpenProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode4/BoxcaseProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.298684, 0.417551, 0)

[node name="BoxcaseNode11" type="Node3D" parent="Storage/PropsNode1/CaseLineNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0, 0, 0)

[node name="BoxcaseWideProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode11" instance=ExtResource("36_ogog8")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 0)
enabled = false

[node name="BoxCloseProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode11/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.973931, 0.424045, 0)

[node name="BoxCloseProp2" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode11/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.353534, 1.17876, 0)

[node name="BoxCloseProp3" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode11/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.374116, 2.0352, 0)

[node name="BoxOpenProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode11/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.949076, 2.85708, 0)

[node name="BoxOpenProp2" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode11/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.949076, 0.414592, 0)

[node name="BoxcaseNode10" type="Node3D" parent="Storage/PropsNode1/CaseLineNode2"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -6, 0, 0)

[node name="BoxcaseWideProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode10" instance=ExtResource("36_ogog8")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 0, 0)
enabled = false

[node name="BoxCloseProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode10/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.97202, 0.418657, 0)

[node name="BoxOpenProp" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode10/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.0621629, 0.418362, 0)

[node name="BoxCloseProp2" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode10/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.0625277, 2.04077, 0)

[node name="BoxCloseProp3" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode10/BoxcaseWideProp" instance=ExtResource("13_tygdv")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -0.908106, 2.84827, 0)

[node name="BoxOpenProp2" parent="Storage/PropsNode1/CaseLineNode2/BoxcaseNode10/BoxcaseWideProp" instance=ExtResource("14_y66er")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0.90897, 1.22787, 0)

[node name="BoxCloseProp" parent="Storage/PropsNode1" instance=ExtResource("13_tygdv")]
transform = Transform3D(0.573648, 0, -0.819102, 0, 1, 0, 0.819102, 0, 0.573648, 0.234564, 0, -4.44665)

[node name="BoxCloseProp2" parent="Storage/PropsNode1" instance=ExtResource("13_tygdv")]
transform = Transform3D(-0.781586, 0, -0.623798, 0, 1, 0, 0.623798, 0, -0.781586, -0.279274, 0, -4.37737)

[node name="BoxCloseProp3" parent="Storage/PropsNode1" instance=ExtResource("13_tygdv")]
transform = Transform3D(-0.040184, 0, -0.999192, 0, 1, 0, 0.999192, 0, -0.040184, 0.012001, 0, -3.92124)

[node name="BoxOpenProp" parent="Storage/PropsNode1" instance=ExtResource("14_y66er")]
transform = Transform3D(0.832341, 0, -0.554263, 0, 1, 0, 0.554263, 0, 0.832341, 0, 0.557873, -4.22121)

[node name="WallStorage" type="Node3D" parent="Storage"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0, 0, 2)

[node name="WallCornice" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 6, 0, 0)
material_override = ExtResource("22_uocqr")

[node name="WallCornice4" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 6, 0, -4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice6" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 6, 0, 4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice9" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -2, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice10" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 0, 4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice11" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 0, 0)
material_override = ExtResource("22_uocqr")

[node name="WallCornice12" parent="Storage/WallStorage" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 0, -4)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice" parent="Storage/WallStorage" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 2, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice4" parent="Storage/WallStorage" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -6, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice2" parent="Storage/WallStorage" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice3" parent="Storage/WallStorage" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 2, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall4" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -2, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall7" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, -2, 4, 12.003)
material_override = ExtResource("22_uocqr")

[node name="Wall11" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 4, -4)
material_override = ExtResource("22_uocqr")

[node name="Wall13" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 4, 4)
material_override = ExtResource("22_uocqr")

[node name="Wall14" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -10, 4, 9.53674e-07)
material_override = ExtResource("22_uocqr")

[node name="Wall8" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 6, 4, 0)
material_override = ExtResource("22_uocqr")

[node name="Wall9" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 6, 4, -4)
material_override = ExtResource("22_uocqr")

[node name="Wall10" parent="Storage/WallStorage" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 6, 4, 4)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide" parent="Storage/WallStorage" instance=ExtResource("15_bo021")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, -2, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCorner2" parent="Storage/WallStorage" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -6, 4, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner3" parent="Storage/WallStorage" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39091e-08, 0, -1, 0, 1, 0, 1, 0, -1.39091e-08, 2, 4, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner4" parent="Storage/WallStorage" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 5.76205e-08, 0, 1, 0, -5.76205e-08, 0, -1, 2, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner5" parent="Storage/WallStorage" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.01332e-07, 0, 1, 0, 1, 0, -1, 0, 1.01332e-07, -6, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="Floor" type="Node3D" parent="Storage"]

[node name="FloorFull" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, -4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull2" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 0)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull3" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull4" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, -4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull5" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, 0)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull6" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, 4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull7" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, -4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull8" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull9" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull10" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, -4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull11" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, 0)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull12" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, 4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull13" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, -4)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull14" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 0)
material_override = ExtResource("29_8k3ct")

[node name="FloorFull15" parent="Storage/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 4)
material_override = ExtResource("29_8k3ct")

[node name="Light" type="Node3D" parent="Storage"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 7.8, 0)

[node name="LampSquareCeilingProp" parent="Storage/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Storage/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 30.0
spot_attenuation = 0.4
spot_angle = 89.99

[node name="WallDoorwaySign" parent="Storage" instance=ExtResource("25_pq8m1")]
transform = Transform3D(4.47035e-08, 0, -0.272165, 0, 0.276767, 0, 0.272165, 0, 4.47035e-08, -10.2406, 3.59304, 0.000404358)
material_override = ExtResource("14_dwcpb")

[node name="Label3D" type="Label3D" parent="Storage/WallDoorwaySign"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.174248, 0.0408936)
text = "انبار
"
font_size = 208
outline_size = 32

[node name="Boss" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 9.5)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Boss"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.49945, 10)
mesh = SubResource("QuadMesh_3mcft")

[node name="PropNode1" type="Node3D" parent="Boss"]

[node name="BossTable" type="Node3D" parent="Boss/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -5.99089)

[node name="TableClothProp" parent="Boss/PropNode1/BossTable" instance=ExtResource("40_m5vs2")]
enabled = false

[node name="PlantSmall1Prop" parent="Boss/PropNode1/BossTable" instance=ExtResource("19_i6gbk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.25446, 1.28275, 0.472095)
enabled = false

[node name="PlantSmall2Prop" parent="Boss/PropNode1/BossTable" instance=ExtResource("20_levdh")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.909788, 1.28275, 0.472095)
enabled = false

[node name="PlantSmall3Prop" parent="Boss/PropNode1/BossTable" instance=ExtResource("21_qjbph")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.556539, 1.28275, 0.472095)
enabled = false

[node name="ChairBossProp" parent="Boss/PropNode1/BossTable" instance=ExtResource("23_jcohf")]
transform = Transform3D(-1, 0, -1.50996e-07, 0, 1, 0, 1.50996e-07, 0, -1, 0, 0, -1.14996)
enabled = false

[node name="LampRoundTableProp" parent="Boss/PropNode1/BossTable" instance=ExtResource("23_y32li")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.2681, 1.28304, 0.482838)
enabled = false

[node name="LoungeNode" type="Node3D" parent="Boss/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.897)

[node name="RugRounded" parent="Boss/PropNode1/LoungeNode" instance=ExtResource("16_a27ce")]
transform = Transform3D(-1.31134e-07, 0, 3, 0, 3, 0, -3, 0, -1.31134e-07, 0, 0, 1)

[node name="LoungeDesignSofaProp" parent="Boss/PropNode1/LoungeNode" instance=ExtResource("41_b2kem")]
transform = Transform3D(-4.47035e-08, 0, -1, 0, 1, 0, 1, 0, -4.47035e-08, -3.4, 0, -0.299999)
enabled = false

[node name="LoungeDesignSofaProp2" parent="Boss/PropNode1/LoungeNode" instance=ExtResource("41_b2kem")]
transform = Transform3D(-7.45058e-09, 0, 1, 0, 1, 0, -1, 0, -7.45058e-09, 3.4, 0, -0.2)
enabled = false

[node name="LoungeDesignChairProp" parent="Boss/PropNode1/LoungeNode" instance=ExtResource("42_yg6dn")]
transform = Transform3D(0.642788, 0, -0.766044, 0, 1, 0, 0.766044, 0, 0.642788, -2.3, 0, 4.4)
enabled = false

[node name="LoungeDesignChairProp2" parent="Boss/PropNode1/LoungeNode" instance=ExtResource("42_yg6dn")]
transform = Transform3D(0.642788, 0, 0.766045, 0, 1, 0, -0.766045, 0, 0.642788, 2.3, 0, 4.4)
enabled = false

[node name="Trash" type="Node3D" parent="Boss"]

[node name="Bear" parent="Boss/Trash" instance=ExtResource("17_6khit")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0.410872, -6.19657)

[node name="WallBoss" type="Node3D" parent="Boss"]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 0, 0, 0)

[node name="WallCornice" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.4869e-14, 0, 1, 0, -2.4869e-14, 0, 1, 4, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice2" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.4869e-14, 0, 1, 0, -2.4869e-14, 0, 1, 3.57628e-07, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice3" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 2.4869e-14, 0, 1, 0, -2.4869e-14, 0, 1, -4, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice4" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 11.9939, 0, -3.99897)
material_override = ExtResource("22_uocqr")

[node name="WallCornice6" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 11.9939, 0, 4.00103)
material_override = ExtResource("22_uocqr")

[node name="WallCornice9" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice10" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, 4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice14" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, 4.76996e-08)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice" parent="Boss/WallBoss" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice4" parent="Boss/WallBoss" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice3" parent="Boss/WallBoss" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall3" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice11" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -9.53674e-07, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall4" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -9.53674e-07, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice12" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall11" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall5" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.4869e-14, 0, 1, 0, -2.4869e-14, 0, 1, -4, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall6" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.4869e-14, 0, 1, 0, -2.4869e-14, 0, 1, 3.57628e-07, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall7" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 2.4869e-14, 0, 1, 0, -2.4869e-14, 0, 1, 4, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall13" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, 4)
material_override = ExtResource("22_uocqr")

[node name="Wall14" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, 5.96046e-08)
material_override = ExtResource("22_uocqr")

[node name="WallCornice15" parent="Boss/WallBoss" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, -4)
material_override = ExtResource("22_uocqr")

[node name="Wall15" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, -4)
material_override = ExtResource("22_uocqr")

[node name="Wall8" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 11.9939, 4, 0.00103265)
material_override = ExtResource("22_uocqr")

[node name="Wall9" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 11.9939, 4, -3.99897)
material_override = ExtResource("22_uocqr")

[node name="Wall10" parent="Boss/WallBoss" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 11.9939, 4, 4.00103)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide" parent="Boss/WallBoss" instance=ExtResource("15_bo021")]
transform = Transform3D(-1.62921e-07, 0, 1, 0, 1, 0, -1, 0, -1.62921e-07, 12.0123, 0, -0.00206786)
material_override = ExtResource("22_uocqr")

[node name="WallCorner3" parent="Boss/WallBoss" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39091e-08, 0, -1, 0, 1, 0, 1, 0, -1.39091e-08, 7.9816, 4, -7.9969)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice5" parent="Boss/WallBoss" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, -1.42109e-14, 0, 1, 0, 1.42109e-14, 0, 1, -8.01075, 0, -8.00765)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner6" parent="Boss/WallBoss" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -8.00765, 4, -7.98925)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner4" parent="Boss/WallBoss" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 5.76205e-08, 0, 1, 0, -5.76205e-08, 0, -1, 7.9816, 4, 8.0031)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner5" parent="Boss/WallBoss" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.01332e-07, 0, 1, 0, 1, 0, -1, 0, 1.01332e-07, -8, 4, 8.0031)
material_override = ExtResource("16_5fxuv")

[node name="Floor" type="Node3D" parent="Boss"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, 1)

[node name="Mosaic" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7, 0, 7)

[node name="Mosaic2" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3, 0, 7)

[node name="Mosaic3" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 1, 0, 7)

[node name="Mosaic4" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5, 0, 7)

[node name="Mosaic5" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 9, 0, 7)

[node name="Mosaic6" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7, 0, 3)

[node name="Mosaic7" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3, 0, 3)

[node name="Mosaic8" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 1, 0, 3)

[node name="Mosaic9" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5, 0, 3)

[node name="Mosaic10" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 9, 0, 3)

[node name="Mosaic11" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7, 0, -1)

[node name="Mosaic12" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3, 0, -1)

[node name="Mosaic13" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 1, 0, -1)

[node name="Mosaic14" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5, 0, -1)

[node name="Mosaic15" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 9, 0, -1)

[node name="Mosaic16" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7, 0, -5)

[node name="Mosaic17" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3, 0, -5)

[node name="Mosaic18" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 1, 0, -5)

[node name="Mosaic19" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5, 0, -5)

[node name="Mosaic20" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 9, 0, -5)

[node name="Mosaic21" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -7, 0, -9)

[node name="Mosaic22" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -3, 0, -9)

[node name="Mosaic23" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 1, 0, -9)

[node name="Mosaic24" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 5, 0, -9)

[node name="Mosaic25" parent="Boss/Floor" instance=ExtResource("39_c2eli")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 9, 0, -9)

[node name="Light" type="Node3D" parent="Boss"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 7.8, 0)

[node name="LampSquareCeilingProp" parent="Boss/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Boss/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 87.18
spot_attenuation = 0.4
spot_angle = 90.0

[node name="WallDoorwaySign" parent="Boss" instance=ExtResource("25_pq8m1")]
transform = Transform3D(0.272165, 0, 4.75868e-08, 0, 0.276767, 0, -4.75868e-08, 0, 0.272165, -0.00100327, 3.59304, 10.256)
material_override = ExtResource("14_dwcpb")

[node name="Label3D" type="Label3D" parent="Boss/WallDoorwaySign"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.3551e-09, 0.2019, 0.0312347)
text = "ریاست"
font_size = 208
outline_size = 32

[node name="Kitchen" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, -18.5)

[node name="PropNode1" type="Node3D" parent="Kitchen"]

[node name="StoveMultiCounterProp" parent="Kitchen/PropNode1" instance=ExtResource("27_x2ftt")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -8.332, 0.05, -2.83141)

[node name="TomatoeBascketProp" parent="Kitchen/PropNode1" instance=ExtResource("28_phrs6")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.39869, 0.0267457, -5.99655)

[node name="BunBasketProp" parent="Kitchen/PropNode1" instance=ExtResource("29_14ru0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.39869, 0.0267457, -8.59822)

[node name="CarrotBasketProp" parent="Kitchen/PropNode1" instance=ExtResource("30_opbe2")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.77593, 0.0267457, -8.6062)

[node name="CheeseBasketProp" parent="Kitchen/PropNode1" instance=ExtResource("31_k6m0g")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3.19177, 0.0267457, -8.64025)

[node name="LettuceBasketProp" parent="Kitchen/PropNode1" instance=ExtResource("32_bwkaj")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.68474, 0.0620684, 8.70593)

[node name="OnionsBasketProp" parent="Kitchen/PropNode1" instance=ExtResource("33_51aou")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.10181, 0.0620684, 8.6942)

[node name="PotatoesBasketProp" parent="Kitchen/PropNode1" instance=ExtResource("34_8ji03")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.55533, 0.0620684, 8.6942)

[node name="SinkProp" parent="Kitchen/PropNode1" instance=ExtResource("36_626kn")]
transform = Transform3D(-1.4, 0, -2.11394e-07, 0, 1.4, 0, 2.11394e-07, 0, -1.4, -4.77422, 0, 8.36084)

[node name="CuttingBoardProp" parent="Kitchen/PropNode1" instance=ExtResource("42_wtkkc")]
transform = Transform3D(-6.99382e-08, 0, 1.6, 0, 1.6, 0, -1.6, 0, -6.99382e-08, -8.14746, 1.41681, 0.0025425)

[node name="TableNode" type="Node3D" parent="Kitchen/PropNode1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.00995, 0.000611175, -6.01221)

[node name="TableRoundProp" parent="Kitchen/PropNode1/TableNode" instance=ExtResource("38_y1q72")]
enabled = false

[node name="ChairModernCushionProp" parent="Kitchen/PropNode1/TableNode" instance=ExtResource("39_5jv04")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 1.4, 0, 0)

[node name="ChairModernCushionProp2" parent="Kitchen/PropNode1/TableNode" instance=ExtResource("39_5jv04")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.6)

[node name="ChairModernCushionProp3" parent="Kitchen/PropNode1/TableNode" instance=ExtResource("39_5jv04")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -1.4, 0, 0)

[node name="ChairModernCushionProp4" parent="Kitchen/PropNode1/TableNode" instance=ExtResource("39_5jv04")]
transform = Transform3D(-1, 0, -1.50996e-07, 0, 1, 0, 1.50996e-07, 0, -1, 0, 0, -1.6)

[node name="MacrowavetProp" parent="Kitchen/PropNode1" instance=ExtResource("40_t4jyg")]
transform = Transform3D(-5.24537e-08, 0, 1.2, 0, 1.2, 0, -1.2, 0, -5.24537e-08, -8.52454, 1.42425, 3.01969)

[node name="FridgeProp" parent="Kitchen/PropNode1" instance=ExtResource("47_mbd4h")]
transform = Transform3D(-1.4, 0, -2.11394e-07, 0, 1.4, 0, 2.11394e-07, 0, -1.4, -1.21056, -4.76837e-07, 8.37097)

[node name="Trash" type="Node3D" parent="Kitchen"]

[node name="Kitchencounter" parent="Kitchen/Trash" instance=ExtResource("36_iibwf")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -8.33999, 1.85437e-07, -0.0200882)

[node name="Kitchencounter2" parent="Kitchen/Trash" instance=ExtResource("36_iibwf")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -8.33999, 1.85437e-07, 2.77417)

[node name="Kitchencounter3" parent="Kitchen/Trash" instance=ExtResource("36_iibwf")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -8.33999, 1.85437e-07, 5.57739)

[node name="Kitchencounter4" parent="Kitchen/Trash" instance=ExtResource("36_iibwf")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -8.33872, 1.85437e-07, 8.37707)

[node name="KitchenCabinet" parent="Kitchen/Trash" instance=ExtResource("37_7igkm")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -9.74893, -7.68237e-07, -5.49685)

[node name="KitchenCabinet2" parent="Kitchen/Trash" instance=ExtResource("37_7igkm")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -9.74893, -7.68237e-07, 0.266094)

[node name="KitchenCabinet3" parent="Kitchen/Trash" instance=ExtResource("37_7igkm")]
transform = Transform3D(-6.11959e-08, 0, 1.4, 0, 1.4, 0, -1.4, 0, -6.11959e-08, -9.74893, -7.68237e-07, 5.9661)

[node name="Kitchencabinet" parent="Kitchen/Trash" instance=ExtResource("38_1lp2e")]
transform = Transform3D(-6.11959e-08, 1.4, -6.11959e-08, 0, -6.11959e-08, -1.4, -1.4, -6.11959e-08, 2.67496e-15, -9.73998, -7.68237e-07, -8.32349)

[node name="KitchencabinetHalf2" parent="Kitchen/Trash" instance=ExtResource("39_eu1pn")]
transform = Transform3D(-6.11959e-08, 1.4, -6.11959e-08, 0, -6.11959e-08, -1.4, -1.4, -6.11959e-08, 2.67496e-15, -9.73357, -7.68237e-07, 3.11528)

[node name="hood" parent="Kitchen/Trash" instance=ExtResource("40_xvs3c")]
transform = Transform3D(-6.11959e-08, 1.4, -6.11959e-08, 0, -6.11959e-08, -1.4, -1.4, -6.11959e-08, 2.67496e-15, -9.71045, 6.62275e-07, -2.61179)

[node name="WallKitchen" type="Node3D" parent="Kitchen"]

[node name="WallCornice" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice2" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice3" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice4" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 0, 4)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice6" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 0, -4)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice7" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice9" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice10" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, -4)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice11" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, 9.53674e-07)
material_override = ExtResource("16_5fxuv")

[node name="WallCornice12" parent="Kitchen/WallKitchen" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, 4)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice" parent="Kitchen/WallKitchen" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice4" parent="Kitchen/WallKitchen" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice2" parent="Kitchen/WallKitchen" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice3" parent="Kitchen/WallKitchen" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall2" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 4, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall3" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 4, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall4" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 4, -8)
material_override = ExtResource("16_5fxuv")

[node name="Wall5" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 4, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="Wall6" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="Wall7" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -4, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="Wall11" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 4, 0)
material_override = ExtResource("16_5fxuv")

[node name="Wall12" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 4, 4)
material_override = ExtResource("16_5fxuv")

[node name="Wall13" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 4, -4)
material_override = ExtResource("16_5fxuv")

[node name="Wall8" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 4, 0)
material_override = ExtResource("16_5fxuv")

[node name="Wall9" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 4, 4)
material_override = ExtResource("16_5fxuv")

[node name="Wall10" parent="Kitchen/WallKitchen" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 4, -4)
material_override = ExtResource("16_5fxuv")

[node name="WallDoorwayWide" parent="Kitchen/WallKitchen" instance=ExtResource("15_bo021")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 7.9816, 0, 0.0031004)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner2" parent="Kitchen/WallKitchen" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -8, 4, -7.997)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner3" parent="Kitchen/WallKitchen" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39091e-08, 0, -1, 0, 1, 0, 1, 0, -1.39091e-08, 7.9816, 4, -7.9969)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner4" parent="Kitchen/WallKitchen" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 5.76205e-08, 0, 1, 0, -5.76205e-08, 0, -1, 7.9816, 4, 8.0031)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner5" parent="Kitchen/WallKitchen" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.01332e-07, 0, 1, 0, 1, 0, -1, 0, 1.01332e-07, -8, 4, 8.0031)
material_override = ExtResource("16_5fxuv")

[node name="WallWindow" parent="Kitchen/WallKitchen" instance=ExtResource("65_u47kt")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -8)
surface_material_override/0 = ExtResource("16_5fxuv")

[node name="Floor" type="Node3D" parent="Kitchen"]

[node name="FloorFullQuarter" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, 8)

[node name="FloorFullQuarter2" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 8)

[node name="FloorFullQuarter3" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, 8)

[node name="FloorFullQuarter4" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 4)

[node name="FloorFullQuarter5" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, 4)

[node name="FloorFullQuarter6" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 4)

[node name="FloorFullQuarter7" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, 4)

[node name="FloorFullQuarter8" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 4)

[node name="FloorFullQuarter24" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 8)

[node name="FloorFullQuarter25" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 8)

[node name="FloorFullQuarter9" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 0)

[node name="FloorFullQuarter10" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, 0)

[node name="FloorFullQuarter11" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0)

[node name="FloorFullQuarter12" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, 0)

[node name="FloorFullQuarter13" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 0)

[node name="FloorFullQuarter14" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, -4)

[node name="FloorFullQuarter15" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, -4)

[node name="FloorFullQuarter16" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, -4)

[node name="FloorFullQuarter17" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, -4)

[node name="FloorFullQuarter19" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4, 0, -8)

[node name="FloorFullQuarter20" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, -8)

[node name="FloorFullQuarter21" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4, 0, -8)

[node name="FloorFullQuarter18" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, -4)

[node name="FloorFullQuarter22" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, -8)

[node name="FloorFullQuarter23" parent="Kitchen/Floor" instance=ExtResource("59_j7vue")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, -8)

[node name="Light" type="Node3D" parent="Kitchen"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 7.8, 0)

[node name="LampSquareCeilingProp" parent="Kitchen/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="Kitchen/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 27.0
spot_attenuation = 0.4
spot_angle = 89.99

[node name="WallDoorwaySign" parent="Kitchen" instance=ExtResource("25_pq8m1")]
transform = Transform3D(-2.09101e-08, 0, 0.272165, 0, 0.276767, 0, -0.272165, 0, -2.09101e-08, 10.2271, 3.59304, 0.00244331)
material_override = ExtResource("14_dwcpb")

[node name="Label3D" type="Label3D" parent="Kitchen/WallDoorwaySign"]
transform = Transform3D(1, 0, 1.42109e-14, 0, 1, 0, -1.42109e-14, 0, 1, 0, 0.151862, 0.0225067)
text = "آشپزخانه
"
font_size = 208
outline_size = 32

[node name="RestRoom" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, -22.5)

[node name="PropNode" type="Node3D" parent="RestRoom"]

[node name="Table" type="Node3D" parent="RestRoom/PropNode"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8.14904, 0, -8.30057)

[node name="RadioProp" parent="RestRoom/PropNode/Table" instance=ExtResource("48_tvxv1")]
transform = Transform3D(-0.860725, 0, -0.50907, 0, 1, 0, 0.50907, 0, -0.860725, -0.434383, 0.924416, -0.500296)

[node name="PlantSmall1Prop" parent="RestRoom/PropNode/Table" instance=ExtResource("19_i6gbk")]
transform = Transform3D(0.987323, 0, -0.158727, 0, 1, 0, 0.158727, 0, 0.987323, 0.301252, 0.92333, -0.593021)

[node name="PlantSmall2Prop" parent="RestRoom/PropNode/Table" instance=ExtResource("20_levdh")]
transform = Transform3D(0.987323, 0, -0.158727, 0, 1, 0, 0.158727, 0, 0.987323, 0.660122, 0.922729, -0.323635)

[node name="TableCoffeeGlassSquareProp" parent="RestRoom/PropNode/Table" instance=ExtResource("71_epmsg")]
enabled = false

[node name="BedBunkProp" parent="RestRoom/PropNode" instance=ExtResource("49_vb48k")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.04461, 0, 6.59702)

[node name="BedBunkProp2" parent="RestRoom/PropNode" instance=ExtResource("49_vb48k")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7.94069, 0, 0.147713)

[node name="BedBunkProp3" parent="RestRoom/PropNode" instance=ExtResource("49_vb48k")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.89424, 0, 6.73912)

[node name="BedBunkProp4" parent="RestRoom/PropNode" instance=ExtResource("49_vb48k")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.74246, 0, 0.0953865)

[node name="LoungeChairProp" parent="RestRoom/PropNode" instance=ExtResource("68_jjhsn")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 8.2, 0, -5)

[node name="LoungeChairProp2" parent="RestRoom/PropNode" instance=ExtResource("68_jjhsn")]
transform = Transform3D(-0.866025, 0, -0.5, 0, 1, 0, 0.5, 0, -0.866025, -2.8, 0, -1)

[node name="LoungeSofaProp" parent="RestRoom/PropNode" instance=ExtResource("69_nted7")]
transform = Transform3D(-0.965926, 0, 0.258819, 0, 1, 0, -0.258819, 0, -0.965926, 2.2, 0, -0.799999)

[node name="PottedPlant" parent="RestRoom/PropNode" instance=ExtResource("22_5uxf6")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.87034, 0, -8.68267)

[node name="SpeakerProp" parent="RestRoom/PropNode" instance=ExtResource("52_vpwwf")]
transform = Transform3D(0.939693, 0, 0.34202, 0, 1, 0, -0.34202, 0, 0.939693, -7.26482, 0, -8.92233)

[node name="SpeakerProp2" parent="RestRoom/PropNode" instance=ExtResource("52_vpwwf")]
transform = Transform3D(0.939693, 0, -0.34202, 0, 1, 0, 0.34202, 0, 0.939693, 6.25153, 0, -8.84355)

[node name="SpeakerProp3" parent="RestRoom/PropNode" instance=ExtResource("52_vpwwf")]
transform = Transform3D(-0.939692, 0, -0.34202, 0, 1, 0, 0.34202, 0, -0.939692, 2.10321, 0, 8.1781)

[node name="SpeakerProp4" parent="RestRoom/PropNode" instance=ExtResource("52_vpwwf")]
transform = Transform3D(-0.939692, 0, 0.34202, 0, 1, 0, -0.34202, 0, -0.939692, -2.08752, 0, 8.19848)

[node name="TableTV" type="Node3D" parent="RestRoom/PropNode"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 8.69591)

[node name="TableCoffeeProp" parent="RestRoom/PropNode/TableTV" instance=ExtResource("53_ub11u")]
enabled = false

[node name="TelevisionModernProp" parent="RestRoom/PropNode/TableTV" instance=ExtResource("54_rjbba")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.924125, 0.331102)

[node name="PlantSmall1Prop" parent="RestRoom/PropNode/TableTV" instance=ExtResource("19_i6gbk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.771315, 0.924657, -0.380726)

[node name="PlantSmall3Prop" parent="RestRoom/PropNode/TableTV" instance=ExtResource("21_qjbph")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.07427, 0.922033, -0.105196)

[node name="PlantSmall2Prop" parent="RestRoom/PropNode/TableTV" instance=ExtResource("19_i6gbk")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.819225, 0.924657, -0.447641)

[node name="PlantSmall4Prop" parent="RestRoom/PropNode/TableTV" instance=ExtResource("20_levdh")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.04732, 0.926498, -0.160117)

[node name="LampSquareFloorProp" parent="RestRoom/PropNode" instance=ExtResource("55_52jm4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.622324, 0, -1.60682)

[node name="Trash" type="Node3D" parent="RestRoom"]

[node name="RugRounded" parent="RestRoom/Trash" instance=ExtResource("16_a27ce")]
transform = Transform3D(-3, 0, 0.00303671, 0, 3, 0, -0.00303671, 0, -3, -0.004, 0, 1.734)

[node name="WallRestroom" type="Node3D" parent="RestRoom"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0, 0, 0)

[node name="WallCornice" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, 4, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice3" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, -4, 0, 12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice4" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 11.9939, 0, -3.99897)
material_override = ExtResource("22_uocqr")

[node name="WallCornice6" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 11.9939, 0, 4.00103)
material_override = ExtResource("22_uocqr")

[node name="WallCornice7" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -4, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice8" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 1.90735e-06, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice9" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 4, 0, -12)
material_override = ExtResource("22_uocqr")

[node name="WallCornice10" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, 4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice11" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, -1.90735e-06)
material_override = ExtResource("22_uocqr")

[node name="WallCornice12" parent="RestRoom/WallRestroom" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 0, -4)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice" parent="RestRoom/WallRestroom" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice2" parent="RestRoom/WallRestroom" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice3" parent="RestRoom/WallRestroom" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 8, 0, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice4" parent="RestRoom/WallRestroom" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -8, 0, 8)
material_override = ExtResource("16_5fxuv")

[node name="Wall2" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -4, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall3" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 1.90735e-06, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall4" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 4, 4, -12)
material_override = ExtResource("22_uocqr")

[node name="Wall5" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, -4, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall6" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, 0, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall7" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, 4, 4, 12)
material_override = ExtResource("22_uocqr")

[node name="Wall11" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, -4)
material_override = ExtResource("22_uocqr")

[node name="Wall13" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, 4)
material_override = ExtResource("22_uocqr")

[node name="Wall14" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, -1.90735e-06)
material_override = ExtResource("22_uocqr")

[node name="Wall8" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 11.9939, 4, 0.00103188)
material_override = ExtResource("22_uocqr")

[node name="Wall9" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 11.9939, 4, -3.99897)
material_override = ExtResource("22_uocqr")

[node name="Wall10" parent="RestRoom/WallRestroom" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, 11.9939, 4, 4.00103)
material_override = ExtResource("22_uocqr")

[node name="WallDoorwayWide" parent="RestRoom/WallRestroom" instance=ExtResource("15_bo021")]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 12.0123, 0, -0.00206852)
material_override = ExtResource("22_uocqr")

[node name="WallCorner2" parent="RestRoom/WallRestroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98023e-08, 0, 1, 0, -2.98023e-08, 0, 1, -8, 4, -7.997)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner3" parent="RestRoom/WallRestroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39091e-08, 0, -1, 0, 1, 0, 1, 0, -1.39091e-08, 7.9816, 4, -8)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner4" parent="RestRoom/WallRestroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, 5.76205e-08, 0, 1, 0, -5.76205e-08, 0, -1, 8, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner5" parent="RestRoom/WallRestroom" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.01332e-07, 0, 1, 0, 1, 0, -1, 0, 1.01332e-07, -8, 4, 8)
material_override = ExtResource("16_5fxuv")

[node name="WallWindow" parent="RestRoom/WallRestroom" instance=ExtResource("65_u47kt")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.90735e-06, 0, 12)
surface_material_override/0 = ExtResource("22_uocqr")

[node name="Floor" type="Node3D" parent="RestRoom"]

[node name="FloorFull" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull2" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -4, 0, 8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull3" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull4" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 4, 0, 8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull5" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull6" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -8, 0, 4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull7" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -4, 0, 4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull8" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 0, 0, 4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull9" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 1.78814e-07, 0, 2, 0, -1.78814e-07, 0, -2, 4, 0, 4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull10" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 8, 0, 4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull11" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, 0)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull12" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -4, 0, 0)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull13" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull14" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2.62268e-07, 0, -2, 0, 2, 0, 2, 0, 2.62268e-07, 4, 0, 0)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull15" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, 0)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull16" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -8, 0, -4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull17" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, -1.74846e-07, 0, 2, 0, 1.74846e-07, 0, -2, -4, 0, -4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull18" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 0, 0, -4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull19" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-2, 0, 1.74846e-07, 0, 2, 0, -1.74846e-07, 0, -2, 4, 0, -4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull20" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, 8, 0, -4)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull21" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8, 0, -8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull22" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -4, 0, -8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull23" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, -8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull24" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 4, 0, -8)
material_override = ExtResource("21_sb1nk")

[node name="FloorFull25" parent="RestRoom/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 8, 0, -8)
material_override = ExtResource("21_sb1nk")

[node name="Light" type="Node3D" parent="RestRoom"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 7.8, 0)

[node name="LampSquareCeilingProp" parent="RestRoom/Light" instance=ExtResource("74_48jwh")]
enabled = false

[node name="SpotLight3D" type="SpotLight3D" parent="RestRoom/Light/LampSquareCeilingProp"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, -0.1, 0)
light_energy = 16.0
light_size = 0.7
light_bake_mode = 1
spot_range = 29.21
spot_attenuation = 0.4
spot_angle = 89.99

[node name="WallDoorwaySign" parent="RestRoom" instance=ExtResource("25_pq8m1")]
transform = Transform3D(-0.272165, 0, -2.37934e-08, 0, 0.276767, 0, 2.37934e-08, 0, -0.272165, 0, 3.59304, -10.2592)
material_override = ExtResource("14_dwcpb")

[node name="Label3D" type="Label3D" parent="RestRoom/WallDoorwaySign"]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 0, 0.104626, 0.0187531)
text = "اتاق استراحت
"
font_size = 208
outline_size = 32

[node name="PathWay" type="Node3D" parent="."]

[node name="Floor" type="Node3D" parent="PathWay"]

[node name="FloorCorner" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, -10.5072)

[node name="FloorCorner3" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, -12, 0, -38.5072)

[node name="FloorCorner4" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -34.5072)

[node name="FloorCorner5" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(-8.74228e-08, 0, -2, 0, 2, 0, 2, 0, -8.74228e-08, 43.9928, 0, -34.5072)

[node name="FloorCorner6" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(-2, 0, 1.74846e-07, 0, 2, 0, -1.74846e-07, 0, -2, 43.9928, 0, -10.5072)

[node name="FloorCorner7" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(-2, 0, 1.74846e-07, 0, 2, 0, -1.74846e-07, 0, -2, 15.9928, 0, 25.4928)

[node name="FloorCorner8" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(1.78814e-07, 0, 2, 0, 2, 0, -2, 0, 1.78814e-07, -12.0072, 0, 25.4928)

[node name="FloorCorner2" parent="PathWay/Floor" instance=ExtResource("69_y07f4")]
transform = Transform3D(-8.74228e-08, 0, 2, 0, 2, 0, -2, 0, -8.74228e-08, -44.0072, 0, 9.49275)

[node name="FloorFull" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12.0072, 0, 21.4928)

[node name="FloorFull2" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4.00725, 0, 21.4928)

[node name="FloorFull3" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -0.00724638, 0, 21.4928)

[node name="FloorFull4" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 3.99275, 0, 21.4928)

[node name="FloorFull5" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 7.99275, 0, 21.4928)

[node name="FloorFull6" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 21.4928)

[node name="FloorFull7" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, 21.4928)

[node name="FloorFull64" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4.00725, 0, 25.4928)

[node name="FloorFull34" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8.00725, 0, 21.4928)

[node name="FloorFull94" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8.00725, 0, 25.4928)

[node name="FloorFull99" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -0.00724638, 0, 25.4928)

[node name="FloorFull101" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 3.99275, 0, 25.4928)

[node name="FloorFull103" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 7.99275, 0, 25.4928)

[node name="FloorFull104" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 25.4928)

[node name="FloorFull8" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, 17.4928)

[node name="FloorFull9" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 17.4928)

[node name="FloorFull10" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 13.4928)

[node name="FloorFull11" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, 13.4928)

[node name="FloorFull12" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, 9.49275)

[node name="FloorFull134" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 19.9928, 0, 9.49275)

[node name="FloorFull13" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 9.49275)

[node name="FloorFull14" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 5.49275)

[node name="FloorFull15" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, 5.49275)

[node name="FloorFull16" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, 1.49275)

[node name="FloorFull17" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, 1.49275)

[node name="FloorFull18" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -2.50725)

[node name="FloorFull19" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -2.50725)

[node name="FloorFull20" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -6.50725)

[node name="FloorFull59" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -2.50725)

[node name="FloorFull61" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -2.50725)

[node name="FloorFull62" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -6.50725)

[node name="FloorFull21" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -6.50725)

[node name="FloorFull65" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 3.99275, 0, -6.50725)

[node name="FloorFull66" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 7.99275, 0, -6.50725)

[node name="FloorFull67" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4.00725, 0, -6.50725)

[node name="FloorFull80" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4.00725, 0, -2.50725)

[node name="FloorFull68" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -0.00724638, 0, -6.50725)

[node name="FloorFull71" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 3.99275, 0, -2.50725)

[node name="FloorFull73" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 7.99275, 0, -2.50725)

[node name="FloorFull79" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -0.00724638, 0, -2.50725)

[node name="FloorFull69" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -4.00725, 0, -10.5072)

[node name="FloorFull70" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -0.00724638, 0, -10.5072)

[node name="FloorFull72" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8.00725, 0, -6.50725)

[node name="FloorFull74" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -8.00725, 0, -10.5072)

[node name="FloorFull75" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16.0072, 0, -6.50725)

[node name="FloorFull76" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12.0072, 0, -6.50725)

[node name="FloorFull77" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16.0072, 0, -10.5072)

[node name="FloorFull81" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -20.0072, 0, -6.50725)

[node name="FloorFull82" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -20.0072, 0, -10.5072)

[node name="FloorFull83" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -24.0072, 0, -6.50725)

[node name="FloorFull84" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -24.0072, 0, -10.5072)

[node name="FloorFull85" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -28.0072, 0, -6.50725)

[node name="FloorFull86" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -28.0072, 0, -10.5072)

[node name="FloorFull87" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -32.0072, 0, -6.50725)

[node name="FloorFull88" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -32.0072, 0, -10.5072)

[node name="FloorFull89" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -36.0072, 0, -6.50725)

[node name="FloorFull90" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -36.0072, 0, -10.5072)

[node name="FloorFull91" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, -6.50725)

[node name="FloorFull92" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, -10.5072)

[node name="FloorFull93" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, -6.50725)

[node name="FloorFull95" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, 1.49275)

[node name="FloorFull96" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, -2.50725)

[node name="FloorFull97" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, 1.49275)

[node name="FloorFull102" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, 5.49275)

[node name="FloorFull105" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, 5.49275)

[node name="FloorFull98" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, -2.50725)

[node name="FloorFull100" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, 9.49275)

[node name="FloorFull78" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12.0072, 0, -10.5072)

[node name="FloorFull106" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -14.5072)

[node name="FloorFull107" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12, 0, -14.5072)

[node name="FloorFull108" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -18.5072)

[node name="FloorFull109" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12, 0, -18.5072)

[node name="FloorFull110" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -22.5072)

[node name="FloorFull111" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12, 0, -22.5072)

[node name="FloorFull112" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -26.5072)

[node name="FloorFull113" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12, 0, -26.5072)

[node name="FloorFull115" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12, 0, -30.5072)

[node name="FloorFull119" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -30.5072)

[node name="FloorFull116" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -34.5072)

[node name="FloorFull117" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -12, 0, -34.5072)

[node name="FloorFull118" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -16, 0, -38.5072)

[node name="FloorFull120" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -24.0072, 0, -34.5072)

[node name="FloorFull121" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -20.0072, 0, -34.5072)

[node name="FloorFull122" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -24.0072, 0, -38.5072)

[node name="FloorFull123" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -20.0072, 0, -38.5072)

[node name="FloorFull124" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -32.0072, 0, -34.5072)

[node name="FloorFull125" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -28.0072, 0, -34.5072)

[node name="FloorFull126" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -32.0072, 0, -38.5072)

[node name="FloorFull127" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -28.0072, 0, -38.5072)

[node name="FloorFull128" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, -34.5072)

[node name="FloorFull129" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -36.0072, 0, -34.5072)

[node name="FloorFull130" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -40.0072, 0, -38.5072)

[node name="FloorFull132" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, -34.5072)

[node name="FloorFull133" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -44.0072, 0, -38.5072)

[node name="FloorFull131" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -36.0072, 0, -38.5072)

[node name="FloorFull22" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -10.5072)

[node name="FloorFull23" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -10.5072)

[node name="FloorFull24" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -14.5072)

[node name="FloorFull25" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -14.5072)

[node name="FloorFull26" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -18.5072)

[node name="FloorFull27" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -18.5072)

[node name="FloorFull28" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -22.5072)

[node name="FloorFull29" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -22.5072)

[node name="FloorFull30" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -26.5072)

[node name="FloorFull31" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -26.5072)

[node name="FloorFull32" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 11.9928, 0, -30.5072)

[node name="FloorFull33" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -30.5072)

[node name="FloorFull35" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 15.9928, 0, -34.5072)

[node name="FloorFull36" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 19.9928, 0, -34.5072)

[node name="FloorFull37" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 23.9928, 0, -34.5072)

[node name="FloorFull38" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 27.9928, 0, -34.5072)

[node name="FloorFull39" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 31.9928, 0, -34.5072)

[node name="FloorFull40" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 35.9928, 0, -34.5072)

[node name="FloorFull41" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -34.5072)

[node name="FloorFull42" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 35.9928, 0, -30.5072)

[node name="FloorFull43" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -30.5072)

[node name="FloorFull49" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 43.9928, 0, -30.5072)

[node name="FloorFull50" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -26.5072)

[node name="FloorFull51" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -22.5072)

[node name="FloorFull52" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 43.9928, 0, -26.5072)

[node name="FloorFull53" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 43.9928, 0, -22.5072)

[node name="FloorFull54" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -18.5072)

[node name="FloorFull55" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -14.5072)

[node name="FloorFull56" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 43.9928, 0, -18.5072)

[node name="FloorFull57" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 43.9928, 0, -14.5072)

[node name="FloorFull58" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 39.9928, 0, -10.5072)

[node name="FloorFull44" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 27.9928, 0, -30.5072)

[node name="FloorFull45" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 31.9928, 0, -30.5072)

[node name="FloorFull46" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 19.9928, 0, -30.5072)

[node name="FloorFull47" parent="PathWay/Floor" instance=ExtResource("20_g2x28")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 23.9928, 0, -30.5072)

[node name="Wall" type="Node3D" parent="PathWay"]

[node name="WallCorner6" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1.39092e-08, 0, -1, 0, 1, 0, 1, 0, -1.39092e-08, -8, 4, -2.5)
material_override = ExtResource("16_5fxuv")

[node name="WallCorner13" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(-7.35136e-08, 0, 1, 0, 1, 0, -1, 0, -7.35136e-08, -44, 4, 9.4)
material_override = ExtResource("22_uocqr")

[node name="WallCorner15" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98022e-08, 0, 1, 0, -2.98022e-08, 0, 1, -44, 4, -10.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner14" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, -1.17225e-07, 0, 1, 0, 1.17225e-07, 0, -1, -40, 4, 9.4)
material_override = ExtResource("22_uocqr")

[node name="WallCorner8" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(-7.35136e-08, 0, 1, 0, 1, 0, -1, 0, -7.35136e-08, -12, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner9" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, -1.17225e-07, 0, 1, 0, 1.17225e-07, 0, -1, 16, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner10" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.60936e-07, 0, -1, 0, 1, 0, 1, 0, 1.60936e-07, 44, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner17" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(-1, 0, -1.17225e-07, 0, 1, 0, 1.17225e-07, 0, -1, 44, 4, -10.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner11" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.04648e-07, 0, 1, 0, -2.04648e-07, 0, 1, 12, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner12" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(1.60936e-07, 0, -1, 0, 1, 0, 1, 0, 1.60936e-07, -12, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, 12, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice2" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -4, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice3" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, 0, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice4" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, 4, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice5" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, 8, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice6" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 16, 0, 21.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice36" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 16, 0, 17.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice37" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 16.0077, 0, -2.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice38" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 16.0077, 0, -6.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice40" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 16.0077, 0, -10.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice8" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 32, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice15" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 36, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice16" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 40, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice33" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, 40, 0, -10.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice34" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.7683e-07, 0, 1, 0, 1.7683e-07, 0, 1, 36, 0, -6.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice9" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 28, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice10" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 24, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice11" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 20, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice12" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, 16, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice13" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.17272e-08, 0, 1, 0, 1, 0, -1, 0, -4.17272e-08, 12, 0, -30.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice35" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.17272e-08, 0, 1, 0, 1, 0, -1, 0, -4.17272e-08, 12, 0, -26.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice14" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 44, 0, -30.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice29" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 44, 0, -14.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice30" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 44, 0, -18.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice31" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 44, 0, -22.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice32" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, 44, 0, -26.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice17" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -24, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice18" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -20, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice19" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -16, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice20" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -28, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice21" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -32, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice22" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -36, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice23" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -40, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice24" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.98419e-09, 0, 1, 0, 1.98419e-09, 0, 1, -44, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornice26" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -44, 0, -34.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice27" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -40, 0, -34.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice28" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -36, 0, -34.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice25" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-4.56956e-08, 0, -1, 0, 1, 0, 1, 0, -4.56956e-08, -12, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -12, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCorner16" parent="PathWay/Wall" instance=ExtResource("17_tot3q")]
transform = Transform3D(1, 0, 2.98022e-08, 0, 1, 0, -2.98022e-08, 0, 1, -12, 4, 21.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice11" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, 21.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice41" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.33118e-07, 0, 1, 0, 1, 0, -1, 0, 1.33118e-07, -44, 0, 5.4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice45" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.33118e-07, 0, 1, 0, 1, 0, -1, 0, 1.33118e-07, -44, 0, -2.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice46" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-2.20541e-07, 0, -1, 0, 1, 0, 1, 0, -2.20541e-07, -40, 0, 5.4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice42" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.33118e-07, 0, 1, 0, 1, 0, -1, 0, 1.33118e-07, -44, 0, -6.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice47" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -16, 0, -6.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice49" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -20, 0, -6.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice48" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -12, 0, -6.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice43" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1.33118e-07, 0, 1, 0, 1, 0, -1, 0, 1.33118e-07, -44, 0, 1.4)
material_override = ExtResource("22_uocqr")

[node name="WallCornice44" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.7683e-07, 0, 1, 0, 1.7683e-07, 0, 1, -36, 0, -10.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice50" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, -1.7683e-07, 0, 1, 0, 1.7683e-07, 0, 1, -40, 0, -10.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice7" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -44, 0, 9.4)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice9" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -40, 0, 9.4)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice10" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -8, 0, -2.5)
material_override = ExtResource("16_5fxuv")

[node name="WallCornerCornice8" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, -10.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice2" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 16, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice3" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, 44, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice6" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 44, 0, -10.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice4" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1, 0, 1.74846e-07, 0, 1, 0, -1.74846e-07, 0, 1, 12, 0, -34.4889)
material_override = ExtResource("22_uocqr")

[node name="WallCornerCornice5" parent="PathWay/Wall" instance=ExtResource("13_ba1l5")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -12, 0, -38.4889)
material_override = ExtResource("22_uocqr")

[node name="Wall12" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -12, 4, -6.5)
material_override = ExtResource("22_uocqr")

[node name="Wall15" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -20, 4, -6.5)
material_override = ExtResource("22_uocqr")

[node name="Wall16" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -16, 4, -6.5)
material_override = ExtResource("22_uocqr")

[node name="Wall26" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, -44, 4, -6.5)
material_override = ExtResource("22_uocqr")

[node name="Wall27" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, -44, 4, 1.4)
material_override = ExtResource("22_uocqr")

[node name="Wall28" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, -44, 4, -2.5)
material_override = ExtResource("22_uocqr")

[node name="Wall62" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -40, 4, 5.4)
material_override = ExtResource("22_uocqr")

[node name="Wall29" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1.31134e-07, 0, 1, 0, 1, 0, -1, 0, 1.31134e-07, -44, 4, 5.4)
material_override = ExtResource("22_uocqr")

[node name="Wall32" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -4, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="Wall33" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 12, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice7" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -8, 0, 25.5)
material_override = ExtResource("22_uocqr")

[node name="Wall59" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -8, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="WallCornice51" parent="PathWay/Wall" instance=ExtResource("12_3u4kp")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 21.5)
material_override = ExtResource("22_uocqr")

[node name="Wall60" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 4, 21.5)
material_override = ExtResource("22_uocqr")

[node name="Wall34" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 4, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="Wall36" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 8, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="Wall37" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16, 4, 21.5)
material_override = ExtResource("22_uocqr")

[node name="Wall38" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16, 4, 17.5)
material_override = ExtResource("22_uocqr")

[node name="Wall39" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16.0077, 4, 1.5)
material_override = ExtResource("22_uocqr")

[node name="Wall40" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16.0077, 4, -2.5)
material_override = ExtResource("22_uocqr")

[node name="Wall41" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16.0077, 4, -6.5)
material_override = ExtResource("22_uocqr")

[node name="Wall43" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 20, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall44" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 16, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall50" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 12, 4, -30.5)
material_override = ExtResource("22_uocqr")

[node name="Wall51" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 12, 4, -26.5)
material_override = ExtResource("22_uocqr")

[node name="Wall45" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 28, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall46" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 24, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall47" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 36, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall49" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 40, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall52" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 44, 4, -30.5)
material_override = ExtResource("22_uocqr")

[node name="Wall53" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 44, 4, -26.5)
material_override = ExtResource("22_uocqr")

[node name="Wall54" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 44, 4, -22.5)
material_override = ExtResource("22_uocqr")

[node name="Wall55" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 44, 4, -18.5)
material_override = ExtResource("22_uocqr")

[node name="Wall56" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 44, 4, -14.5)
material_override = ExtResource("22_uocqr")

[node name="Wall57" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, 36, 4, -6.4889)
material_override = ExtResource("22_uocqr")

[node name="Wall58" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 40, 4, -10.5)
material_override = ExtResource("22_uocqr")

[node name="Wall48" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 7.10543e-15, 0, 1, 0, -7.10543e-15, 0, 1, 32, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall42" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 16.0077, 4, -10.5)
material_override = ExtResource("22_uocqr")

[node name="Wall35" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 0, 4, 25.5)
material_override = ExtResource("22_uocqr")

[node name="Wall30" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -36, 4, -10.5)
material_override = ExtResource("22_uocqr")

[node name="Wall31" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, -1.74846e-07, 0, 1, 0, 1.74846e-07, 0, 1, -40, 4, -10.5)
material_override = ExtResource("22_uocqr")

[node name="Wall13" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall14" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall19" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall20" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall21" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall22" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall23" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -44, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall24" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -40, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall25" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -36, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall17" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Wall61" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -12, 4, -34.5)
material_override = ExtResource("22_uocqr")

[node name="Wall18" parent="PathWay/Wall" instance=ExtResource("14_j712o")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 4, -38.5)
material_override = ExtResource("22_uocqr")

[node name="Roof" type="Node3D" parent="."]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -2, 8, 27.5)
visible = false

[node name="FloorCorner9" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner10" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -5, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner11" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner12" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, 23, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner13" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 23, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner14" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, 9, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner15" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(8.9407e-08, 0, 1, 0, 1, 0, -1, 0, 8.9407e-08, -5, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorCorner16" parent="Roof" instance=ExtResource("69_y07f4")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -21, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull48" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull60" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull63" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull114" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull135" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull136" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull137" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull138" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull139" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -3)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull61" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull64" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull115" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull261" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull262" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull62" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull65" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull116" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull263" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull264" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull66" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull67" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull117" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull265" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull266" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull68" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull69" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull118" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull267" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull268" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull70" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull71" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull119" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull269" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull270" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull92" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull93" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull130" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull291" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull292" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull109" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull110" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull131" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull293" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull294" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull111" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull112" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull132" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull295" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull296" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull113" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull133" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull134" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull297" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull298" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull299" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull300" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull301" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull302" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull303" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull304" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull72" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull73" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull120" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull271" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull272" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull74" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull75" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull121" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull273" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull274" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull76" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull77" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull122" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull275" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull276" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull78" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull79" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull123" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull277" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull278" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull80" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull81" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull124" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull279" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull280" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull140" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull141" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull142" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull143" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull144" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -1)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull145" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull146" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -5)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull147" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull148" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -7)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull149" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull150" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull82" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull83" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull125" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull281" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull282" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull84" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull85" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull126" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull283" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull284" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull86" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull87" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull127" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull285" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull88" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull89" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull128" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull286" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull287" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull90" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull91" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull129" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull288" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull289" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull290" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull151" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull152" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull153" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull154" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull155" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull156" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull157" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull158" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull159" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull160" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull161" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull162" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull163" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull164" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull165" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull166" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull167" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull168" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull169" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull170" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull171" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull172" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull173" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull174" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull175" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull176" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull177" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull178" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull179" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull180" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -11, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull181" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -11, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull182" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull183" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull184" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull185" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull186" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull187" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull188" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull189" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull190" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -17)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull191" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull192" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull193" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull194" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull195" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull196" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull197" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -9)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull198" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull199" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull200" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull201" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull202" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull203" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull204" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull205" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull206" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull207" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull208" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull209" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull210" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull211" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -7, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull212" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -11, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull213" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull214" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -11, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull215" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull216" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull217" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull218" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull219" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull220" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull221" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull222" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull223" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull224" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull225" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull305" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -25, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull306" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull307" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -25, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull308" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull309" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull310" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -27, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull311" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull312" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -31, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull313" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -31, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull314" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -27, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull315" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -25, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull316" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull317" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -25, 0, -35)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull318" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23, 0, -35)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull319" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull320" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -27, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull321" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29, 0, -35)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull322" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -31, 0, -33)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull323" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -31, 0, -35)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull324" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -27, 0, -35)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull226" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull227" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull228" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull229" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull230" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull231" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull232" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull233" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull234" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull235" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull236" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull237" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull238" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull239" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull240" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull241" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull242" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull243" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull244" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -31)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull245" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull246" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull247" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 23, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull248" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull249" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull250" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 23, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull251" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 23, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull252" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull253" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull254" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 23, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull255" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 23, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull256" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21, 0, -19)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull257" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull258" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull259" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull260" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull26" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull27" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull28" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull29" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull30" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -21)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull31" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -17, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull32" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -15, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull33" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -13, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull34" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.9407e-08, 0, 1, 0, -8.9407e-08, 0, -1, -11, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull35" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -9, 0, -23)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull36" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull37" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull38" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull39" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -11, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull40" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -25)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull41" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -17, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull42" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -15, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull43" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -13, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull44" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -11, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull45" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -9, 0, -27)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull46" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull47" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull49" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull50" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull51" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -29)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull94" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull95" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull96" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull97" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -11, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull98" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -11)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull99" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -17, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull100" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -15, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull101" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -13, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull102" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-1, 0, 8.74228e-08, 0, 1, 0, -8.74228e-08, 0, -1, -11, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull103" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -9, 0, -13)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull104" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull105" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -15, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull106" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull107" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -11, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="FloorFull108" parent="Roof" instance=ExtResource("20_g2x28")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9, 0, -15)
material_override = ExtResource("23_cqyyt")

[node name="HaunterSpawn" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.06508, 0.727483, 9.00012)

[node name="1" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 7.2, 0, 8)

[node name="2" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-1, 0, -2.62269e-07, 0, 1, 0, 2.62269e-07, 0, -1, -3.57628e-07, 0, 8)

[node name="3" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -7.4, 0, 8)

[node name="4" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -7.4, 0, 0.6)

[node name="5" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -6.10948e-07, 0, 9.53674e-07)

[node name="6" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 7.2, 0, 0.6)

[node name="7" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 7.2, 0, -7)

[node name="8" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -7.4, 0, -7)

[node name="9" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -7.4, 0, -3.4)

[node name="10" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 7.2, 0, -3.5)

[node name="11" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(1.31134e-07, 0, -1, 0, 1, 0, 1, 0, 1.31134e-07, -7.4, 0, 4.6)

[node name="12" type="Marker3D" parent="HaunterSpawn"]
transform = Transform3D(-4.37112e-08, 0, 1, 0, 1, 0, -1, 0, -4.37112e-08, 7.2, 0, 4.6)
