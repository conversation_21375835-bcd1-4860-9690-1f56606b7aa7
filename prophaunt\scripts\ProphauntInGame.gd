extends ProphauntServerState

var game_update_counter = 0.0
var sync_counter = 0.0
var round_end_timer = 0.0
var round_ended = false
var round_manager: ProphauntRoundManager

var rounds_result = []

func set_state():
	print("Set State to Prophaunt InGame")
	server.state = Constants.ServerState.InGame
	round_ended = false
	round_end_timer = 0.0

	# Initialize round manager
	if not round_manager:
		round_manager = ProphauntRoundManager.new()
		round_manager.name = "RoundManager"
		add_child(round_manager)

		# Connect round manager signals
		round_manager.round_ended.connect(_on_round_ended)
		round_manager.timer_updated.connect(_on_timer_updated)
		round_manager.props_eliminated.connect(_on_props_eliminated)
		round_manager.round_warning.connect(_on_round_warning)

	start_round()


func start_round():
	round_timer = Constants.PROPHAUNT_ROUND_TIME
	round_start_time = Time.get_ticks_msec()
	
	assign_teams()
	for player_id in server.players_data.keys():
		if server.is_bot(player_id):
			continue
		var player:Character = server.players[player_id]
		assign_my_team.rpc_id(player_id, player.prophaunt_player.team)
	

	spawn_prophaunt_players()
	# Start the round
	round_manager.start_round(current_round, server.props_team, server.haunters_team)
	# Notify all players that the round has started
	for key in server.players_data.keys():
		if server.is_bot(key):
			continue
		if server.is_dc(key):
			continue
		prophaunt_round_start.rpc_id(key, current_round, round_manager.get_time_remaining())
	
	if Constants.is_server:
		send_all_players_start_sync()


func spawn_prophaunt_players():
	"""Spawn players in their appropriate locations"""
	var haunter_spawn_index = 0
	
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue
		
		var player:Character = server.players[key]
		if not player:
			continue
		
		if player.prophaunt_player.team == Constants.ProphauntTeam.HAUNTERS:
			# Spawn haunters in haunter room
			var pos = get_haunter_spawn_position(haunter_spawn_index)
			var rot = get_haunter_spawn_rotation(haunter_spawn_index)
			player.global_position = pos
			player.global_rotation = rot
			if not server.is_bot(key):
				player.force_set_pos_rot.rpc_id(key, pos, rot)

			haunter_spawn_index += 1
		else:
			assign_random_prop_disguise(key)
			if not server.is_bot(key):
				player.force_set_pos_rot.rpc_id(key, player.global_position, player.global_rotation)
		
		# Update player data
		server.players_data[key]["server"]["checkpoint"] = player.global_position
		server.players_data[key]["d"]["p"] = player.global_position


func get_haunter_spawn_position(index):
	"""Get spawn position for haunters (in haunter room)"""

	var map: PropHauntMap = Constants.client.game_scene.selected_map
	var count = map.haunter_spawn.get_child_count()
	return map.haunter_spawn.get_child(index % count).global_position


func get_haunter_spawn_rotation(index):
	"""Get spawn rotation for haunters"""
	var map: PropHauntMap = Constants.client.game_scene.selected_map
	var count = map.haunter_spawn.get_child_count()
	return map.haunter_spawn.get_child(index % count).global_rotation


#Server
func run(delta):
	if not server:
		return
	
	if check_for_dc_players():
		#All players exited!!! kill the server
		server.set_state_to_prophaunt_results(rounds_result)
	
	# Update basic server functionality
	server.set_all_player_scenes_data()
	server.update_players_data()
	
	if not round_ended:
		# Update round manager
		if round_manager:
			round_manager.update_round(delta)

		# Update cooldowns and effects
		update_cooldowns(delta)
		
		# Send game updates to clients
		game_update_counter += delta
		if game_update_counter >= 0.1:  # Update 10 times per second
			send_game_update()
			game_update_counter = 0.0
		
		sync_counter += delta
		if sync_counter >= 1.0 / 30.0:
			sync_all_players()  # Send all players data using packed byte arrays
			sync_counter = 0
	else:
		# Handle round end transition
		sync_counter += delta
		if sync_counter >= 1.0 / 30.0:
			sync_all_players()  # Send all players data using packed byte arrays
			sync_counter = 0
		
		round_end_timer += delta
		if round_end_timer >= 5.0:  # 5 seconds to show results
			transition_to_next_round()



func end_round(winner_team):
	"""End the current round"""
	print("Round ended! Winner: ", "Props" if winner_team == Constants.ProphauntTeam.PROPS else "Haunters")
	round_ended = true
	round_end_timer = 0.0
	
	# Calculate scores and stats
	rounds_result.append(calculate_round_results(winner_team))
	
	# Notify all players of round end
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		prophaunt_round_end.rpc_id(key, winner_team)


func calculate_round_results(winner_team):
	"""Calculate round results and player stats"""
	var results = {
		"winner_team": winner_team,
		"round_time": Constants.PROPHAUNT_ROUND_TIME - round_timer,
		"total_props": server.props_team.size(),
		"total_haunters": server.haunters_team.size(),
		"player_stats": {}
	}
	
	# Calculate individual player stats
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue
		
		var player:Character = server.players[key]
		
		var player_stats = {
			"team": player.prophaunt_player.team,
			"survived": player.prophaunt_player.player_state != Constants.ProphauntPlayerState.DEAD,
			"hp_remaining": player.prophaunt_player.get_current_hp(),
			"score": 0
		}
		
		# Award points based on performance
		if player_stats["team"] == Constants.ProphauntTeam.PROPS:
			if player_stats["survived"]:
				player_stats["score"] = 5  # Survival bonus
			else:
				player_stats["score"] = 1   # Participation
		else:  # Haunters
			if winner_team == Constants.ProphauntTeam.HAUNTERS:
				player_stats["score"] = 3   # Win bonus
			else:
				player_stats["score"] = 1   # Participation
		
		results["player_stats"][key] = player_stats
	
	return results


func transition_to_next_round():
	"""Transition to the next round or map"""
	rounds_played_on_map += 1
	current_round += 1
	
	if rounds_played_on_map >= Constants.PROPHAUNT_ROUNDS_PER_MAP:
		# Change map
		rounds_played_on_map = 0
		print("Changing to next map after ", Constants.PROPHAUNT_ROUNDS_PER_MAP, " rounds")
		server.set_state_to_prophaunt_results(rounds_result)
		return
	
	# Reset player states for next round
	reset_player_states()
	
	round_ended = false
	round_end_timer = 0.0
	start_round()


func reset_player_states():
	"""Reset all player states for the next round"""
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue
		
		var player:Character = server.players[key]
		player.prophaunt_player.health_system.current_hp = Constants.PROPHAUNT_PROP_DEFAULT_HP
		player.prophaunt_player.player_state = Constants.ProphauntPlayerState.ALIVE
		player.prophaunt_player.hex_cooldown = 0.0
		player.prophaunt_player.sound_cooldown = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN
		player.enable()
		if is_instance_valid(player.prophaunt_player.disguise_system):
			player.prophaunt_player.disguise_system.remove_disguise()


func send_game_update():
	"""Send game state update to all clients"""
	var game_data = {
		"round_timer": round_timer,
		#"props_alive": props_alive.size(),
		"total_props": server.props_team.size(),
		"current_round": current_round,
		"player_states": {}
	}

	# Send to all players
	var prophaunt_game:PropHauntGame = Constants.client.game_scene
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		prophaunt_game.prophaunt_game_update.rpc_id(key, game_data)


func sync_all_players():
	"""Send all players data to every player using packed byte arrays"""
	if Constants.is_client():
		return
	if not server:
		return

	var all_players_data = PackedByteArray()
	var player_count = 0

	# Count valid players first
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue
		if server.players_data[key]["server"]["eliminated"]:
			continue
		player_count += 1

	# Encode player count first
	all_players_data.resize(1)
	all_players_data.encode_u8(0, player_count)

	# Encode each player's data
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue

		var player_data = server.players_data[key]
		var encoded_player: PackedByteArray
		var player:Character = server.players[key]
		
		if player.prophaunt_player == null:
			continue

		if player.prophaunt_player.team == Constants.ProphauntTeam.PROPS:
			encoded_player = encodeProp(player_data)
		else:
			encoded_player = encodeHaunter(player_data)

		# Append this player's data to the main array
		all_players_data.append_array(encoded_player)

	# Send to all players
	var prophaunt_game:PropHauntGame = Constants.client.game_scene
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		prophaunt_game.sync_all_players_data.rpc_id(key, all_players_data)


func encodeProp(player_data: Dictionary) -> PackedByteArray:
	"""Encode prop player data to PackedByteArray"""
	var player:Character = Constants.server.players[player_data["id"]]
	var buf = PackedByteArray()
	buf.resize(Constants.PROPHAUNT_SYNC_PROP_SIZE)

	# Player ID (4 bytes)
	buf.encode_u32(0, player_data["id"])

	# Team (1 byte) - 0 for PROPS
	buf.encode_u8(4, Constants.ProphauntTeam.PROPS)

	# Position (6 bytes - 2 bytes per axis)
	buf.encode_half(5, player.global_position.x)
	buf.encode_half(7, player.global_position.y)
	buf.encode_half(9, player.global_position.z)

	# Rotation (6 bytes - 2 bytes per axis)
	buf.encode_half(11, player.global_rotation.x)
	buf.encode_half(13, player.global_rotation.y)
	buf.encode_half(15, player.global_rotation.z)

	# Velocity (6 bytes - 2 bytes per axis)
	buf.encode_half(17, player.velocity.x)
	buf.encode_half(19, player.velocity.y)
	buf.encode_half(21, player.velocity.z)

	# Animation (1 byte)
	buf.encode_u8(23, player_data["d"]["a"])

	# Prop ID/disguise (1 bytes)
	buf.encode_u8(24, player.prophaunt_player.disguise_system.current_disguise.index)

	# Player state (1 byte)
	buf.encode_u8(25, player.prophaunt_player.player_state)

	# HP (2 bytes)
	buf.encode_u8(26, player.prophaunt_player.get_current_hp())

	#State
	buf.encode_u8(27, player_data["d"]["s"])
	
	# HEX COOLDOWN (2 bytes)
	buf.encode_half(28, player.prophaunt_player.hex_cooldown)
	
	buf.encode_u8(30, player.prophaunt_player.is_stealth)
	return buf


func encodeHaunter(player_data: Dictionary) -> PackedByteArray:
	"""Encode haunter player data to PackedByteArray"""
	var player:Character = Constants.server.players[player_data["id"]]
	var buf = PackedByteArray()
	buf.resize(Constants.PROPHAUNT_SYNC_HAUNTER_SIZE)

	# Player ID (4 bytes)
	buf.encode_u32(0, player_data["id"])

	# Team (1 byte) - 1 for HAUNTERS
	buf.encode_u8(4, Constants.ProphauntTeam.HAUNTERS)

	# Position (6 bytes - 2 bytes per axis)
	buf.encode_half(5, player.global_position.x)
	buf.encode_half(7, player.global_position.y)
	buf.encode_half(9, player.global_position.z)

	# Rotation (6 bytes - 2 bytes per axis)
	buf.encode_half(11, player.global_rotation.x)
	buf.encode_half(13, player.global_rotation.y)
	buf.encode_half(15, player.global_rotation.z)

	# Velocity (6 bytes - 2 bytes per axis)
	buf.encode_half(17, player.velocity.x)
	buf.encode_half(19, player.velocity.y)
	buf.encode_half(21, player.velocity.z)

	# Animation (1 byte)
	buf.encode_u8(23, player_data["d"]["a"])

	# Player state (1 byte)
	buf.encode_u8(24, player.prophaunt_player.player_state)

	# Hex cooldown (4 bytes - float as u32)
	buf.encode_u8(25, player.prophaunt_player.get_current_hp())

	#State
	buf.encode_u8(26, player_data["d"]["s"])
	
	#State
	if player_data.has("prophaunt_current_selected_weapon"):
		buf.encode_u8(27, player_data["prophaunt_current_selected_weapon"]["id"])
	else:
		buf.encode_u8(27, 255)
	return buf


######################################Server RPCs
# RPC handlers for Prophaunt actions
@rpc("any_peer", "call_remote", "reliable")
func prophaunt_shoot(start: Vector3, end: Vector3):
	var shooter_id = multiplayer.get_remote_sender_id()
	
	var player:Character = server.players[shooter_id]
	# Verify shooter is a haunter and not hexed
	if player.prophaunt_player.team != Constants.ProphauntTeam.HAUNTERS:
		return
	if player.prophaunt_player.player_state == Constants.ProphauntPlayerState.HEXED:
		return
	if player.prophaunt_player.player_state == Constants.ProphauntPlayerState.DEAD:
		return
	
	#SoundManager.play_3d_sound(end, 50, SoundManager.SOUND_TYPE.Pistol)
	ProphauntAudioSystem.play_3d_sound.rpc(player.global_position, SoundManager.SOUND_TYPE.Pistol, 50)

	var worldspace = get_viewport().get_world_3d().direct_space_state

	# Use intersect_shape with a box for better hit detection
	var param = PhysicsShapeQueryParameters3D.new()
	var box = BoxShape3D.new()

	# Calculate box dimensions and position
	var shoot_direction = (end - start).normalized()
	var shoot_distance = start.distance_to(end)

	# Set box size: small width/height, length matches shoot distance
	box.size = Vector3(0.5, 0.5, shoot_distance)
	param.shape = box

	# Position the box at the midpoint between start and end
	var box_center = start + shoot_direction * (shoot_distance * 0.5)

	# Create transform that aligns the box with the shoot direction
	var transform = Transform3D()
	transform.origin = box_center

	# Align the box's Z-axis with the shoot direction
	if shoot_direction != Vector3.ZERO:
		var up = Vector3.UP
		# If shoot direction is too close to up vector, use forward as up
		if abs(shoot_direction.dot(Vector3.UP)) > 0.9:
			up = Vector3.FORWARD
		transform.basis = Basis.looking_at(shoot_direction, up)

	param.transform = transform
	param.collision_mask = 0x3  # Same collision mask as the original ray

	# Perform shape intersection
	var results = worldspace.intersect_shape(param, 3)  # Max 10 results

	# Find the closest valid hit along the shoot direction
	var result = {}
	var closest_distance = INF

	for hit in results:
		if hit.has("collider"):
			# Calculate distance along the shoot direction from start point
			var to_hit = hit.collider.global_position - start
			var distance_along_ray = to_hit.dot(shoot_direction)
			print(hit.collider)

			# Only consider hits that are in front of the shooter and closer than previous hits
			if distance_along_ray > 0 and distance_along_ray < closest_distance:
				closest_distance = distance_along_ray
				result = hit
	
	if result.has("collider"):
		var collider = result["collider"]
		
		#Real attack
		if collider is Character:
			if collider.prophaunt_player.team == Constants.ProphauntTeam.PROPS:
				var is_kill = damage_prop(collider.player_id, Constants.PROPHAUNT_GUN_DAMAGE)
				if is_kill:
					player.prophaunt_player.total_kill_count += 1
					Constants.client.game_scene.prophaunt_player_eliminated.rpc(collider.player_id)
		
		#Fake attack
		if collider.get_parent().is_in_group("PropObject"):
			if collider.get_parent().enabled:
				var is_kill = player.prophaunt_player.health_system.take_damage(Constants.PROPHAUNT_SELF_DAMAGE)
				if is_kill:
					Constants.client.game_scene.prophaunt_player_eliminated.rpc(shooter_id)


@rpc("any_peer", "call_remote", "reliable")
func prophaunt_grenade(position: Vector3, direction: Vector3):
	var thrower_id = multiplayer.get_remote_sender_id()
	
	var player:Character = server.players[thrower_id]
	# Verify thrower is a haunter and not hexed
	if player.prophaunt_player.team != Constants.ProphauntTeam.HAUNTERS:
		return
	if player.prophaunt_player.player_state == Constants.ProphauntPlayerState.HEXED:
		return
	
	if server.players_data[thrower_id]["prophaunt_grenades"] <= 0:
		return
	
	server.players_data[thrower_id]["prophaunt_grenades"] -= 1
	var backend_id = server.players_data[thrower_id]["backend_id"]
	var grenades = server.players_data[thrower_id]["prophaunt_grenades"]
	var hexes = server.players_data[thrower_id]["prophaunt_hexes"]
	#BackendManager.send_prophaunt_update_item_data_request(backend_id, grenades, hexes)
	BackendManager.queue_prophaunt_item_data_update_request(backend_id, grenades, hexes)
	
	var grenade = SpawnManager.call_spawn_grenade(position, direction, 25)
	grenade.explode.connect(on_grenade_explode)


func on_grenade_explode(center_position:Vector3):
	 #Damage all props in radius
	for key in server.players:
		var player:Character = server.players[key]
		if player.prophaunt_player.team == Constants.ProphauntTeam.PROPS:
			var prop_position = player.global_position
			var distance = center_position.distance_to(prop_position)
		
			if distance <= Constants.PROPHAUNT_GRENADE_RADIUS:
				damage_prop(key, Constants.PROPHAUNT_GRENADE_DAMAGE)


@rpc("any_peer", "call_remote", "reliable")
func prophaunt_hex():
	var caster_id = multiplayer.get_remote_sender_id()
	
	var player:Character = server.players[caster_id]
	# Verify caster is a prop and hex is off cooldown
	if player.prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return
	if player.prophaunt_player.hex_cooldown > 0:
		return
	
	server.players_data[caster_id]["prophaunt_hexes"] -= 1
	var backend_id = server.players_data[caster_id]["backend_id"]
	var grenades = server.players_data[caster_id]["prophaunt_grenades"]
	var hexes = server.players_data[caster_id]["prophaunt_hexes"]
	#BackendManager.send_prophaunt_update_item_data_request(backend_id, grenades, hexes)
	BackendManager.queue_prophaunt_item_data_update_request(backend_id, grenades, hexes)
	
	player.prophaunt_player.hex_cooldown = Constants.PROPHAUNT_HEX_COOLDOWN
	
	# Find haunters in range
	var caster_position = player.global_position
	for haunter_id in server.haunters_team:
		var haunter_position = server.players[haunter_id].global_position
		var distance = caster_position.distance_to(haunter_position)
		
		if distance <= 6.0:  # Hex range
			hex_haunter(haunter_id)
			if not server.is_bot(haunter_id) and not server.is_dc(haunter_id):
				hex_me.rpc_id(haunter_id)
			print("haunter should hex : ", haunter_id)
	
	# Set cooldown
	player.prophaunt_player.hex_cooldown = Constants.PROPHAUNT_HEX_COOLDOWN


@rpc("any_peer", "call_remote", "reliable")
func prophaunt_change_disguise(propId: int):
	var player_id = multiplayer.get_remote_sender_id()
	
	var player:Character = server.players[player_id]
	# Verify player is a prop
	if player.prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return
	
	player.prophaunt_player.disguise_system.apply_disguise_by_index(propId)


######################################Client RPCs
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func prophaunt_round_start(round_index, time):
	for key in Constants.client.remote_players_scenes.keys():
		var p:Character = Constants.client.remote_players_scenes[key]
		if is_instance_valid(p.prophaunt_player.disguise_system):
			p.prophaunt_player.disguise_system.remove_disguise()
		p.prophaunt_player.queue_free()
		p.prophaunt_player = null
	Constants.hide_mouse()
	Constants.client.game_scene.prophaunt_round_start(round_index, time)
	Constants.server.state = Constants.ServerState.InGame


#Client
@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func prophaunt_round_end(winner_team):
	Constants.client.game_scene.prophaunt_round_end(winner_team)


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func assign_my_team(team: Constants.ProphauntTeam):
	var my_player = Constants.client.game_scene.player
	#print(team, "my team is : ", "Haunter" if team == Constants.ProphauntTeam.HAUNTERS else "Prop")
	ProphauntPlayerManager.get_instance().initialize_prophaunt_player(multiplayer.get_unique_id(), my_player, team)
	Constants.client.game_scene.set_my_team(team)


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func hex_me():
	var my_player:Character = Constants.client.game_scene.player
	my_player.hexState.start_state(false)
	#print("hex me!")


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func unhex_me():
	var my_player:Character = Constants.client.game_scene.player
	my_player.hexState.end_state()
	#print("unhex me!")


@rpc("reliable", "authority", "call_remote", Constants.CHANNEL_MINIGAMES)
func player_died():
	var my_player:Character = Constants.client.game_scene.player
	my_player.hexState.start_state(false)


func find_player_at_position(position: Vector3):
	"""Find player at or near the given position"""
	var closest_player = -1
	var closest_distance = 2.0  # Maximum hit distance

	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue

		var player_position = server.players[key].global_position
		var distance = position.distance_to(player_position)

		if distance < closest_distance:
			closest_distance = distance
			closest_player = key

	return closest_player


#Server
# Round manager signal handlers
func _on_round_ended(winner: Constants.ProphauntTeam, _stats: Dictionary):
	"""Called when round ends"""
	#print("INGAME: round ended: ", winner, " ", stats)
	end_round(winner)


func _on_timer_updated(time_remaining: float):
	"""Called when timer updates"""
	round_timer = time_remaining


func _on_props_eliminated(eliminated_count: int, total_count: int):
	"""Called when props are eliminated"""
	print("Props eliminated: ", total_count - eliminated_count, "/", total_count, " remaining")


func _on_round_warning(warning_type: String, time_remaining: float):
	"""Called for round time warnings"""
	#print("Round warning: ", warning_type, " - ", time_remaining, " seconds remaining")

	# Notify all players of the warning
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			Constants.client.game_scene.prophaunt_round_warning.rpc_id(key, warning_type, time_remaining)


func assign_random_prop_disguise(player_id):
	"""Assign a random prop disguise to a prop player"""
	var character:Character = server.players[player_id]
	var initial_disguise = character.prophaunt_player.disguise_system.get_random_disguise()
	character.prophaunt_player.disguise_system.apply_disguise(initial_disguise)
	character.global_position = initial_disguise.global_position
	character.global_rotation = initial_disguise.global_rotation


func on_my_rifle_shoot(start, end):
	var prophaunt_game:PropHauntGame = Constants.client.game_scene
	prophaunt_game.player.prophaunt_player.rifle_ammo -= 1
	prophaunt_game.touch_controller.update_rifle_ammo(prophaunt_game.player.prophaunt_player.rifle_ammo)
	prophaunt_shoot.rpc_id(1, start, end)


func on_my_grenade_shoot(start_pos, dir):
	prophaunt_grenade.rpc_id(1, start_pos, dir)
	Selector.my_prophaunt_item_data["grenades"] -= 1
	Selector.my_prophaunt_item_data["grenades"] = max(0, Selector.my_prophaunt_item_data["grenades"])
	var prophaunt_game:PropHauntGame = Constants.client.game_scene
	prophaunt_game.touch_controller.update_grenade_ammo()


func send_all_players_start_sync():
	if round_manager.current_round > 1:
		return
	var all_players_data = PackedByteArray()
	var player_count = 0

	# Count valid players first
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue
		player_count += 1

	# Encode player count first
	all_players_data.resize(1)
	all_players_data.encode_u8(0, player_count)

	# Encode each player's data
	for key in server.players_data.keys():
		if server.is_dc(key):
			continue

		var player_data = server.players_data[key]
		var encoded_player: PackedByteArray

		encoded_player = encode_player_data_general(player_data)
		# Append this player's data to the main array
		all_players_data.append_array(encoded_player)

	# Send to all players
	var prophaunt_game:PropHauntGame = Constants.client.game_scene
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		prophaunt_game.sync_all_players_start_data.rpc_id(key, all_players_data)


func encode_player_data_general(player_data: Dictionary):
	var buf:PackedByteArray = PackedByteArray()
	buf.resize(Constants.PROPHAUNT_SYNC_START_SIZE)
	
	buf.encode_u32(0, player_data["id"]) #4bytes #Key
	buf.encode_var(4, player_data["selection"]["name"])#55bytes
	var char_path = player_data["selection"]["character"]
	buf.encode_u16(59, Selector.find_id_from_character_path(char_path))#2Bytes
	
	return buf


#Server
func check_for_dc_players():
	if Constants.is_client():
		return
		
	for p in Constants.server.players.keys():
		if server.is_bot(p):
			continue
		if not Constants.server.is_dc(p):
			return false
	
	return true
