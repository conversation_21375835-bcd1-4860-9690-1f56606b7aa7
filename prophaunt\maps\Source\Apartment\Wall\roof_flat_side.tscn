[gd_scene load_steps=6 format=4 uid="uid://epw214kv0m4y"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_wd3f5"]

[sub_resource type="ArrayMesh" id="ArrayMesh_ckx0k"]
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.4, 0.8, 4),
"format": 34359742465,
"index_count": 138,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABAAAAAIAAgAFAAQAAQAGAAcABwADAAEABwAGAAgACAAJAAcACgAJAAgACAALAAoADQAMAAQABAAFAA0ADQAOAA8ADwAMAA0AEQAQAA8ADwAOABEAEgAQABEAEQATABIABAAMAA8ADwAQAAQAEAASAAQAEgAAAAQAEgALAAAACwABAAAACwAIAAEACAAGAAEABwAJAAoAAwAHAAoAAgADAAoACgATAAIABQACABMABQATABEADQAFABEAEQAOAA0ACgALABIAEgATAAoAFgAUABUAFQAXABYAFQAUABgAGAAZABUAGgAZABgAGAAbABoAFQAZABoAGgAXABUAGgAbABYAFgAXABoA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 28,
"vertex_data": PackedByteArray("MjOzP5qZmT4AAADAMjOzP83MTD8AAADAMjOzP5qZmT4AAABAMjOzP83MTD8AAABA//9/P5qZmT4AAADA//9/P5qZmT4AAABAmZkZQM3MTD8AAADAmZkZQM3MTD8AAABAmZkZQJqZGT8AAADAmZkZQJqZGT8AAABAzMwMQJqZGT8AAABAzMwMQJqZGT8AAADA//9/PwAAAAAAAADA//9/PwAAAAAAAABAdvMJQAAAAAAAAABAdvMJQAAAAAAAAADAdvMJQM3MTD4AAADAdvMJQM3MTD4AAABAAAAAQJqZmT4AAADAAAAAQJqZmT4AAABAAACAPwAAAAAAAABAAAAAwAAAAAAAAABAAACAP83MTD4AAABAAAAAwM3MTD4AAABAAACAPwAAAAAAAADAAAAAwAAAAAAAAADAAAAAwM3MTD4AAADAAACAP83MTD4AAADA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_nfst7"]
resource_name = "RoofFlatSide_roof-flat-side"
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.4, 0.8, 4),
"attribute_data": PackedByteArray("nzOlPjoscT+fM6U+viJwP58zpT46LHE/nzOlPr4icD+fM6U+OixxP58zpT46LHE/nzOlPjoscT+fM6U+OixxP58zpT6+InA/nzOlPr4icD+fM6U+viJwP58zpT6+InA/nzOlPr4icD+fM6U+8IxwP58zpT6+InA/nzOlPvCMcD+fM6U+8IxwP58zpT7wjHA/nzOlPvCMcD+fM6U+8IxwP58zpT6Dy3E/nzOlPjoscT+fM6U+g8txP58zpT46LHE/nzOlPoPLcT+fM6U+g8txP58zpT6Dy3E/nzOlPoPLcT+fM6U+UmFxP58zpT6Dy3E/nzOlPlJhcT+fM6U+g8txP58zpT5SYXE/nzOlPlJhcT+fM6U+OixxP58zpT46LHE/nzOlPoPLcT+fM6U+g8txP58zpT46LHE/nzOlPlJhcT+fM6U+OixxP58zpT46LHE/nzOlPvCMcD+fM6U+viJwP58zpT7wjHA/nzOlPr4icD+fM6U+8IxwP58zpT7wjHA/nzOlPr4icD+fM6U+viJwP58zpT46LHE/nzOlPjoscT+fM6U+OixxP58zpT5SYXE/nzOlPoPLcT+fM6U+g8txP58zpT7wjHA/nzOlPjoscT+fM6U+8IxwP58zpT46LHE/nzOlPlJhcT+fM6U+UmFxP58zpT7wjHA/nzOlPvCMcD+fM6U+UmFxP58zpT5SYXE/nzOlPlJhcT+fM6U+UmFxP58zpT5SYXE/nzOlPlJhcT+fM6U+8IxwP58zpT7wjHA/nzOlPlJhcT+fM6U+8IxwP58zpT5SYXE/nzOlPvCMcD8syaQ+hGhtPyzJpD6EaG0/LMmkPoRobT8syaQ+hGhtPw=="),
"format": 34359742487,
"index_count": 138,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAJwAoACYAKAApACYAKAAqACkAKgArACkAKgAsACsALAAtACsAMAAuAC8AMQAwAC8AMgAxAC8ALwAzADIANAAyADMANAAzADUANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4A"),
"material": ExtResource("1_wd3f5"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 80,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_ckx0k")

[sub_resource type="BoxShape3D" id="BoxShape3D_noq73"]
size = Vector3(4.15814, 0.211853, 3.9812)

[sub_resource type="BoxShape3D" id="BoxShape3D_fop18"]
size = Vector3(1.00613, 0.801353, 3.9812)

[node name="RoofFlatSide" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_nfst7")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0751038, 0.0953674, -0.0057373)
shape = SubResource("BoxShape3D_noq73")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.89666, 0.415104, -0.0057373)
shape = SubResource("BoxShape3D_fop18")
