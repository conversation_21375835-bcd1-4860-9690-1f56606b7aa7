[gd_scene load_steps=5 format=4 uid="uid://bllyomds38rd8"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_jqan0"]

[sub_resource type="ArrayMesh" id="ArrayMesh_b6kp4"]
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.25, 4.8, 4.25),
"format": 34359742465,
"index_count": 180,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACQAIAAYABgAHAAkACQADAAEAAQAIAAkADAAKAAsACwANAAwADAANAA4ADgAPAAwAEgAQABEAEQATABIAFAATABEAEQAVABQAFgAUABUAFQAXABYAGAAWABcAFwAZABgAGAAZABoAGgAbABgAHAAbABoAGgAdABwAHgAcAB0AHQAfAB4AGQAXABUAFQARABkAEQAgABkAEAAgABEAIAAaABkAIAAfABoAHwAdABoAAQAAABIAEgATAAEAGAABABMAGAAIAAEAGAAGAAgAFgAYABMAEwAUABYAGAAbAAYABgAbAB4AGwAcAB4AHgAEAAYAAgADAAkACQAhAAIACQAHACEABwAFACEAIgAKAAwADAAjACIADAAPACMADwAkACMAJgAlACIAIgAjACYAJAAnACYAJgAjACQADgAnACQAJAAPAA4AJQALAAoACgAiACUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("////P83MTD8yM9M/mZkJQM3MTD8yM9M/////P5qZmUAyM9M/mZkJQJqZmUAyM9M/MjPTP83MTD////8/MjPTP5qZmUD///8/MjPTP83MTD+ZmQlAMjPTP5qZmUCZmQlAmZkJQM3MTD+ZmQlAmZkJQJqZmUCZmQlAAAAAQJqZmUAAAADAAAAAQAAAAAAAAADAAAAAQJqZmUAAAABAAAAAQAAAAAAAAABAAAAAwAAAAAAAAABAAAAAwJqZmUAAAABA////P/Xc0CZmZsY/mZkJQPXc0CZmZsY/////P83MTD9mZsY/mZkJQM3MTD9mZsY/AAAQQM3MTD8yM9M/AAAQQPXc0CYyM9M/AAAQQM3MTD+ZmQlAAAAQQPXc0CaZmQlAmZkJQM3MTD8AABBAmZkJQPXc0CYAABBAMjPTP/Xc0CYAABBAMjPTP83MTD8AABBAZmbGP83MTD+ZmQlAZmbGP/Xc0CaZmQlAZmbGP83MTD////8/ZmbGP/Xc0Cb///8/////P/Xc0Cb///8/////P5qZmUD///8/ZmbmP5qZmUAAAADAZmbmP5qZmUBmZuY/AAAAwJqZmUBmZuY/ZmbmP6kT0CMAAADAZmbmP6kT0CNmZuY/AAAAwKkT0CNmZuY/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_6omcs"]
resource_name = "WallCornerColumnBottom_wall-corner-column-bottom"
_surfaces = [{
"aabb": AABB(-2, 0, -2, 4.25, 4.8, 4.25),
"attribute_data": PackedByteArray("famkPiqecD99qaQ+Kp5wP32ppD4qnnA/famkPiqecD99qaQ+Kp5wP32ppD4qnnA/famkPiqecD99qaQ+Kp5wP32ppD4qnnA/famkPiqecD99qaQ+Kp5wP32ppD4qnnA/famkPiqecD99qaQ+Kp5wP32ppD4qnnA/famkPiqecD+kKqU+KH9sP6QqpT7Om20/pCqlPih/bD+kKqU+zpttP6QqpT7Om20/pCqlPs6bbT+kKqU+KH9sP6QqpT4of2w/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP6BTpT40MnE/oFOlPjQycT+gU6U+NDJxP9d1pT5q7nA/13WlPmrucD/XdaU+au5wP9d1pT5q7nA/13WlPmrucD/XdaU+au5wP2yopD6DGHE/bKikPoMYcT9sqKQ+gxhxP2yopD6DGHE/bKikPoMYcT9sqKQ+gxhxPwz3pD5Mk20/DPekPq6QbD8M96Q+TJNtPwz3pD6ukGw/DPekPkyTbT8M96Q+TJNtPwz3pD6ukGw/DPekPq6QbD8/GKU+0sRxPz8YpT5SSHA/PxilPtLEcT8/GKU+UkhwPz8YpT7SxHE/PxilPlJIcD8/GKU+0sRxPz8YpT5SSHA/"),
"format": 34359742487,
"index_count": 180,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHAAbABkAGQAdABwAHgAcAB0AHQAfAB4AIAAeAB8AHwAhACAAIAAhACIAIgAjACAAJAAjACIAIgAlACQAJgAkACUAJQAnACYAKgAoACkAKQArACoAKwAsACoALQAsACsALAAuACoALAAvAC4ALwAwAC4AMwAxADIAMgA0ADMANQAzADQANQA2ADMANQA3ADYAOAA1ADQANAA5ADgANQA6ADcANwA6ADsAOgA8ADsAOwA9ADcAQAA+AD8APwBBAEAAPwBCAEEAQgBDAEEARgBEAEUARQBHAEYARQBIAEcASABJAEcATABKAEsASwBNAEwAUABOAE8ATwBRAFAAVABSAFMAUwBVAFQAWABWAFcAVwBZAFgA"),
"material": ExtResource("1_jqan0"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 90,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_b6kp4")

[sub_resource type="BoxShape3D" id="BoxShape3D_b1371"]
size = Vector3(0.4, 4.8, 4.2)

[node name="wall-corner-column-bottom" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_6omcs")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.9, 2.4, 0.1)
shape = SubResource("BoxShape3D_b1371")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0.0999999, 2.4, 1.9)
shape = SubResource("BoxShape3D_b1371")
