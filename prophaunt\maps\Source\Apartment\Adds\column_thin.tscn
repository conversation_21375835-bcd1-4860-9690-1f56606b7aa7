[gd_scene load_steps=4 format=4 uid="uid://bp2iuo7rb5epi"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_3aepb"]

[sub_resource type="ArrayMesh" id="ArrayMesh_8lok7"]
_surfaces = [{
"aabb": AABB(-0.4, 0, -0.4, 0.8, 4.8, 0.8),
"format": 34359742465,
"index_count": 114,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIAAwAFAAQABQAGAAQABQAHAAYACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADQAPAA4ACwACAAQAAAACAAsABAANAAsACwAJAAAADwANAAQAAQAAAAkABAAGAA8ACQAIAAEADgAPAAYABgAHAA4AAwABAAgACAAKAAMADAAOAAcAAwAKAAwABwAFAAwADAAFAAMAEgAQABEAEQATABIAFgAUABUAFQAXABYAEQAQABUAFQAUABEAFgAXABIAEgATABYAEQAUABYAFgATABEA"),
"lods": [0.240315, PackedByteArray("AwAEAAAAAwAFAAQABQAGAAQACwAMAAgACwANAAwACwAAAAQABAANAAsABAAGAA0ABgAFAA0ADAANAAUAAwAAAAsACwAIAAMAAwAIAAwADAAFAAMAEgAQABEAEQATABIAFgAUABUAFQAXABYAEQAQABUAFQAUABEAFgAXABIAEgATABYAEQAUABYAFgATABEA"), 0.510302, PackedByteArray("AwAEAAwABAADAAwAEgAQABEAEQATABIAFgAUABUAFQAXABYAEQAQABUAFQAUABEAFgAXABIAEgATABYAEQAUABYAFgATABEA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 24,
"vertex_data": PackedByteArray("zczMPs3MTD+amZm+zczMPs3MTD+amZk+mpmZPs3MTD/NzMy+mpmZPs3MTD/NzMw+mpmZvs3MTD/NzMy+mpmZvs3MTD/NzMw+zczMvs3MTD+amZm+zczMvs3MTD+amZk+zczMPgAAAACamZk+zczMPgAAAACamZm+mpmZPgAAAADNzMw+mpmZPgAAAADNzMy+mpmZvgAAAADNzMw+mpmZvgAAAADNzMy+zczMvgAAAACamZk+zczMvgAAAACamZm+mpmZvs3MTD+amZm+mpmZvpqZmUCamZm+mpmZvs3MTD+amZk+mpmZvpqZmUCamZk+mpmZPpqZmUCamZm+mpmZPs3MTD+amZm+mpmZPpqZmUCamZk+mpmZPs3MTD+amZk+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_rx4kc"]
resource_name = "ColumnThin_column-thin"
_surfaces = [{
"aabb": AABB(-0.4, 0, -0.4, 0.8, 4.8, 0.8),
"attribute_data": PackedByteArray("UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpPyYrnT6/r3A/JiudPr+vcD8mK50+v69wPyYrnT6/r3A/JiudPr+vcD8mK50+v69wPyYrnT6/r3A/JiudPr+vcD8mK50+v69wPyYrnT6/r3A/JiudPr+vcD8mK50+v69wPyYrnT6/r3A/JiudPr+vcD8mK50+v69wPyYrnT6/r3A/JiudPr+vcD8mK50+v69wPyYrnT6/r3A/JiudPr+vcD9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/UAzZPiZgaT9QDNk+JmBpP1AM2T4mYGk/"),
"format": 34359742487,
"index_count": 114,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIAAwAFAAQABQAGAAQABQAHAAYACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADQAPAA4AEgAQABEAEwAQABIAEQAUABIAEgAVABMAFgAUABEAFwATABUAEQAYABYAFQAZABcAGgAWABgAGAAbABoAHAAXABkAGQAdABwAHgAaABsAHAAdAB4AGwAfAB4AHgAfABwAIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIA"),
"lods": [0.240315, PackedByteArray("AwAEAAAAAwAFAAQABQAGAAQACwAMAAgACwANAAwAOwA9ADoAOgAUADsAOgBAABQAQABHAD8ARQA/AEcAQwA+ADwAPABBAEMARABCAEYARgAfAEQAIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIA"), 0.510302, PackedByteArray("NgA0ADgANQA3ADkAIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIA")],
"material": ExtResource("1_3aepb"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 72,
"vertex_data": PackedByteArray("zczMPs3MTD+amZm+zczMPs3MTD+amZk+mpmZPs3MTD/NzMy+mpmZPs3MTD/NzMw+mpmZvs3MTD/NzMy+mpmZvs3MTD/NzMw+zczMvs3MTD+amZm+zczMvs3MTD+amZk+zczMPgAAAACamZk+zczMPgAAAACamZm+mpmZPgAAAADNzMw+mpmZPgAAAADNzMy+mpmZvgAAAADNzMw+mpmZvgAAAADNzMy+zczMvgAAAACamZk+zczMvgAAAACamZm+mpmZPs3MTD/NzMy+mpmZvs3MTD/NzMy+mpmZPgAAAADNzMy+zczMPs3MTD+amZm+mpmZvgAAAADNzMy+zczMPgAAAACamZm+zczMvgAAAACamZm+zczMPs3MTD+amZk+zczMvs3MTD+amZm+zczMPgAAAACamZk+zczMvgAAAACamZk+zczMvs3MTD+amZk+mpmZPs3MTD/NzMw+mpmZPgAAAADNzMw+mpmZvgAAAADNzMw+mpmZvs3MTD/NzMw+mpmZvs3MTD+amZm+mpmZvpqZmUCamZm+mpmZvs3MTD+amZk+mpmZvpqZmUCamZk+mpmZPpqZmUCamZm+mpmZPs3MTD+amZm+mpmZPpqZmUCamZk+mpmZPs3MTD+amZk+mpmZvs3MTD+amZm+mpmZPs3MTD+amZm+mpmZvpqZmUCamZm+mpmZPpqZmUCamZm+mpmZPs3MTD+amZk+mpmZvs3MTD+amZk+mpmZPpqZmUCamZk+mpmZvpqZmUCamZk+mpmZPpqZmUCamZm+mpmZPpqZmUCamZk+mpmZvpqZmUCamZm+mpmZvpqZmUCamZk+mpmZvs3MTD/NzMy+mpmZvs3MTD/NzMy+mpmZPs3MTD/NzMw+mpmZPs3MTD/NzMw+mpmZvgAAAADNzMw+mpmZvgAAAADNzMw+mpmZvs3MTD/NzMy+mpmZPgAAAADNzMy+mpmZPgAAAADNzMy+zczMPs3MTD+amZm+zczMPs3MTD+amZm+mpmZvgAAAADNzMy+zczMvs3MTD+amZm+zczMPgAAAACamZk+zczMPgAAAACamZk+mpmZPs3MTD/NzMw+mpmZPs3MTD/NzMw+mpmZvgAAAADNzMw+mpmZvgAAAADNzMw+mpmZvs3MTD/NzMw+/3///////7//f///////v/9///////+//3///////7//f///////v/9///////+//3///////7//f///////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7//fwAA////v/9/AAD///+//38AAP///7///4Ha////vwAAgdr///+///+B2v///7///32l////vwAAgdr///+///99pf///78AAH2l////v4Ha/3////8/AAB9pf///7+B2v9/////P30l/3////8/fSX/f////z99pf9/////P32l/3////8/gVr/f////z+BWv9/////PwAA/3////+/AAD/f////78AAP9/////vwAA/3////+/////f////7////9/////v////3////+/////f////7//////////v/////////+//////////7//////////v/9//3////8//3//f////z//f/9/////P/9//3////8//3///////7//f///////v/9///////+//3///////7/m8T4t////v4ppktX///+/P/eraf///z8Da6e6////P+rR9Dz///8/00xUl////z8AAMzr////v///hPT///+///8Pjv///7///8Th////v///VY////+/AADCh////78AAKmt////v6Dy/3////8/d6j/f////z/q+/9/////P62R/3////8/ryT/f////z+Sf/9/////P+8a/3////8/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_8lok7")

[node name="ColumnThin" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_rx4kc")
skeleton = NodePath("")
