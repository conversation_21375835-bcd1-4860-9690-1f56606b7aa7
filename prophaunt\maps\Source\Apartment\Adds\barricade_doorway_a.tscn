[gd_scene load_steps=5 format=4 uid="uid://clwxhcnjxjdpl"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_e1ow5"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_jvgua"]
resource_name = "colormap"
cull_mode = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_33apy"]
_surfaces = [{
"aabb": AABB(-0.0794361, 0.00266963, -1.0465, 0.125, 2.43364, 2.093),
"format": 34359742465,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYABAABAAAAAAAFAAQAAwABAAQABAAGAAMAAgADAAYABgAHAAIABQAAAAIAAgAHAAUACgAIAAkACQALAAoADgAMAA0ADQAPAA4ADAAJAAgACAANAAwACwAJAAwADAAOAAsACgALAA4ADgAPAAoADQAIAAoACgAPAA0AEgAQABEAEQATABIAEgAUABUAFQAQABIAFgAUABIAEgATABYAEQAXABYAFgATABEAEQAQABUAFQAXABEAGgAYABkAGQAbABoAGgAcAB0AHQAYABoAHgAcABoAGgAbAB4AGQAfAB4AHgAbABkAGQAYAB0AHQAfABkAIgAgACEAIQAjACIAIgAkACUAJQAgACIAJgAkACIAIgAjACYAIQAnACYAJgAjACEAIQAgACUAJQAnACEAKgAoACkAKQArACoAKgAsAC0ALQAoACoALgAsACoAKgArAC4AKQAvAC4ALgArACkAKQAoAC0ALQAvACkA"),
"lods": [0.0518247, PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYABAABAAAAAAAFAAQAAwABAAQABAAGAAMAAgADAAYABgAHAAIABQAAAAIAAgAHAAUACgAIAAkACQALAAoADgAMAA0ADQAPAA4ADAAJAAgACAANAAwACwAJAAwADAAOAAsACgALAA4ADgAPAAoADQAIAAoACgAPAA0AEgAUABUAFgAUABIAEgAXABYAFQAXABIAGgAcAB0AHgAcABoAGgAfAB4AHQAfABoAIgAkACUAJgAkACIAIgAnACYAJQAnACIAKgAsAC0ALgAsACoAKgAvAC4ALQAvACoA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("gKE6PUFyD0DU7IW/gKE6PR4h9j9lEoK/gKE6PYHsG0BnEoI/gKE6Pc+KB0DW7IU/ALQavR4h9j9lEoK/ALQavUFyD0DU7IW/ALQavc+KB0DW7IU/ALQavYHsG0BnEoI/gKE6PXbvBT/U9IG/gKE6Pci+UT6v84W/gKE6PXRdpD6y84U/gKE6PQD1LjvX9IE/ALQavci+UT6v84W/ALQavXbvBT/U9IG/ALQavQD1LjvX9IE/ALQavXRdpD6y84U/YK+ivf43BEAosXK/YK+ivVSNCUAosXK/YK+ivf43BEDRW12/YK+ivVSNCUDRW12/ALQavf43BEDRW12/ALQavf43BEAosXK/ALQavVSNCUDRW12/ALQavVSNCUAosXK/YK+ivajMmz40/W6/YK+ivVh3xj40/W6/YK+ivajMmz7ep1m/YK+ivVh3xj7ep1m/ALQavajMmz7ep1m/ALQavajMmz40/W6/ALQavVh3xj7ep1m/ALQavVh3xj40/W6/YK+ivWD8FT7qiVA/YK+ivcBRaz7qiVA/YK+ivWD8FT5B32U/YK+ivcBRaz5B32U/ALQavWD8FT5B32U/ALQavWD8FT7qiVA/ALQavcBRaz5B32U/ALQavcBRaz7qiVA/YK+ivRO1DkArTFI/YK+ivWkKFEArTFI/YK+ivRO1DkCDoWc/YK+ivWkKFECDoWc/ALQavRO1DkCDoWc/ALQavRO1DkArTFI/ALQavWkKFECDoWc/ALQavWkKFEArTFI/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_eiveu"]
resource_name = "BarricadeDoorwayA_barricade-doorway-a"
_surfaces = [{
"aabb": AABB(-0.0794361, 0.00266963, -1.0465, 0.125, 2.43364, 2.093),
"attribute_data": PackedByteArray("goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+goBOP8bl4j6CgE4/xuXiPoKATj/G5eI+hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/hwOdPoKfcT+HA50+gp9xP4cDnT6Cn3E/"),
"format": 34359742487,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoAbgBsAG0AbQBvAG4AcgBwAHEAcQBzAHIAdgB0AHUAdQB3AHYAegB4AHkAeQB7AHoAfgB8AH0AfQB/AH4A"),
"lods": [0.0518247, PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AhACAAIIAhwCBAIUAhgCJAIgAgwCJAIYAjgCKAIwAkQCLAI8AkACTAJIAjQCTAJAAmACUAJYAmwCVAJkAmgCdAJwAlwCdAJoAogCeAKAApQCfAKMApACnAKYAoQCnAKQA")],
"material": SubResource("StandardMaterial3D_jvgua"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 168,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_33apy")

[node name="BarricadeDoorwayA" type="MeshInstance3D"]
transform = Transform3D(0, 0, -1, 0, 1, 0, 1, 0, 0, 0, 0, 0)
material_override = ExtResource("1_e1ow5")
mesh = SubResource("ArrayMesh_eiveu")
skeleton = NodePath("")
