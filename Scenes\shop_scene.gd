extends Control

@onready var container = $Tab1Scroll/HBoxContainer
@onready var http_request = $HTTPRequest
@onready var loading = $Loading
@onready var tab1_scroll = $Tab1Scroll
@onready var tab2_scroll = $Tab2Scroll
@onready var error = $Error
@onready var info_panel = $InfoPanel
@onready var info_container = $InfoPanel/InfoPanel/InfoScroll/HBoxContainer
@onready var info_scroll = $InfoPanel/InfoPanel/InfoScroll
@onready var coin_shop_card = preload("res://Scenes/ui/shop_card.tscn")
@onready var character_shop_card = preload("res://Scenes/ui/shop_card_character.tscn")
@onready var character_unlocker_popup = $CharacterUnlockerPopup
@onready var verify_http = $VerifyHttp
@onready var shop_item_prize_popup = $ShopItemPrizePopup
@onready var content_creators: Control = $ContentCreators

@onready var tab_1_button = $Tab1Button
@onready var tab_2_button = $Tab2Button
@onready var chest_popup = $ChestPopup
@onready var purchase_verifier_ui: Control = $PurchaseVerifierUi
@onready var restore_button: Control = $RestoreButton



var tabs = []
var selected_tab

var package_price = 0
var package_sku = ""

func _ready():
	init_zarinpal_request()
	tabs = [tab1_scroll, tab2_scroll, content_creators]
	shop_item_prize_popup.visible = false
	info_panel.visible = false
	http_request.request_completed.connect(on_list_request_complete)
	verify_http.request_completed.connect(on_verify_request_complete)
	NativeMarket.purchase_succeed.connect(on_purchase_succeed)
	NativeMarket.purchase_start.connect(on_purchase_start)
	NativeMarket.purchase_finished.connect(on_purchase_finished)
	chest_popup.visible = false
	chest_popup.start_character_unlock.connect(start_character_unlock)
	send_request()
	if Selector.shop_mode == Selector.SHOP_ENUM.Chest:
		_on_tab_1_button_pressed()
	if Selector.shop_mode == Selector.SHOP_ENUM.Coin:
		_on_tab_2_button_pressed()
	
	var is_mob = NativeMarket.market == NativeMarket.MarketType.CafeBazaar or NativeMarket.market == NativeMarket.MarketType.Myket
	restore_button.visible = is_mob


func _on_exit_button_pressed():
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")


func send_request():
	for child in container.get_children():
		child.queue_free()
	hide_all()
	loading.visible = true
	var url = Constants.BACKEND_URL + "/shop/list_shop/"
	var data = {}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	http_request.cancel_request()
	http_request.request(url, headers, HTTPClient.METHOD_POST, json)


var is_google_iap = false
func on_list_request_complete(_result, response_code, _headers, body):
	loading.visible = false
	is_google_iap = false
	if response_code == 200:
		tab1_scroll.visible = true
		#unlock_button.visible = true
		var json = JSON.parse_string(body.get_string_from_utf8())
		gateway = json["gateway_type"]
		
		if gateway == "IAP" and NativeMarket.market == NativeMarket.MarketType.GooglePlay:
			#_on_tab_1_button_pressed()
			#tab_2_button.visible = false
			is_google_iap = true
			_on_tab_2_button_pressed()
			tab_2_button.visible = true
		else:
			_on_tab_2_button_pressed()
			tab_2_button.visible = true
		
		handle_items(json["items"])
		
		#print(json["items"])
		return
	
	error.visible = true


func handle_items(items):
	for child in container.get_children():
		child.queue_free()

	for item in items:
		if DataSaver.has_purchased(item["id"]) and item["one_time"] == true:
			#print("already bought one time item: ", item["id"])
			continue

		var card
		if len(item.get("unlock_characters", [])) > 0:
			card = character_shop_card.instantiate()
			card.show_info.connect(show_info)
		else:
			card = coin_shop_card.instantiate()
		container.add_child(card)
		card.set_data(item, is_google_iap)
		card.pressed.connect(purchase_item)


func hide_all():
	error.visible = false
	tab1_scroll.visible = false
	loading.visible = false


func _on_retry_button_pressed():
	send_request()


func _on_unlock_button_pressed():
	Firebase.logEvent("ShopCharacter", {})
	character_unlocker_popup.show_popup()


func purchase_item(item):
	if gateway == "IPG" or NativeMarket.market == NativeMarket.MarketType.Zarinpal:
		send_ipg_request(item)
		return
	Firebase.logEvent("PurchaseItemTry", {})
	package_price = int(item["price_str"].replace(",", "").replace("Tomans", "").replace(" ", ""))
	package_sku = item.get("sku")
	NativeMarket.purchase_item(item)


var last_item = null
func on_purchase_succeed(item):
	last_item = item
	verify_purchase(item)


func verify_purchase(item):
	print("verifying purchase")
	hide_all()
	loading.visible = true
	var url = Constants.BACKEND_URL + "/shop/verify_purchase/"
	var data = {
		"item_id": item["id"],
		"market": NativeMarket.market_string(),
		"token": item["token"],
	}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	verify_http.cancel_request()
	verify_http.request(url, headers, HTTPClient.METHOD_POST, json)


func on_verify_request_complete(_result, response_code, _headers, body):
	#unlock_button.visible = true
	if response_code == 200:
		loading.visible = false
		tab1_scroll.visible = true
		var json = JSON.parse_string(body.get_string_from_utf8())
		var added_coin = json["coin"] - Selector.my_coin
		Selector.my_coin = json["coin"]
		print("verify success")
		@warning_ignore("integer_division")
		Tenjin.purchase_event(package_sku, package_price / 1000000, "USD")
		for character in json["characters"]:
			Selector.my_characters.append(character)
		
		shop_item_prize_popup.show_popup(added_coin, json["characters"])
		DataSaver.add_purchase_id(json["id"])
		return
	else:
		await Constants.wait_timer(1)
		verify_purchase(last_item)


func on_purchase_start():
	loading.visible = true


func on_purchase_finished(_sku):
	loading.visible = false


func show_info(characters):
	info_panel.visible = true
	for child in info_container.get_children():
		child.queue_free()
	
	for chara in characters:
		var image = TextureRect.new()
		image.expand_mode = TextureRect.EXPAND_IGNORE_SIZE
		image.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		image.texture = chara
		info_container.add_child(image)
		image.custom_minimum_size.x = 157
		image.custom_minimum_size.y = 249
	
	await get_tree().process_frame
	await get_tree().process_frame
	info_scroll.scroll_horizontal = 0


func _on_info_exit_button_pressed():
	info_panel.visible = false


func _on_chest_card_chest_open(tier):
	chest_popup.show_popup(tier)


func _on_chest_card_2_chest_open(tier):
	chest_popup.show_popup(tier)


func _on_chest_card_3_chest_open(tier):
	chest_popup.show_popup(tier)


func _on_chest_card_4_chest_open(tier):
	chest_popup.show_popup(tier)


func select_tab(index):
	for tab in tabs:
		tab.visible = false
	selected_tab = tabs[index]
	selected_tab.visible = true


func _on_tab_1_button_pressed():
	$Tab1Button/Selected.visible = true
	$Tab2Button/Selected.visible = false
	$Tab3Button/Selected.visible = false
	select_tab(1)


func _on_tab_2_button_pressed():
	$Tab1Button/Selected.visible = false
	$Tab2Button/Selected.visible = true
	$Tab3Button/Selected.visible = false
	select_tab(0)


func _on_tab_3_button_pressed():
	$Tab1Button/Selected.visible = false
	$Tab2Button/Selected.visible = false
	$Tab3Button/Selected.visible = true
	select_tab(2)
	content_creators.start()

func start_character_unlock(tier):
	character_unlocker_popup.show_popup(tier)


func _on_rubika_button_pressed() -> void:
	OS.shell_open("https://rubika.ir/animalrush")


var zarinpal_http_request: HTTPRequest
var gateway = "IAP"
func init_zarinpal_request():
	zarinpal_http_request = HTTPRequest.new()
	add_child(zarinpal_http_request)
	zarinpal_http_request.request_completed.connect(on_zarinpal_request)

func send_ipg_request(item):
	loading.visible = true
	var url = Constants.BACKEND_URL + "/shop/ipg/"
	var data = {
		"item_id": item.id
		}
	var json = JSON.stringify(data)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + DataSaver.get_item("JWT", "")]
	zarinpal_http_request.cancel_request()
	zarinpal_http_request.request(url, headers, HTTPClient.METHOD_POST, json)


func on_zarinpal_request(_result, response_code, _headers, body):
	loading.visible = false
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		OS.shell_open(json["url"])
		return
	
	Constants.show_toast(str(response_code))


func _notification(what: int) -> void:
	if Constants.is_server:
		return
	if what == NOTIFICATION_APPLICATION_RESUMED:
		ClientBackendManager.send_sync_request()


func _on_restore_button_pressed() -> void:
	purchase_verifier_ui.start()
