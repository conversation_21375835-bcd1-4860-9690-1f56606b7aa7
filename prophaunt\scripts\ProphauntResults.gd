extends ProphauntServerState

var results_timer = 0.0
var results_sent = false
var results_ui: ProphauntResultsUI
var results#Set in server before set_state

func set_state():
	print("Set State to Prophaunt Results")
	server.state = Constants.ServerState.Results
	results_timer = 0.0
	results_sent = false

	# Initialize results UI if not already done
	if not results_ui and Constants.is_client():
		var results_ui_scene = preload("res://prophaunt/ui/ProphauntResultsUI.tscn")
		results_ui = results_ui_scene.instantiate()
		results_ui.name = "ResultsUI"
		add_child(results_ui)
		results_ui.next_round_requested.connect(_on_next_round_requested)
		results_ui.leave_game_requested.connect(_on_leave_game_requested)


func run(delta):
	if not server:
		return
	
	results_timer += delta
	
	# Send results to players if not already sent
	if not results_sent:
		send_final_results()
		results_sent = true
	
	# After showing results for 15 seconds, start next round
	if results_timer >= 15.0:
		start_next_round()


func send_final_results():
	"""Send final results to all players"""
	# Display results on server if UI is available
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		Constants.client.game_scene.prophaunt_final_results.rpc_id(key, results)


func start_next_round():
	"""Start the next round or end the game"""
	# Check if we should continue with more rounds
	if should_continue_game():
		# Reset for next round
		reset_for_next_round()
		server.set_state_to_prophaunt_lobby()
	else:
		# End the game session
		end_game_session()


func should_continue_game():
	"""Determine if the game should continue with more rounds"""
	# Continue if we have enough players and haven't reached max rounds
	var active_players = 0
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			active_players += 1
	
	return active_players >= 2  # Minimum players to continue


func reset_for_next_round():
	"""Reset game state for the next round"""
	# Clear team assignments (will be reassigned in lobby)
	server.props_team.clear()
	server.haunters_team.clear()
	
	# Reset player states
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		server.players_data[key]["prophaunt_hp"] = Constants.PROPHAUNT_PROP_DEFAULT_HP
		server.players_data[key]["prophaunt_state"] = Constants.ProphauntPlayerState.ALIVE
		server.players_data[key]["prophaunt_hex_cooldown"] = 0.0
		server.players_data[key]["prophaunt_sound_cooldown"] = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN
		server.players_data[key]["prophaunt_disguise"] = ""
		server.players_data[key]["prophaunt_team"] = -1


func end_game_session():
	"""End the current game session"""
	print("Ending Prophaunt game session")
	
	# Reset server state
	current_round = 1
	rounds_played_on_map = 0
	
	# Transition back to lobby or shutdown
	#server.set_state_to_prophaunt_lobby()
	Constants.kill_server(0)


# UI signal handlers
func _on_next_round_requested():
	"""Handle next round request from UI"""
	print("Next round requested")
	start_next_round()


func _on_leave_game_requested():
	"""Handle leave game request from UI"""
	print("Leave game requested")
	# Handle player leaving the game
	# This would typically disconnect the player
