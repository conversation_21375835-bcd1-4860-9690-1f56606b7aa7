extends ProphauntServerState

var results_timer = 0.0
var results_ui: ProphauntResultsUI
var results#Set in server before set_state

# HTTP request for sending results to backend
var http_request: HTTPRequest

func set_state():
	print("Set State to Prophaunt Results")
	server.state = Constants.ServerState.Results
	results_timer = -10000.0#Not finishing

	# Initialize HTTP request for backend communication
	if not http_request:
		http_request = HTTPRequest.new()
		http_request.request_completed.connect(_on_backend_request_completed)
		add_child(http_request)

	handle_results()
	send_results_to_backend()


func run(delta):
	if not Constants.is_server:
		return

	results_timer += delta

	# After showing results for 15 seconds, start next round
	if results_timer >= 5.0:
		end_game_session()


func handle_results():
	if Constants.is_client():
		return
	
	var player_stats = {}
	
	for round_data in results:
		var stats = round_data["player_stats"]
		for key in stats:
			var p_data = stats[key]
			if player_stats.has(key):
				player_stats[key]["score"] += p_data["score"]
			else:
				player_stats[key] = p_data.duplicate()
	
	for key in player_stats:
		if server.players.has(key):
			var p:Character = server.players[key]
			player_stats[key]["total_kill"] = p.prophaunt_player.total_kill_count
			var data = Constants.server.players_data
			if data.has(key):
				player_stats[key]["backend_id"] = data[key]["backend_id"]
		else:
			player_stats[key]["total_kill"] = 0
	
	for round_data in results:
		round_data.erase("player_stats")
	
	results = {
		"player_stats": player_stats,
		"rounds": results,
	}


func send_final_results_to_clients(data):
	"""Send final results to all players"""
	# Display results on server if UI is available
	var participants = data.get("game", {}).get("participants", [])
	for p_data in participants:
		var key = int(p_data["server_id"])
		if server.is_bot(key) or server.is_dc(key):
			continue
		var s_data = {
			"kills": p_data["kills"],
			"coin_earned": p_data["coin_earned"],
			"smart_earned": p_data["smart_earned"],
		}
		Constants.client.game_scene.prophaunt_final_results.rpc_id(key, s_data)


func end_game_session():
	"""End the current game session"""
	print("Ending Prophaunt game session")
	
	# Reset server state
	current_round = 1
	rounds_played_on_map = 0
	
	# Transition back to lobby or shutdown
	#server.set_state_to_prophaunt_lobby()
	Constants.kill_server(0)


func send_results_to_backend():
	"""Send game results to backend"""
	
	var url = Constants.BACKEND_URL + "/prophunt/end_game"
	
	var json = JSON.stringify(results)
	var headers = ["Content-Type: application/json", "Authorization: JWT " + Constants.BACKEND_TOKEN]

	print("Sending Prophaunt results to backend: ", url)
	http_request.cancel_request()
	http_request.request(url, headers, HTTPClient.METHOD_POST, json)


var req_counter = 0
func _on_backend_request_completed(_result: int, response_code: int, _headers: PackedStringArray, body: PackedByteArray):
	"""Handle backend request completion"""
	if response_code == 200:
		var json = JSON.parse_string(body.get_string_from_utf8())
		send_final_results_to_clients(json)
		print("Prophaunt results successfully sent to backend")
		results_timer = 0.0
	else:
		req_counter += 1
		if req_counter > 10:
			end_game_session()
		await Constants.wait_timer(1)
		send_results_to_backend()
