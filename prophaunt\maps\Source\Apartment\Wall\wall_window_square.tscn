[gd_scene load_steps=7 format=4 uid="uid://dcieii017it83"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_td8as"]

[sub_resource type="ArrayMesh" id="ArrayMesh_6jng1"]
_surfaces = [{
"aabb": AABB(1.73661, 0, -2, 0.4, 4.8, 4),
"format": 34359742465,
"index_count": 282,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYABwAIAAkABwAKAAgAAgAHAAkACQALAAIAAgALAAwAAAACAAwADAANAAAABwAFAAoADQAFAAAADgAKAAUADQAPAAUADwAOAAUABAAQABEABAASABAAAQAEABEAEQATAAEAEwAUAAEAAwABABQAFAAVAAMABAAGABIAFQAGAAMAFgASAAYAFQAXAAYAFwAWAAYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AHwAgAB4AIAAhAB4AGwAgAB8AHwAiABsAIgAaABsAIAAjACEAIgAjABoAIgAhACMAIgAkACEAIgAlACQAKAAmACcAJwApACgAKQAZACgAGQAqACgAKwAZACkAGQAYACoAKQAsACsAGAAtACoALAAtACsALAAqAC0ALAAuACoALAAvAC4AIAArAC0ALQAjACAAIwAtABgAGAAaACMAGQArACAAIAAbABkACgAlACIAJAAlAAoAIgAIAAoACgAOACQACAAiAB8AHwAJAAgACQAfAB0AHQALAAkACwAdABwAHAAMAAsADAAcAB4AHgANAAwAIQAkAA4ADQAeACEADgAPACEAIQAPAA0ALgAWABcAEgAWAC4AFwAqAC4ALgAvABIAKgAXABUAFQAoACoAKAAVABQAFAAmACgAEgAvACwALAAQABIAEQAQACwALAApABEAJgAUABMAEwARACkAEwAnACYAKQAnABMAAgADAAYABgAHAAIA"),
"lods": [1.29902, PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYAAgAHAAoABwAFAAoAAgAKAA0ADQAKAAUAAAACAA0ADQAFAAAABAASABQABAAGABIAFAASAAYAAQAEABQAFAAGAAMAAwABABQAGgAYABkAGQAbABoAHwAgABwAIAAkABwAGwAgAB8AIAAjACQAHwAiABsAIgAkACMAIgAaABsAIgAjABoAKQAZACYAGQAuACYAKwAZACkAGQAYAC4AKQAsACsAGAAtAC4ALAAtACsALAAuAC0AIAArAC0ALQAjACAAIwAtABgAGAAaACMAGQArACAAIAAbABkAJAAiAAoACgANACQAHAANAAoACgAfABwACgAiAB8ADQAcACQAEgAUAC4AFAApACYALAApABQAFAASACwALgAsABIAFAAmAC4AAgADAAYABgAHAAIA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 48,
"vertex_data": PackedByteArray("6xXrPwAAAAAAAADAwlcCQAAAAAAAAADA6xXrP5qZmUAAAADAwlcCQJqZmUAAAADAwlcCQAAAAAAAAABA6xXrP6kT0CYAAABAwlcCQJqZmUAAAABA6xXrP5qZmUAAAABA6xXrPwAAgEAAAIA/6xXrPwAAgEAAAIC/6xXrP5mZeUDNzIw/6xXrP5mZeUDNzIy/6xXrP2Zmpj/NzIy/6xXrP5qZmT8AAIC/6xXrP2Zmpj/NzIw/6xXrP5qZmT8AAIA/wlcCQJqZmT8AAIA/wlcCQJqZmT8AAIC/wlcCQGZmpj/NzIw/wlcCQGZmpj/NzIy/wlcCQJmZeUDNzIy/wlcCQAAAgEAAAIC/wlcCQJmZeUDNzIw/wlcCQAAAgEAAAIA/Kb4IQDIzc0BmZmY/Kb4IQDIzc0BmZma/HknePzIzc0BmZmY/HknePzIzc0BmZma/HkneP2Zmpj/NzIy/HkneP5mZeUDNzIy/HkneP5qZmT8AAIC/HknePwAAgEAAAIC/HknePzMzsz9mZma/HkneP5qZmT8AAIA/HknePwAAgEAAAIA/HknePzMzsz9mZmY/HkneP2Zmpj/NzIw/HkneP5mZeUDNzIw/Kb4IQJmZeUDNzIy/Kb4IQGZmpj/NzIy/Kb4IQAAAgEAAAIC/Kb4IQJqZmT8AAIC/Kb4IQAAAgEAAAIA/Kb4IQDMzsz9mZma/Kb4IQJqZmT8AAIA/Kb4IQDMzsz9mZmY/Kb4IQJmZeUDNzIw/Kb4IQGZmpj/NzIw/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_atcr7"]
resource_name = "WallWindowSquare_wall-window-square"
_surfaces = [{
"aabb": AABB(1.73661, 0, -2, 0.4, 4.8, 4),
"attribute_data": PackedByteArray("zGCdPpzQbT/MYJ0+nNBtP8xgnT4+f2w/zGCdPj5/bD/MYJ0+nNBtP8xgnT6c0G0/zGCdPj5/bD/MYJ0+Pn9sP8xgnT54t2w/zGCdPni3bD/MYJ0+Pn9sP8xgnT6Avmw/zGCdPj5/bD/MYJ0+gL5sP8xgnT49dW0/zGCdPpzQbT/MYJ0+RHxtP8xgnT6c0G0/zGCdPj11bT/MYJ0+RHxtP+cZnT4iXW0/5xmdPiJdbT/nGZ0+o7dtP+cZnT6YVW0/5xmdPqO3bT/nGZ0+mFVtP+cZnT6AkWw/5xmdPqBNbD/nGZ0+9olsP+cZnT6gTWw/5xmdPoCRbD/nGZ0+9olsP51snT4VpnA/nWydPhWmcD+dbJ0+FaZwP51snT4VpnA/nWydPoPJcT+dbJ0+bJpwP51snT4s1XE/nWydPsSOcD+dbJ0+271xP51snT4s1XE/nWydPhWmcD+dbJ0+xI5wP51snT4VpnA/nWydPtu9cT+dbJ0+g8lxP51snT5smnA/nWydPmyacD+dbJ0+g8lxP51snT7EjnA/nWydPizVcT+dbJ0+FaZwP51snT7EjnA/nWydPtu9cT+dbJ0+FaZwP51snT4s1XE/nWydPtu9cT+dbJ0+bJpwP51snT6DyXE/nWydPtu9cT+dbJ0+271xP51snT7bvXE/nWydPtu9cT+dbJ0+271xP51snT4VpnA/nWydPtu9cT+dbJ0+FaZwP51snT7bvXE/nWydPtu9cT+dbJ0+FaZwP51snT4VpnA/nWydPmyacD+dbJ0+xI5wP51snT5smnA/nWydPoPJcT+dbJ0+xI5wP51snT6DyXE/nWydPsSOcD+dbJ0+xI5wP51snT5smnA/nWydPmyacD+dbJ0+g8lxP51snT6DyXE/nWydPizVcT+dbJ0+LNVxP51snT4s1XE/nWydPizVcT+dbJ0+bJpwP51snT7EjnA/nWydPmyacD+dbJ0+g8lxP51snT7EjnA/nWydPoPJcT+dbJ0+xI5wP51snT7EjnA/nWydPmyacD+dbJ0+bJpwP51snT4s1XE/nWydPizVcT+dbJ0+LNVxP51snT4s1XE/nWydPoPJcT+dbJ0+g8lxP15inD7iPXE/XmKcPuI9cT9eYpw+4j1xP15inD7iPXE/nWydPsSOcD+dbJ0+xI5wP51snT5smnA/nWydPmyacD+dbJ0+g8lxP51snT6DyXE/nWydPoPJcT+dbJ0+LNVxP51snT5smnA/nWydPmyacD+dbJ0+g8lxP51snT6DyXE/nWydPmyacD+dbJ0+bJpwP51snT4s1XE/nWydPizVcT8="),
"format": 34359742487,
"index_count": 282,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACgALAAgADAAKAAkACQANAAwADAANAA4ADwAMAA4ADgAQAA8ACgARAAsAEAARAA8AEgALABEAEAATABEAEwASABEAFgAUABUAFgAXABQAGAAWABUAFQAZABgAGQAaABgAGwAYABoAGgAcABsAFgAdABcAHAAdABsAHgAXAB0AHAAfAB0AHwAeAB0AIgAgACEAIQAjACIAJgAkACUAJQAnACYAJwAoACYAKAApACYAKgAoACcAJwArACoAKwAsACoAKAAtACkAKwAtACwAKwApAC0AKwAuACkAKwAvAC4AMgAwADEAMQAzADIAMwA0ADIANAA1ADIANgA0ADMANAA3ADUAMwA4ADYANwA5ADUAOAA5ADYAOAA1ADkAOAA6ADUAOAA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASwBIAEoASQBMAEoASgBNAEsATABJAE4ATgBPAEwATwBOAFAAUABRAE8AUQBQAFIAUgBTAFEAUwBSAFQAVABVAFMAVgBLAE0AVQBUAFYATQBXAFYAVgBXAFUAWgBYAFkAWwBYAFoAWQBcAFoAWgBdAFsAXABZAF4AXgBfAFwAXwBeAGAAYABhAF8AWwBdAGIAYgBjAFsAZABjAGIAYgBlAGQAYQBgAGYAZgBkAGUAZgBnAGEAZQBnAGYAagBoAGkAaQBrAGoA"),
"lods": [1.29902, PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYADAAKAAsACgARAAsADAALABAAEAALABEADwAMABAAEAARAA8AFgAXABoAFgAdABcAGgAXAB0AGAAWABoAGgAdABsAGwAYABoAIgAgACEAIQAjACIAJwAoACQAKAAuACQAKgAoACcAKAAtAC4AJwArACoAKwAuAC0AKwAsACoAKwAtACwAMwA0ADAANAA6ADAANgA0ADMANAA3ADoAMwA4ADYANwA5ADoAOAA5ADYAOAA6ADkAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYAcABsAG4ASgBVAEsAUgBVAEoASgBOAFIAbwBtAE4AcwByAHEAdgBgAFoAYAB7AGEAYgBlAGAAYABbAGIAdAB6AHcAeAB5AHUAagBoAGkAaQBrAGoA")],
"material": ExtResource("1_td8as"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 124,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_6jng1")

[sub_resource type="BoxShape3D" id="BoxShape3D_qiaeg"]
size = Vector3(0.311475, 1.39581, 4)

[sub_resource type="BoxShape3D" id="BoxShape3D_c7f4e"]
size = Vector3(0.311475, 1, 4.8)

[sub_resource type="BoxShape3D" id="BoxShape3D_3pgvu"]
size = Vector3(0.311475, 1.0166, 4)

[node name="WallWindowSquare" type="MeshInstance3D"]
transform = Transform3D(-1.19209e-07, 0, -1, 0, 1, 0, 1, 0, -1.19209e-07, 0, 0, 0)
mesh = SubResource("ArrayMesh_atcr7")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.90681, 0.672943, -2.38418e-07)
shape = SubResource("BoxShape3D_qiaeg")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, -1.19209e-07, -1.19209e-07, 1.19209e-07, -4.37114e-08, 1, -1.19209e-07, -1, -4.37114e-08, 1.90681, 2.37294, -1.49791)
shape = SubResource("BoxShape3D_c7f4e")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, -1.19209e-07, -1.19209e-07, 1.19209e-07, -4.37114e-08, 1, -1.19209e-07, -1, -4.37114e-08, 1.90681, 2.37294, 1.50209)
shape = SubResource("BoxShape3D_c7f4e")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.90681, 4.30665, -2.38418e-07)
shape = SubResource("BoxShape3D_3pgvu")
