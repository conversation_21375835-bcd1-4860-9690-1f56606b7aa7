[gd_scene load_steps=5 format=3 uid="uid://cwf7qdbn87n4t"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_5k0g0"]
[ext_resource type="PackedScene" uid="uid://djboohlk525ya" path="res://Scenes/FreeRide/Assets/Buildings/ToolShop/Sources/hammer.tscn" id="2_3hdnq"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.0133909
height = 0.155969

[sub_resource type="BoxShape3D" id="BoxShape3D_fhvsn"]
size = Vector3(0.0831185, 0.042099, 0.0518647)

[node name="HammerProp" instance=ExtResource("1_5k0g0")]

[node name="Hammer" parent="Meshes" index="0" instance=ExtResource("2_3hdnq")]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0.100524, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1.91069e-15, 4.37114e-08, 1, 0.0126708, 0.99992, -4.37079e-08, -0.99992, 0.0126708, -5.53855e-10, -0.00170118, 0.0786348, 0.000817407)
shape = SubResource("CapsuleShape3D_5q4rx")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, -0.00385853, 0.111928, 0.00133646)
shape = SubResource("BoxShape3D_fhvsn")
