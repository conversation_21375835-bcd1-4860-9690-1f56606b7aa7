[gd_scene load_steps=5 format=3 uid="uid://dlk26gfdn1hkq"]

[ext_resource type="PackedScene" uid="uid://cy82bsxfj1ddv" path="res://prophaunt/scenes/PropObject.tscn" id="1_5l0e2"]
[ext_resource type="PackedScene" uid="uid://dlx48uf0od0ud" path="res://Scenes/FreeRide/Assets/Buildings/ToolShop/Sources/tool_pickaxe.tscn" id="2_pkfh7"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_5q4rx"]
radius = 0.0184349
height = 0.240971

[sub_resource type="BoxShape3D" id="BoxShape3D_fhvsn"]
size = Vector3(0.178983, 0.042099, 0.0403099)

[node name="ToolPickaxerProp" instance=ExtResource("1_5l0e2")]

[node name="ToolPickaxe" parent="Meshes" index="0" instance=ExtResource("2_pkfh7")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D" index="0"]
transform = Transform3D(1.91069e-15, 4.37114e-08, 1, 0.0126708, 0.99992, -4.37079e-08, -0.99992, 0.0126708, -5.53855e-10, -0.000400431, 0.119609, 0.000817407)
shape = SubResource("CapsuleShape3D_5q4rx")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D" index="1"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, -0.000576464, 0.219619, 0.00133646)
shape = SubResource("BoxShape3D_fhvsn")
