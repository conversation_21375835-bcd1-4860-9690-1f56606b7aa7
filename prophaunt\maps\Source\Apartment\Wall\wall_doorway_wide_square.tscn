[gd_scene load_steps=6 format=4 uid="uid://cul41cpf42hs5"]

[ext_resource type="Material" uid="uid://b6gd8jjb1kopc" path="res://prophaunt/Mat/Colormap2.tres" id="1_e761b"]

[sub_resource type="ArrayMesh" id="ArrayMesh_dt7vw"]
_surfaces = [{
"aabb": AABB(-0.203273, -1.37163e-14, -4, 0.400001, 4.8, 8),
"format": 34359742465,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABgADAAUABgAFAAcACAAGAAcABwAJAAgACgAGAAgACAALAAoACwAMAA0ADQAKAAsADQAOAAoADwAKAA4ADwAOABAADwAQABEAEgAPABEAEQATABIAEgACAAMAAwAPABIAAwAGAAoACgAPAAMAFAAJAAcABwAVABQAFgAVAAcABwAFABYAFwAWAAUABQAEABcAFwAEAAEAAQAYABcAGAABAAAAAAAZABgAGwAaABMAEwARABsAGwARABAAEAAcABsAHQAcABAAEAAOAB0AHQAOAA0ADQAeAB0AHgANAAwADAAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAHgAfACcAJwAlAB4AJQAiAB4AGwAeACIAGwAdAB4AGwAcAB0AGgAbACIAIgAjABoAGAAZACEAIQAgABgAIAAkABgAFQAYACQAFQAXABgAFQAWABcAFQAkACYAJgAUABUAJQAkACAAIAAiACUA"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray("IBnGPWb3IqhnZkbAIBnGPZmZiUBnZkbAMBnGPVjTzCYAAIDAMBnGPZqZmUAAAIDAIBnGPc3MjEABAEDAwBjGPc3MjEADAEBAsBjGPZqZmUACAIBAwBjGPZmZiUBpZkZAsBjGPagT0CYCAIBAwBjGPavPJahpZkZAAIHTvZqZmUACAIBAAIHTvagT0CYCAIBAAIHTvVAMAqZpZkZAAIHTvZmZiUBpZkZA4IDTvc3MjEADAEBAgIDTvZqZmUAAAIDAgIDTvc3MjEABAEDAgIDTvZmZiUBnZkbAgIDTvagT0CYAAIDAgIDTvSAW6qVnZkbAyHJJPlkXd6hpZkZAyHJJPpmZiUBpZkZAyHJJPs3MjEADAEBA+HJJPs3MjEABAEDA+HJJPpmZiUBnZkbA+HJJPryWcKhnZkbAsCZQvqgT0CZnZkbAsCZQvpmZiUBnZkbAsCZQvs3MjEABAEDA4CZQvs3MjEADAEBA4CZQvpmZiUBpZkZA4CZQvqgT0CZpZkZA+HJJPmZmhkCZmTnA+HJJPryWcKiZmTnAsCZQvmZmhkCZmTnAsCZQvqgT0CaZmTnAyHJJPmZmhkCbmTlA4CZQvmZmhkCbmTlAyHJJPryWcKibmTlA4CZQvqgT0CabmTlA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ubfw0"]
resource_name = "WallDoorwayWideSquare_wall-doorway-wide-square"
_surfaces = [{
"aabb": AABB(-0.203273, -1.37163e-14, -4, 0.400001, 4.8, 8),
"attribute_data": PackedByteArray("ZvqkPlLRbT9m+qQ+JGZsP2b6pD5S0W0/ZvqkPuk7bD9m+qQ+sl1sP2b6pD6yXWw/ZvqkPuk7bD9m+qQ+JGZsP2b6pD5S0W0/ZvqkPlLRbT8rS6U+cTtwPytLpT7a0HE/K0ulPnE7cD8rS6U+2tBxP/n+pD6Q0m0/+f6kPmNnbD/5/qQ+kNJtP/n+pD4oPWw/+f6kPvFebD/5/qQ+KD1sP/n+pD7xXmw/+f6kPmNnbD/5/qQ+kNJtP/n+pD6Q0m0/K0ulPtrQcT8rS6U+cTtwPytLpT7a0HE/K0ulPnE7cD8JUaU+HmFxPwlRpT4eYXE/CVGlPh5hcT8JUaU+HmFxP+bvpD5nwXE/5u+kPvNxcD/m76Q+Z8FxP+bvpD7zcXA/5u+kPiZqcD/m76Q+JmpwP+bvpD4manA/5u+kPiZqcD/m76Q+83FwP+bvpD7zcXA/5u+kPmfBcT/m76Q+Z8FxP+bvpD5nwXE/5u+kPmfBcT/m76Q+83FwP+bvpD7zcXA/5u+kPiZqcD/m76Q+JmpwP+bvpD4manA/5u+kPiZqcD/m76Q+83FwP+bvpD7zcXA/5u+kPmfBcT/m76Q+Z8FxP+bvpD7AeXA/5u+kPmfBcT/m76Q+wHlwP+bvpD5nwXE/5u+kPsB5cD/m76Q+wHlwP+bvpD5nwXE/5u+kPmfBcT/m76Q+Z8FxP+bvpD5nwXE/5u+kPvNxcD/m76Q+wHlwP+bvpD7AeXA/5u+kPvNxcD/m76Q+JmpwP+bvpD4manA/5u+kPmfBcT/m76Q+Z8FxP+bvpD5nwXE/5u+kPmfBcT/m76Q+83FwP+bvpD7AeXA/5u+kPsB5cD/m76Q+83FwP+bvpD4manA/5u+kPiZqcD/m76Q+Z8FxP+bvpD5nwXE/5u+kPsB5cD/m76Q+wHlwP+bvpD7AeXA/5u+kPsB5cD8="),
"format": 34359742487,
"index_count": 192,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABgADAAUABgAFAAcACAAGAAcABwAJAAgADAAKAAsACwANAAwAEAAOAA8ADwARABAADwASABEAEwARABIAEwASABQAEwAUABUAFgATABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJAAjACEAIQAlACQAJgAkACUAJQAnACYAJgAnACgAKAApACYAKQAoACoAKgArACkALgAsAC0ALQAvAC4ALgAvADAAMAAxAC4AMgAxADAAMAAzADIAMgAzADQANAA1ADIANQA0ADYANgA3ADUAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIAQwBEAEIARQBCAEQARQBGAEIARQBHAEYASABFAEQARABJAEgATABKAEsASwBNAEwATQBOAEwATwBMAE4ATwBQAEwATwBRAFAATwBOAFIAUgBTAE8AVgBUAFUAVQBXAFYA"),
"material": ExtResource("1_e761b"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 88,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_dt7vw")

[sub_resource type="BoxShape3D" id="BoxShape3D_6p3r7"]
size = Vector3(0.4, 4.8, 1.1)

[sub_resource type="BoxShape3D" id="BoxShape3D_i7b7w"]
size = Vector3(0.4, 7.99938, 0.621558)

[node name="WallDoorwayWideSquare" type="MeshInstance3D"]
transform = Transform3D(-1.19209e-07, 0, -1, 0, 1, 0, 1, 0, -1.19209e-07, 0, 0, 0)
mesh = SubResource("ArrayMesh_ubfw0")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
collision_layer = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4.32133e-07, 2.4, -3.45)
shape = SubResource("BoxShape3D_6p3r7")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.90409e-07, 2.4, 3.45)
shape = SubResource("BoxShape3D_6p3r7")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, -1.19209e-07, -1.19209e-07, 1.19209e-07, -4.37114e-08, 1, -1.19209e-07, -1, -4.37114e-08, 1.50974e-08, 4.50775, 0.00939834)
shape = SubResource("BoxShape3D_i7b7w")
